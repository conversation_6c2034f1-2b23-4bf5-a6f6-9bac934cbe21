"use client";

import type React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { createRoot } from "react-dom/client";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Textarea } from "../ui/textarea";

// Types
interface WidgetConfig {
	websiteId: string;
	primaryColor?: string;
	secondaryColor?: string;
	position?: "bottom-right" | "bottom-left";
	welcomeMessage?: string;
	headerText?: string;
	initiallyOpen?: boolean;
	baseUrl?: string;
	visitorId?: string;
}

interface WidgetProps {
	config: WidgetConfig;
	shadowRoot: ShadowRoot | HTMLElement;
}

interface Message {
	id: string;
	content: string;
	role: "user" | "assistant";
	timestamp: Date;
}

// Styles as JavaScript objects for complete isolation
const styles = {
	container: {
		position: "fixed" as const,
		bottom: "20px",
		right: "20px",
		zIndex: 2147483647,
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Se<PERSON>e UI", Robot<PERSON>, sans-serif',
		fontSize: "14px",
		lineHeight: "1.5",
		color: "#333",
		pointerEvents: "auto" as const,
	},

	containerLeft: {
		left: "20px",
		right: "auto",
	},

	bubble: {
		width: "60px",
		height: "60px",
		borderRadius: "50%",
		border: "none",
		cursor: "pointer",
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
		transition: "all 0.3s ease",
		outline: "none",
		position: "relative" as const,
	},

	bubbleHover: {
		transform: "scale(1.05)",
		boxShadow: "0 6px 16px rgba(0, 0, 0, 0.2)",
	},

	chatWindow: {
		position: "absolute" as const,
		bottom: "70px",
		right: "0",
		width: "360px",
		height: "600px",
		maxHeight: "calc(100vh - 100px)",
		backgroundColor: "#ffffff",
		borderRadius: "12px",
		boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
		border: "1px solid #e5e7eb",
		display: "flex",
		flexDirection: "column" as const,
		overflow: "hidden",
		transform: "translateY(10px)",
		opacity: 0,
		transition: "all 0.3s ease",
		pointerEvents: "auto" as const,
	},

	chatWindowLeft: {
		left: "0",
		right: "auto",
	},

	chatWindowOpen: {
		transform: "translateY(0)",
		opacity: 1,
	},

	header: {
		padding: "16px 20px",
		borderBottom: "1px solid #e5e7eb",
		display: "flex",
		alignItems: "center",
		justifyContent: "space-between",
		backgroundColor: "#ffffff",
	},

	headerTitle: {
		margin: 0,
		fontSize: "16px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		fontWeight: "600",
		color: "#111827",
	},

	closeButton: {
		background: "none",
		border: "none",
		cursor: "pointer",
		padding: "4px",
		borderRadius: "4px",
		color: "#6b7280",
		fontSize: "18px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		lineHeight: 1,
		outline: "none",
	},

	messagesContainer: {
		flex: 1,
		padding: "16px",
		overflowY: "auto" as const,
		display: "flex",
		flexDirection: "column" as const,
		gap: "12px",
		// Custom scrollbar will be applied via CSS class
	},

	message: {
		maxWidth: "80%",
		padding: "8px 12px",
		borderRadius: "12px",
		fontSize: "14px",
		lineHeight: "1.4",
	},

	userMessage: {
		alignSelf: "flex-end" as const,
		backgroundColor: "#3b82f6",
		color: "#ffffff",
	},

	assistantMessage: {
		alignSelf: "flex-start" as const,
		backgroundColor: "#f3f4f6",
		color: "#111827",
	},

	inputContainer: {
		padding: "16px",
		borderTop: "1px solid #e5e7eb",
		display: "flex",
		gap: "8px",
		backgroundColor: "#ffffff",
		color: "#111827 !important",
	},

	input: {
		flex: 1,
		padding: "8px 12px",
		border: "1px solid #d1d5db",
		borderRadius: "8px",
		fontSize: "14px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		outline: "none",
		resize: "none" as const,
		minHeight: "36px",
		maxHeight: "100px",
		backgroundColor: "#ffffff",
	},

	sendButton: {
		padding: "8px 16px",
		border: "none",
		borderRadius: "8px",
		cursor: "pointer",
		fontSize: "14px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		fontWeight: "500",
		outline: "none",
		transition: "all 0.2s ease",
	},

	sendButtonDisabled: {
		opacity: 0.5,
		cursor: "not-allowed",
	},
	brandingFooter: {
		padding: "8px 16px",
		textAlign: "center" as const,
		borderTop: "1px solid #f3f4f6",
		backgroundColor: "#fafafa",
	},
	brandingText: {
		fontSize: "11px",
		fontFamily:
			'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
		color: "#9ca3af",
		margin: 0,
	},
	brandingLink: {
		color: "#6b7280",
		textDecoration: "none",
		fontWeight: "500",
		transition: "color 0.2s ease",
	},

	// Markdown styles
	markdownContent: {
		lineHeight: "1.6",
	},

	markdownH1: {
		fontSize: "18px",
		fontWeight: "700",
		margin: "16px 0 8px 0",
		color: "inherit",
	},

	markdownH2: {
		fontSize: "16px",
		fontWeight: "600",
		margin: "14px 0 6px 0",
		color: "inherit",
	},

	markdownH3: {
		fontSize: "15px",
		fontWeight: "600",
		margin: "12px 0 4px 0",
		color: "inherit",
	},

	markdownP: {
		margin: "8px 0",
		color: "inherit",
	},

	markdownUl: {
		margin: "8px 0",
		paddingLeft: "20px",
		color: "inherit",
	},

	markdownOl: {
		margin: "8px 0",
		paddingLeft: "20px",
		color: "inherit",
	},

	markdownLi: {
		margin: "4px 0",
		color: "inherit",
	},

	markdownCode: {
		backgroundColor: "rgba(0, 0, 0, 0.1)",
		padding: "2px 4px",
		borderRadius: "3px",
		fontSize: "13px",
		fontFamily: 'Monaco, Consolas, "Courier New", monospace',
		color: "inherit",
	},

	markdownPre: {
		backgroundColor: "rgba(0, 0, 0, 0.1)",
		padding: "12px",
		borderRadius: "6px",
		overflow: "auto",
		margin: "8px 0",
		fontSize: "13px",
		fontFamily: 'Monaco, Consolas, "Courier New", monospace',
		color: "inherit",
	},

	markdownBlockquote: {
		borderLeft: "3px solid #ddd",
		paddingLeft: "12px",
		margin: "8px 0",
		fontStyle: "italic",
		color: "inherit",
	},

	markdownA: {
		color: "#3b82f6",
		textDecoration: "underline",
	},

	markdownStrong: {
		fontWeight: "600",
		color: "inherit",
	},

	markdownEm: {
		fontStyle: "italic",
		color: "inherit",
	},
};

// Markdown Renderer Component
function MarkdownRenderer({
	content,
	primaryColor,
}: { content: string; primaryColor: string }) {
	return (
		<div style={styles.markdownContent}>
			<ReactMarkdown
				remarkPlugins={[remarkGfm]}
				components={{
					// Headings
					h1: ({ children }) => (
						<h1
							style={{
								fontSize: "18px",
								fontWeight: 700,
								margin: "16px 0 8px 0",
								color: "inherit",
							}}
						>
							{children}
						</h1>
					),
					h2: ({ children }) => (
						<h2
							style={{
								fontSize: "16px",
								fontWeight: 600,
								margin: "14px 0 6px 0",
								color: "inherit",
							}}
						>
							{children}
						</h2>
					),
					h3: ({ children }) => (
						<h3
							style={{
								fontSize: "15px",
								fontWeight: 600,
								margin: "12px 0 4px 0",
								color: "inherit",
							}}
						>
							{children}
						</h3>
					),
					// Paragraphs and text
					p: ({ children }) => (
						<p style={{ margin: "8px 0", color: "inherit" }}>{children}</p>
					),
					// Lists
					ul: ({ children }) => (
						<ul
							style={{
								margin: "8px 0",
								paddingLeft: "20px",
								color: "inherit",
							}}
						>
							{children}
						</ul>
					),
					ol: ({ children }) => (
						<ol
							style={{
								margin: "8px 0",
								paddingLeft: "20px",
								color: "inherit",
							}}
						>
							{children}
						</ol>
					),
					li: ({ children }) => (
						<li style={{ margin: "4px 0", color: "inherit" }}>{children}</li>
					),
					// Code
					code: ({ children, className }) => {
						const isInline = !className;
						if (isInline) {
							return (
								<code
									style={{
										backgroundColor: "rgba(0, 0, 0, 0.1)",
										padding: "2px 4px",
										borderRadius: "3px",
										fontSize: "13px",
										fontFamily: "Monaco, Consolas, 'Courier New', monospace",
										color: "inherit",
									}}
								>
									{children}
								</code>
							);
						}
						return (
							<code
								style={{
									backgroundColor: "rgba(0, 0, 0, 0.1)",
									padding: "12px",
									borderRadius: "6px",
									overflow: "auto",
									margin: "8px 0",
									fontSize: "13px",
									fontFamily: "Monaco, Consolas, 'Courier New', monospace",
									color: "inherit",
									display: "block",
								}}
							>
								{children}
							</code>
						);
					},
					pre: ({ children }) => (
						<pre
							style={{
								backgroundColor: "rgba(0, 0, 0, 0.1)",
								padding: "12px",
								borderRadius: "6px",
								overflow: "auto",
								margin: "8px 0",
								fontSize: "13px",
								fontFamily: "Monaco, Consolas, 'Courier New', monospace",
								color: "inherit",
							}}
						>
							{children}
						</pre>
					),
					// Blockquotes
					blockquote: ({ children }) => (
						<blockquote
							style={{
								borderLeft: "3px solid #ddd",
								paddingLeft: "12px",
								margin: "8px 0",
								fontStyle: "italic",
								color: "inherit",
							}}
						>
							{children}
						</blockquote>
					),
					// Links
					a: ({ children, href }) => (
						<a
							href={href}
							style={{
								color: primaryColor,
								textDecoration: "underline",
							}}
							target="_blank"
							rel="noopener noreferrer"
						>
							{children}
						</a>
					),
					// Text formatting
					strong: ({ children }) => (
						<strong style={{ fontWeight: 600, color: "inherit" }}>
							{children}
						</strong>
					),
					em: ({ children }) => (
						<em style={{ fontStyle: "italic", color: "inherit" }}>
							{children}
						</em>
					),
				}}
			>
				{content}
			</ReactMarkdown>
		</div>
	);
}

// Mobile responsive styles function
function createMobileStyles() {
	return `
		/* Mobile responsive styles */
		@media (max-width: 640px) {
			.chat-widget-container {
				position: fixed !important;
				bottom: 10px !important;
				left: 10px !important;
				right: 10px !important;
				width: auto !important;
			}

			.chat-widget-bubble {
				width: 56px !important;
				height: 56px !important;
				position: fixed !important;
				bottom: 20px !important;
				right: 20px !important;
			}

			.chat-widget-window {
				position: fixed !important;
				bottom: 0 !important;
				left: 0 !important;
				right: 0 !important;
				top: auto !important;
				width: 100vw !important;
				height: 100vh !important;
				max-height: 100vh !important;
				border-radius: 0 !important;
				border: none !important;
				box-shadow: none !important;
			}

			.chat-widget-header {
				padding: 16px 20px !important;
				min-height: 60px !important;
			}

			.chat-widget-close-button {
				padding: 8px !important;
				font-size: 20px !important;
				min-width: 44px !important;
				min-height: 44px !important;
			}

			.chat-widget-messages {
				padding: 12px 16px !important;
			}

			.chat-widget-message {
				max-width: 85% !important;
				font-size: 16px !important;
				padding: 12px 16px !important;
			}

			.chat-widget-input-container {
				padding: 16px !important;
				gap: 12px !important;
			}

			.chat-widget-input {
				font-size: 16px !important;
				padding: 12px 16px !important;
				min-height: 44px !important;
			}

			.chat-widget-send-button {
				padding: 12px 20px !important;
				font-size: 16px !important;
				min-height: 44px !important;
				min-width: 80px !important;
			}

			.chat-widget-branding {
				padding: 12px 16px !important;
			}
		}

		/* Small mobile devices (320px and up) */
		@media (max-width: 375px) {
			.chat-widget-input-container {
				flex-direction: column !important;
				gap: 8px !important;
			}

			.chat-widget-send-button {
				width: 100% !important;
			}

			.chat-widget-message {
				max-width: 90% !important;
				font-size: 15px !important;
			}
		}

		/* Tablet landscape and small desktop */
		@media (min-width: 641px) and (max-width: 1024px) {
			.chat-widget-window {
				width: 380px !important;
				height: 650px !important;
			}
		}
	`;
}

// Custom scrollbar styles function
function createScrollbarStyles(primaryColor: string) {
	// Convert hex to rgba for transparency effects
	const hexToRgba = (hex: string, alpha: number) => {
		// Handle both #RGB and #RRGGBB formats
		const cleanHex = hex.replace("#", "");
		let r: number;
		let g: number;
		let b: number;

		if (cleanHex.length === 3) {
			r = Number.parseInt(cleanHex[0] + cleanHex[0], 16);
			g = Number.parseInt(cleanHex[1] + cleanHex[1], 16);
			b = Number.parseInt(cleanHex[2] + cleanHex[2], 16);
		} else {
			r = Number.parseInt(cleanHex.slice(0, 2), 16);
			g = Number.parseInt(cleanHex.slice(2, 4), 16);
			b = Number.parseInt(cleanHex.slice(4, 6), 16);
		}

		return `rgba(${r}, ${g}, ${b}, ${alpha})`;
	};

	const scrollbarTrackColor = hexToRgba(primaryColor, 0.08);
	const scrollbarThumbColor = hexToRgba(primaryColor, 0.25);
	const scrollbarThumbHoverColor = hexToRgba(primaryColor, 0.4);
	const scrollbarThumbActiveColor = hexToRgba(primaryColor, 0.6);

	return `
		/* WebKit browsers (Chrome, Safari, Edge) */
		.chat-messages-container::-webkit-scrollbar {
			width: 6px;
			background: transparent;
		}

		.chat-messages-container::-webkit-scrollbar-track {
			background: ${scrollbarTrackColor};
			border-radius: 3px;
			margin: 2px 0;
		}

		.chat-messages-container::-webkit-scrollbar-thumb {
			background: ${scrollbarThumbColor};
			border-radius: 3px;
			transition: all 0.2s ease;
			min-height: 20px;
		}

		.chat-messages-container::-webkit-scrollbar-thumb:hover {
			background: ${scrollbarThumbHoverColor};
		}

		.chat-messages-container::-webkit-scrollbar-thumb:active {
			background: ${scrollbarThumbActiveColor};
		}

		/* Hide scrollbar when not needed */
		.chat-messages-container::-webkit-scrollbar-corner {
			background: transparent;
		}

		/* Firefox */
		.chat-messages-container {
			scrollbar-width: thin;
			scrollbar-color: ${scrollbarThumbColor} ${scrollbarTrackColor};
		}

		/* Ensure smooth scrolling and proper behavior */
		.chat-messages-container {
			scroll-behavior: smooth;
			overflow-x: hidden;
			overflow-y: auto;
		}

		/* Hide scrollbar on mobile devices where it's not needed */
		@media (max-width: 640px) {
			.chat-messages-container::-webkit-scrollbar {
				width: 4px;
			}
		}

		/* Ensure scrollbar doesn't interfere with content */
		.chat-messages-container {
			scrollbar-gutter: stable;
		}

		/* Add subtle fade effect for better visual integration */
		.chat-messages-container::-webkit-scrollbar-thumb {
			box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
		}

		/* Improve accessibility - ensure scrollbar is visible for keyboard navigation */
		.chat-messages-container:focus-within::-webkit-scrollbar-thumb {
			background: ${scrollbarThumbHoverColor};
		}
	`;
}

// Widget Component
function WidgetV2({ config, shadowRoot }: WidgetProps) {
	const [isOpen, setIsOpen] = useState(config.initiallyOpen || false);
	const [messages, setMessages] = useState<Message[]>([]);
	const [inputValue, setInputValue] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const [isHovered, setIsHovered] = useState(false);
	const messagesEndRef = useRef<HTMLDivElement>(null);

	const primaryColor = config.primaryColor || "#3b82f6";
	const secondaryColor = config.secondaryColor || "#ffffff";
	const position = config.position || "bottom-right";

	// Calculate text colors based on background colors for better contrast
	const getTextColor = (backgroundColor: string) => {
		// Simple function to determine if we should use light or dark text
		// based on the background color brightness
		const hex = backgroundColor.replace("#", "");
		const r = Number.parseInt(hex.substring(0, 2), 16);
		const g = Number.parseInt(hex.substring(2, 4), 16);
		const b = Number.parseInt(hex.substring(4, 6), 16);
		const brightness = (r * 299 + g * 587 + b * 114) / 1000;
		return brightness > 128 ? "#111827" : "#ffffff";
	};

	const headerTextColor = getTextColor(secondaryColor);
	const assistantTextColor = getTextColor("#f3f4f6"); // Assistant message background

	// Inject custom styles into Shadow DOM
	useEffect(() => {
		if (shadowRoot) {
			// Remove existing styles if they exist
			const existingScrollbarStyle = shadowRoot.querySelector(
				"#chat-scrollbar-styles",
			);
			const existingMobileStyle = shadowRoot.querySelector(
				"#chat-mobile-styles",
			);
			if (existingScrollbarStyle) {
				existingScrollbarStyle.remove();
			}
			if (existingMobileStyle) {
				existingMobileStyle.remove();
			}

			// Create and inject scrollbar styles
			const scrollbarStyleElement = document.createElement("style");
			scrollbarStyleElement.id = "chat-scrollbar-styles";
			scrollbarStyleElement.textContent = createScrollbarStyles(primaryColor);
			shadowRoot.appendChild(scrollbarStyleElement);

			// Create and inject mobile responsive styles
			const mobileStyleElement = document.createElement("style");
			mobileStyleElement.id = "chat-mobile-styles";
			mobileStyleElement.textContent = createMobileStyles();
			shadowRoot.appendChild(mobileStyleElement);
		}
	}, [shadowRoot, primaryColor]);

	// Scroll to bottom when messages change
	// biome-ignore lint/correctness/useExhaustiveDependencies: We want to scroll on any message change, not just length
	useEffect(() => {
		if (messagesEndRef.current) {
			messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
		}
	}, [messages]);

	// Add welcome message on first open
	useEffect(() => {
		if (isOpen && messages.length === 0 && config.welcomeMessage) {
			setMessages([
				{
					id: "welcome",
					content: config.welcomeMessage,
					role: "assistant",
					timestamp: new Date(),
				},
			]);
		}
	}, [isOpen, messages.length, config.welcomeMessage]);

	const handleSendMessage = useCallback(async () => {
		if (!inputValue.trim() || isLoading) return;

		const userMessage: Message = {
			id: Date.now().toString(),
			content: inputValue.trim(),
			role: "user",
			timestamp: new Date(),
		};

		setMessages((prev) => [...prev, userMessage]);
		setInputValue("");
		setIsLoading(true);

		// Create a placeholder assistant message that will be updated
		const assistantMessageId = (Date.now() + 1).toString();
		const assistantMessage: Message = {
			id: assistantMessageId,
			content: "",
			role: "assistant",
			timestamp: new Date(),
		};

		setMessages((prev) => [...prev, assistantMessage]);

		try {
			const response = await fetch(
				`${config.baseUrl}/api/chat/${config.websiteId}`,
				{
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						messages: [...messages, userMessage].map((m) => ({
							role: m.role,
							content: m.content,
						})),
						websiteId: config.websiteId,
						visitorId: config.visitorId,
					}),
				},
			);

			if (!response.ok) throw new Error("Failed to send message");

			// Handle Mastra streaming response
			const reader = response.body?.getReader();
			const decoder = new TextDecoder();
			let fullContent = "";

			if (reader) {
				while (true) {
					const { done, value } = await reader.read();
					if (done) break;

					const chunk = decoder.decode(value, { stream: true });
					const lines = chunk.split("\n");

					for (const line of lines) {
						if (line.startsWith("0:")) {
							// Text content from Mastra stream
							const content = line.slice(2);
							fullContent += content;

							// Hide "Thinking..." as soon as we get the first meaningful content
							if (isLoading && content.trim()) {
								setIsLoading(false);
							}

							// Clean the content to remove extra quotes and formatting issues
							const cleanContent = fullContent
								// First, convert literal \n strings to actual newlines
								.replace(/\\n/g, "\n") // Convert literal \n to actual newlines
								.replace(/\\r\\n/g, "\n") // Convert literal \r\n to actual newlines
								.replace(/\\r/g, "\n") // Convert literal \r to actual newlines
								// Handle the specific pattern you mentioned: "Hello""! I""'""m"" here to assist"" you."
								.replace(/([a-zA-Z])""/g, "$1") // Remove double quotes after letters
								.replace(/""([a-zA-Z])/g, "$1") // Remove double quotes before letters
								.replace(/""([!?.,;:])/g, "$1") // Remove double quotes before punctuation
								.replace(/([!?.,;:])""/g, "$1") // Remove double quotes after punctuation
								.replace(/""'/g, "'") // Fix quote-apostrophe patterns
								.replace(/'""/g, "'") // Fix apostrophe-quote patterns
								// Remove various quote patterns
								.replace(/"""/g, '"') // Triple quotes to single
								.replace(/""/g, "") // Remove double quotes entirely
								.replace(/\\"/g, '"') // Escaped quotes
								.replace(/\\""/g, '"') // Escaped double quotes
								.replace(/"{2,}/g, '"') // Multiple consecutive quotes
								// Handle JSON-like quote patterns
								.replace(/"([^"]*)""/g, '"$1"') // Fix quote-quote patterns
								.replace(/""([^"]*)""/g, '"$1"') // Fix double-quote wrapping
								.replace(/\\\\"/g, '"') // Double-escaped quotes
								// Handle specific patterns that might come from JSON stringification
								.replace(/"\s*"/g, '"') // Quotes with spaces
								// Clean up any remaining quote artifacts
								.replace(/([a-zA-Z])"([a-zA-Z])/g, "$1'$2") // Replace quotes between letters with apostrophes
								.replace(/\s+"/g, ' "') // Fix spacing before quotes
								.replace(/"\s+/g, '" ') // Fix spacing after quotes
								.trim()
								// Remove surrounding quotes from the entire message (after all other cleaning)
								// Handle multiple quotes at start/end and both single and double quotes
								.replace(/^["']+(.*)["']+$/, "$1") // Remove surrounding quotes (single or double, multiple)
								.replace(/^["']\s*([\s\S]*?)\s*["']$/, "$1") // Remove quotes with optional whitespace (multiline safe)
								// Handle cases where quotes might be at start or end only
								.replace(/^["']+\s*/, "") // Remove quotes at start with optional whitespace
								.replace(/\s*["']+$/, "") // Remove quotes at end with optional whitespace
								.trim();

							// Update the assistant message with cleaned accumulated content
							setMessages((prev) =>
								prev.map((msg) =>
									msg.id === assistantMessageId
										? { ...msg, content: cleanContent }
										: msg,
								),
							);
						}
					}
				}
			}

			// If no content was received, show error
			if (!fullContent) {
				setMessages((prev) =>
					prev.map((msg) =>
						msg.id === assistantMessageId
							? { ...msg, content: "Sorry, I encountered an error." }
							: msg,
					),
				);
			}
		} catch (error) {
			console.error("Error sending message:", error);

			// Update the assistant message with error content
			setMessages((prev) =>
				prev.map((msg) =>
					msg.id === assistantMessageId
						? {
								...msg,
								content: "Sorry, I encountered an error. Please try again.",
							}
						: msg,
				),
			);
		} finally {
			// Always ensure loading is false when done
			setIsLoading(false);
		}
	}, [inputValue, isLoading, messages, config]);

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			handleSendMessage();
		}
	};

	const toggleChat = () => {
		setIsOpen(!isOpen);
	};

	const closeChat = () => {
		setIsOpen(false);
	};

	return (
		<div
			className="chat-widget-container"
			style={{
				...styles.container,
				...(position === "bottom-left" ? styles.containerLeft : {}),
			}}
		>
			{/* Chat Window */}
			{isOpen && (
				<div
					className="chat-widget-window"
					style={{
						...styles.chatWindow,
						...(position === "bottom-left" ? styles.chatWindowLeft : {}),
						...styles.chatWindowOpen,
					}}
				>
					{/* Header */}
					<div
						className="chat-widget-header"
						style={{ ...styles.header, backgroundColor: secondaryColor }}
					>
						<h3 style={{ ...styles.headerTitle, color: headerTextColor }}>
							{config.headerText || "Chat Assistant"}
						</h3>
						<button
							type="button"
							className="chat-widget-close-button"
							style={{ ...styles.closeButton, color: headerTextColor }}
							onClick={closeChat}
							aria-label="Close chat"
						>
							×
						</button>
					</div>

					{/* Messages */}
					<div
						className="chat-messages-container chat-widget-messages"
						style={styles.messagesContainer}
					>
						{messages.map((message) => (
							<div
								key={message.id}
								className="chat-widget-message"
								style={{
									...styles.message,
									...(message.role === "user"
										? styles.userMessage
										: {
												...styles.assistantMessage,
												color: assistantTextColor,
											}),
									...(message.role === "user"
										? { backgroundColor: primaryColor }
										: {}),
								}}
							>
								{message.role === "assistant" ? (
									// Show "Thinking..." only if the message is empty and we're loading
									message.content.trim() === "" && isLoading ? (
										<div
											style={{ color: assistantTextColor, fontStyle: "italic" }}
										>
											Thinking...
										</div>
									) : (
										<MarkdownRenderer
											content={message.content}
											primaryColor={primaryColor}
										/>
									)
								) : (
									message.content
								)}
							</div>
						))}
						<div ref={messagesEndRef} />
					</div>

					{/* Input */}
					<div
						className="chat-widget-input-container"
						style={{
							...styles.inputContainer,
							backgroundColor: secondaryColor,
							color: headerTextColor,
						}}
					>
						<Textarea
							className="chat-widget-input"
							style={{
								...styles.input,
								backgroundColor: secondaryColor,
								color: headerTextColor,
							}}
							value={inputValue}
							onChange={(e) => setInputValue(e.target.value)}
							onKeyDown={handleKeyPress}
							placeholder="Type your message..."
							rows={1}
						/>
						<button
							type="button"
							className="chat-widget-send-button"
							style={{
								...styles.sendButton,
								backgroundColor: primaryColor,
								color: "#ffffff",
								...(!inputValue.trim() || isLoading
									? styles.sendButtonDisabled
									: {}),
							}}
							onClick={handleSendMessage}
							disabled={!inputValue.trim() || isLoading}
						>
							Send
						</button>
					</div>

					{/* Branding Footer */}
					<div className="chat-widget-branding" style={styles.brandingFooter}>
						<p style={styles.brandingText}>
							Powered by{" "}
							<a
								href="https://bublai.com"
								target="_blank"
								rel="noopener noreferrer"
								style={styles.brandingLink}
								aria-label="Visit Bubl website"
								onMouseEnter={(e) => {
									e.currentTarget.style.color = primaryColor;
								}}
								onMouseLeave={(e) => {
									e.currentTarget.style.color = "#6b7280";
								}}
							>
								Bubl
							</a>
						</p>
					</div>
				</div>
			)}

			{/* Bubble Button */}
			<button
				type="button"
				className="chat-widget-bubble"
				style={{
					...styles.bubble,
					backgroundColor: primaryColor,
					...(isHovered ? styles.bubbleHover : {}),
				}}
				onClick={toggleChat}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
				aria-label={isOpen ? "Close chat" : "Open chat"}
			>
				<svg
					width="24"
					height="24"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
					style={{ color: "#ffffff" }}
				>
					<title>{isOpen ? "Close chat" : "Open chat"}</title>
					{isOpen ? (
						<path d="M18 6L6 18M6 6l12 12" />
					) : (
						<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
					)}
				</svg>
			</button>
		</div>
	);
}

// Widget initialization function
export function initWidget({
	shadowRoot,
	config,
}: { shadowRoot: ShadowRoot | HTMLElement; config: WidgetConfig }) {
	// Create container in shadow root
	const container = document.createElement("div");
	shadowRoot.appendChild(container);

	// Create React root and render
	const root = createRoot(container);
	root.render(<WidgetV2 config={config} shadowRoot={shadowRoot} />);

	// Return widget instance with API methods
	return {
		open() {
			// Implementation would trigger state change
		},
		close() {
			// Implementation would trigger state change
		},
		toggle() {
			// Implementation would trigger state change
		},
		destroy() {
			root.unmount();
			if (container.parentNode) {
				container.parentNode.removeChild(container);
			}
		},
	};
}
