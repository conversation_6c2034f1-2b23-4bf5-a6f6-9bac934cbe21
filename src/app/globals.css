@import "tailwindcss";
@import "tw-animate-css";
@plugin '@tailwindcss/typography';
@tailwind utilities;

@custom-variant dark (&:is(.dark *));

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: Geist Mono, monospace;
	--font-mono: Geist Mono, monospace;
	--color-sidebar-ring: var(--sidebar-ring);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar: var(--sidebar);
	--color-chart-5: var(--chart-5);
	--color-chart-4: var(--chart-4);
	--color-chart-3: var(--chart-3);
	--color-chart-2: var(--chart-2);
	--color-chart-1: var(--chart-1);
	--color-ring: var(--ring);
	--color-input: var(--input);
	--color-border: var(--border);
	--color-destructive: var(--destructive);
	--color-accent-foreground: var(--accent-foreground);
	--color-accent: var(--accent);
	--color-muted-foreground: var(--muted-foreground);
	--color-muted: var(--muted);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-secondary: var(--secondary);
	--color-primary-foreground: var(--primary-foreground);
	--color-primary: var(--primary);
	--color-popover-foreground: var(--popover-foreground);
	--color-popover: var(--popover);
	--color-card-foreground: var(--card-foreground);
	--color-card: var(--card);
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--font-serif: Geist Mono, monospace;
	--radius: 0rem;
	--tracking-tighter: calc(var(--tracking-normal) - 0.05em);
	--tracking-tight: calc(var(--tracking-normal) - 0.025em);
	--tracking-wide: calc(var(--tracking-normal) + 0.025em);
	--tracking-wider: calc(var(--tracking-normal) + 0.05em);
	--tracking-widest: calc(var(--tracking-normal) + 0.1em);
	--tracking-normal: var(--tracking-normal);
	--shadow-2xl: var(--shadow-2xl);
	--shadow-xl: var(--shadow-xl);
	--shadow-lg: var(--shadow-lg);
	--shadow-md: var(--shadow-md);
	--shadow: var(--shadow);
	--shadow-sm: var(--shadow-sm);
	--shadow-xs: var(--shadow-xs);
	--shadow-2xs: var(--shadow-2xs);
	--spacing: var(--spacing);
	--letter-spacing: var(--letter-spacing);
	--shadow-offset-y: var(--shadow-offset-y);
	--shadow-offset-x: var(--shadow-offset-x);
	--shadow-spread: var(--shadow-spread);
	--shadow-blur: var(--shadow-blur);
	--shadow-opacity: var(--shadow-opacity);
	--color-shadow-color: var(--shadow-color);
	--color-destructive-foreground: var(--destructive-foreground);
}

:root {
	--radius: 0rem;
	/* Soft light theme colors */
	--background: oklch(1.0 0 0);
	--foreground: oklch(0.1448 0 0);
	--card: oklch(1.0 0 0);
	--card-foreground: oklch(0.1448 0 0);
	--popover: oklch(1.0 0 0);
	--popover-foreground: oklch(0.1448 0 0);

	/* Soft blue primary color */
	--primary: oklch(0.5555 0 0);
	--primary-foreground: oklch(0.9851 0 0);

	/* Soft secondary color */
	--secondary: oklch(0.9702 0 0);
	--secondary-foreground: oklch(0.2046 0 0);

	/* Muted colors */
	--muted: oklch(0.9702 0 0);
	--muted-foreground: oklch(0.5486 0 0);

	/* Accent colors */
	--accent: oklch(0.9702 0 0);
	--accent-foreground: oklch(0.2046 0 0);

	/* Destructive colors */
	--destructive: oklch(0.583 0.2387 28.4765);
	--destructive-foreground: oklch(0.9702 0 0);

	/* Border and input colors */
	--border: oklch(0.9219 0 0);
	--input: oklch(0.9219 0 0);
	--ring: oklch(0.709 0 0);

	/* Chart colors - soft pastels */
	--chart-1: oklch(0.5555 0 0); /* Soft blue */
	--chart-2: oklch(0.5555 0 0); /* Soft teal */
	--chart-3: oklch(0.5555 0 0); /* Soft amber */
	--chart-4: oklch(0.5555 0 0); /* Soft purple */
	--chart-5: oklch(0.5555 0 0); /* Soft cyan */

	/* Sidebar colors */
	--sidebar: oklch(0.9851 0 0);
	--sidebar-foreground: oklch(0.1448 0 0);
	--sidebar-primary: oklch(0.2046 0 0);
	--sidebar-primary-foreground: oklch(0.9851 0 0);
	--sidebar-accent: oklch(0.9702 0 0);
	--sidebar-accent-foreground: oklch(0.2046 0 0);
	--sidebar-border: oklch(0.9219 0 0);
	--sidebar-ring: oklch(0.709 0 0);
	--font-sans: Geist Mono, monospace;
	--font-serif: Geist Mono, monospace;
	--font-mono: Geist Mono, monospace;
	--shadow-color: hsl(0 0% 0%);
	--shadow-opacity: 0;
	--shadow-blur: 0px;
	--shadow-spread: 0px;
	--shadow-offset-x: 0px;
	--shadow-offset-y: 1px;
	--letter-spacing: 0em;
	--spacing: 0.25rem;
	--shadow-2xs: 0px 1px 0px 0px hsl(0 0% 0% / 0.0);
	--shadow-xs: 0px 1px 0px 0px hsl(0 0% 0% / 0.0);
	--shadow-sm: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 1px 2px -1px
		hsl(0 0% 0% / 0.0);
	--shadow: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 1px 2px -1px
		hsl(0 0% 0% / 0.0);
	--shadow-md: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 2px 4px -1px
		hsl(0 0% 0% / 0.0);
	--shadow-lg: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 4px 6px -1px
		hsl(0 0% 0% / 0.0);
	--shadow-xl: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 8px 10px -1px
		hsl(0 0% 0% / 0.0);
	--shadow-2xl: 0px 1px 0px 0px hsl(0 0% 0% / 0.0);
	--tracking-normal: 0em;
}

.dark {
	/* Soft dark theme colors */
	--background: oklch(0.1448 0 0);
	--foreground: oklch(0.9851 0 0);
	--card: oklch(0.2134 0 0);
	--card-foreground: oklch(0.9851 0 0);
	--popover: oklch(0.2686 0 0);
	--popover-foreground: oklch(0.9851 0 0);

	/* Soft blue primary color in dark mode */
	--primary: oklch(0.5555 0 0);
	--primary-foreground: oklch(0.9851 0 0);

	/* Soft secondary color in dark mode */
	--secondary: oklch(0.2686 0 0);
	--secondary-foreground: oklch(0.9851 0 0);

	/* Muted colors in dark mode */
	--muted: oklch(0.2686 0 0);
	--muted-foreground: oklch(0.709 0 0);

	/* Accent colors in dark mode */
	--accent: oklch(0.3715 0 0);
	--accent-foreground: oklch(0.9851 0 0);

	/* Destructive colors in dark mode */
	--destructive: oklch(0.7022 0.1892 22.2279);
	--destructive-foreground: oklch(0.2686 0 0);

	/* Border and input colors in dark mode */
	--border: oklch(0.3407 0 0);
	--input: oklch(0.4386 0 0);
	--ring: oklch(0.5555 0 0);

	/* Chart colors - soft pastels in dark mode */
	--chart-1: oklch(0.5555 0 0); /* Soft blue */
	--chart-2: oklch(0.5555 0 0); /* Soft teal */
	--chart-3: oklch(0.5555 0 0); /* Soft amber */
	--chart-4: oklch(0.5555 0 0); /* Soft purple */
	--chart-5: oklch(0.5555 0 0); /* Soft cyan */

	/* Sidebar colors in dark mode */
	--sidebar: oklch(0.2046 0 0);
	--sidebar-foreground: oklch(0.9851 0 0);
	--sidebar-primary: oklch(0.9851 0 0);
	--sidebar-primary-foreground: oklch(0.2046 0 0);
	--sidebar-accent: oklch(0.2686 0 0);
	--sidebar-accent-foreground: oklch(0.9851 0 0);
	--sidebar-border: oklch(1.0 0 0);
	--sidebar-ring: oklch(0.4386 0 0);
	--radius: 0rem;
	--font-sans: Geist Mono, monospace;
	--font-serif: Geist Mono, monospace;
	--font-mono: Geist Mono, monospace;
	--shadow-color: hsl(0 0% 0%);
	--shadow-opacity: 0;
	--shadow-blur: 0px;
	--shadow-spread: 0px;
	--shadow-offset-x: 0px;
	--shadow-offset-y: 1px;
	--letter-spacing: 0em;
	--spacing: 0.25rem;
	--shadow-2xs: 0px 1px 0px 0px hsl(0 0% 0% / 0.0);
	--shadow-xs: 0px 1px 0px 0px hsl(0 0% 0% / 0.0);
	--shadow-sm: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 1px 2px -1px
		hsl(0 0% 0% / 0.0);
	--shadow: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 1px 2px -1px
		hsl(0 0% 0% / 0.0);
	--shadow-md: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 2px 4px -1px
		hsl(0 0% 0% / 0.0);
	--shadow-lg: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 4px 6px -1px
		hsl(0 0% 0% / 0.0);
	--shadow-xl: 0px 1px 0px 0px hsl(0 0% 0% / 0.0), 0px 8px 10px -1px
		hsl(0 0% 0% / 0.0);
	--shadow-2xl: 0px 1px 0px 0px hsl(0 0% 0% / 0.0);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
		letter-spacing: var(--tracking-normal);
	}
}

/* Modern Startup Styles */
.gradient-border {
	position: relative;
	border-radius: var(--radius);
	background: linear-gradient(var(--background), var(--background)) padding-box,
		linear-gradient(to right, oklch(0.55 0.15 250), oklch(0.6 0.15 200))
		border-box;
	border: 2px solid transparent;
}

.hover-lift {
	transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
	transform: translateY(-2px);
}

.glass-effect {
	backdrop-filter: blur(16px) saturate(180%);
	background-color: rgba(255, 255, 255, 0.75);
	border: 1px solid rgba(209, 213, 219, 0.3);
}

.dark .glass-effect {
	background-color: rgba(17, 25, 40, 0.75);
	border: 1px solid rgba(255, 255, 255, 0.125);
}

/* Animations */
@keyframes float {
	0% {
		transform: translateY(0px);
	}
	50% {
		transform: translateY(-10px);
	}
	100% {
		transform: translateY(0px);
	}
}

.animate-float {
	animation: float 3s ease-in-out infinite;
}

@keyframes gradient {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

.gradient-animate {
	background: linear-gradient(
		-45deg,
		oklch(0.55 0.15 250),
		oklch(0.6 0.15 200),
		oklch(0.65 0.15 220),
		oklch(0.6 0.15 240)
	);
	background-size: 400% 400%;
	animation: gradient 15s ease infinite;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
	width: 10px;
}

::-webkit-scrollbar-track {
	background: var(--background);
}

::-webkit-scrollbar-thumb {
	background: var(--muted);
	border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
	background: var(--muted-foreground);
}

/* Loading Skeleton */
.skeleton {
	background: linear-gradient(
		90deg,
		var(--muted) 25%,
		var(--accent) 50%,
		var(--muted) 75%
	);
	background-size: 200% 100%;
	animation: loading 1.5s infinite;
}

@keyframes loading {
	0% {
		background-position: 200% 0;
	}
	100% {
		background-position: -200% 0;
	}
}

.chat-message {
	padding: 1rem;
	margin-bottom: 1rem !important;
}

.p-4 {
	padding: 1rem !important;
}

.chat-fullscreen {
	border: 1px solid var(--border) !important;
	/* padding: 10px !important; */
}

.widget-header {
	padding: 1rem !important;
	border-bottom: 1px solid var(--border) !important;
}

.chat-widget {
	textarea {
		padding: 10px;
		border: 1px solid var(--border) !important;
	}

	form {
		padding: 10px;
		border-top: 1px solid var(--border) !important;
	}

	.pl-6 {
		padding-left: 1rem !important;
	}
}

.chat-copy {
	padding: 8px 0 !important;
}
