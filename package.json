{"name": "bubl", "version": "0.1.0", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "packages:build": "pnpm --filter \"@bubl/packages\" build", "packages:dev": "pnpm --filter \"@bubl/packages\" dev", "packages:clean": "pnpm --filter \"@bubl/packages\" clean", "packages:publish": "pnpm --filter \"@bubl/packages\" publish-packages", "packages:create": "node scripts/create-package.js", "format": "biome format ./src --write", "check": "biome check --write ./src", "check!": "biome check --write --unsafe --no-errors-on-unmatched ./src", "clean": "rm -rf .next .output node_modules && pnpm install && pnpm dev", "build:check": "pnpm check! && pnpm build", "prepare": "husky", "db:migrate": "drizzle-kit migrate", "db:generate": "drizzle-kit generate", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:drop": "drizzle-kit drop", "db:pgvector": "tsx src/scripts/enable-pgvector.ts", "db:setup": "pnpm db:pgvector && pnpm db:migrate", "db:seed-plans": "tsx src/scripts/seed-plans.ts", "db:rebuild-vectors": "tsx src/scripts/rebuild-vector-indexes.ts", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:setup": "playwright test tests/auth.setup.ts", "test:security": "tsx src/scripts/test-security.ts", "security:audit": "pnpm audit", "inngest:dev": "npx inngest-cli@latest dev -u http://localhost:3000/api/inngest", "widget:build": "node scripts/build-widget-v2.js", "widget:dev": "node scripts/build-widget-v2.js --watch"}, "dependencies": {"@ai-sdk/mistral": "^1.2.7", "@ai-sdk/react": "^1.2.10", "@assistant-ui/react-markdown": "^0.10.3", "@clerk/backend": "^1.30.1", "@clerk/clerk-sdk-node": "^5.1.6", "@clerk/nextjs": "^6.18.0", "@mastra/core": "^0.9.0", "@mastra/pg": "^0.3.0", "@mastra/rag": "^0.1.19", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@t3-oss/env-nextjs": "^0.13.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.74.11", "@tanstack/react-query-devtools": "^5.74.11", "@tanstack/react-query-next-experimental": "^5.74.11", "ai": "^4.3.11", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "drizzle-zod": "^0.7.1", "framer-motion": "^12.9.2", "inngest": "^3.35.1", "lucide-react": "0.509.0", "next": "15.3.1", "next-themes": "^0.4.6", "pg": "^8.15.6", "pgvector": "^0.2.0", "postgres": "^3.4.5", "puppeteer": "^24.7.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "robots-parser": "^3.0.1", "sonner": "^2.0.3", "svix": "^1.64.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.3.3", "uuid": "^11.1.0", "web-vitals": "^4.2.4", "zod": "^3.24.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@clerk/testing": "^1.6.6", "@playwright/test": "^1.52.0", "@tailwindcss/postcss": "^4", "@types/node": "22.15.17", "@types/pg": "^8.11.14", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.0", "esbuild": "^0.24.2", "husky": "^9.1.7", "node-fetch": "^3.3.2", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}