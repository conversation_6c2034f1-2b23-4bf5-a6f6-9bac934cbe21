
/**
 * Bubl Widget V2 Bundle
 * Self-contained React widget with Shadow DOM isolation
 * Generated: 2025-05-27T19:22:04.928Z
 */

"use strict";(()=>{var Rx=Object.create;var ha=Object.defineProperty,Nx=Object.defineProperties,Lx=Object.getOwnPropertyDescriptor,Ux=Object.getOwnPropertyDescriptors,Bx=Object.getOwnPropertyNames,pa=Object.getOwnPropertySymbols,Hx=Object.getPrototypeOf,Co=Object.prototype.hasOwnProperty,Im=Object.prototype.propertyIsEnumerable;var Jm=Math.pow,Fm=(t,e,n)=>e in t?ha(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,z=(t,e)=>{for(var n in e||(e={}))Co.call(e,n)&&Fm(t,n,e[n]);if(pa)for(var n of pa(e))Im.call(e,n)&&Fm(t,n,e[n]);return t},gt=(t,e)=>Nx(t,Ux(e));var Wm=(t,e)=>{var n={};for(var l in t)Co.call(t,l)&&e.indexOf(l)<0&&(n[l]=t[l]);if(t!=null&&pa)for(var l of pa(t))e.indexOf(l)<0&&Im.call(t,l)&&(n[l]=t[l]);return n};var Kt=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),Pm=(t,e)=>{for(var n in e)ha(t,n,{get:e[n],enumerable:!0})},qx=(t,e,n,l)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Bx(e))!Co.call(t,i)&&i!==n&&ha(t,i,{get:()=>e[i],enumerable:!(l=Lx(e,i))||l.enumerable});return t};var bn=(t,e,n)=>(n=t!=null?Rx(Hx(t)):{},qx(e||!t||!t.__esModule?ha(n,"default",{value:t,enumerable:!0}):n,t));var Xi=(t,e,n)=>new Promise((l,i)=>{var r=o=>{try{u(n.next(o))}catch(c){i(c)}},a=o=>{try{u(n.throw(o))}catch(c){i(c)}},u=o=>o.done?l(o.value):Promise.resolve(o.value).then(r,a);u((n=n.apply(t,e)).next())});var fp=Kt(X=>{"use strict";var Mo=Symbol.for("react.transitional.element"),jx=Symbol.for("react.portal"),Yx=Symbol.for("react.fragment"),Vx=Symbol.for("react.strict_mode"),Xx=Symbol.for("react.profiler"),Gx=Symbol.for("react.consumer"),Qx=Symbol.for("react.context"),Zx=Symbol.for("react.forward_ref"),Kx=Symbol.for("react.suspense"),Fx=Symbol.for("react.memo"),ip=Symbol.for("react.lazy"),$m=Symbol.iterator;function Ix(t){return t===null||typeof t!="object"?null:(t=$m&&t[$m]||t["@@iterator"],typeof t=="function"?t:null)}var rp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ap=Object.assign,up={};function Hl(t,e,n){this.props=t,this.context=e,this.refs=up,this.updater=n||rp}Hl.prototype.isReactComponent={};Hl.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Hl.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function op(){}op.prototype=Hl.prototype;function Oo(t,e,n){this.props=t,this.context=e,this.refs=up,this.updater=n||rp}var _o=Oo.prototype=new op;_o.constructor=Oo;ap(_o,Hl.prototype);_o.isPureReactComponent=!0;var tp=Array.isArray,ft={H:null,A:null,T:null,S:null,V:null},cp=Object.prototype.hasOwnProperty;function Ro(t,e,n,l,i,r){return n=r.ref,{$$typeof:Mo,type:t,key:e,ref:n!==void 0?n:null,props:r}}function Jx(t,e){return Ro(t.type,e,void 0,void 0,void 0,t.props)}function No(t){return typeof t=="object"&&t!==null&&t.$$typeof===Mo}function Wx(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var ep=/\/+/g;function Do(t,e){return typeof t=="object"&&t!==null&&t.key!=null?Wx(""+t.key):e.toString(36)}function np(){}function Px(t){switch(t.status){case"fulfilled":return t.value;case"rejected":throw t.reason;default:switch(typeof t.status=="string"?t.then(np,np):(t.status="pending",t.then(function(e){t.status==="pending"&&(t.status="fulfilled",t.value=e)},function(e){t.status==="pending"&&(t.status="rejected",t.reason=e)})),t.status){case"fulfilled":return t.value;case"rejected":throw t.reason}}throw t}function Bl(t,e,n,l,i){var r=typeof t;(r==="undefined"||r==="boolean")&&(t=null);var a=!1;if(t===null)a=!0;else switch(r){case"bigint":case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case Mo:case jx:a=!0;break;case ip:return a=t._init,Bl(a(t._payload),e,n,l,i)}}if(a)return i=i(t),a=l===""?"."+Do(t,0):l,tp(i)?(n="",a!=null&&(n=a.replace(ep,"$&/")+"/"),Bl(i,e,n,"",function(c){return c})):i!=null&&(No(i)&&(i=Jx(i,n+(i.key==null||t&&t.key===i.key?"":(""+i.key).replace(ep,"$&/")+"/")+a)),e.push(i)),1;a=0;var u=l===""?".":l+":";if(tp(t))for(var o=0;o<t.length;o++)l=t[o],r=u+Do(l,o),a+=Bl(l,e,n,r,i);else if(o=Ix(t),typeof o=="function")for(t=o.call(t),o=0;!(l=t.next()).done;)l=l.value,r=u+Do(l,o++),a+=Bl(l,e,n,r,i);else if(r==="object"){if(typeof t.then=="function")return Bl(Px(t),e,n,l,i);throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.")}return a}function da(t,e,n){if(t==null)return t;var l=[],i=0;return Bl(t,l,"","",function(r){return e.call(n,r,i++)}),l}function $x(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var lp=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function tb(){}X.Children={map:da,forEach:function(t,e,n){da(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return da(t,function(){e++}),e},toArray:function(t){return da(t,function(e){return e})||[]},only:function(t){if(!No(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};X.Component=Hl;X.Fragment=Yx;X.Profiler=Xx;X.PureComponent=Oo;X.StrictMode=Vx;X.Suspense=Kx;X.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ft;X.__COMPILER_RUNTIME={__proto__:null,c:function(t){return ft.H.useMemoCache(t)}};X.cache=function(t){return function(){return t.apply(null,arguments)}};X.cloneElement=function(t,e,n){if(t==null)throw Error("The argument must be a React element, but you passed "+t+".");var l=ap({},t.props),i=t.key,r=void 0;if(e!=null)for(a in e.ref!==void 0&&(r=void 0),e.key!==void 0&&(i=""+e.key),e)!cp.call(e,a)||a==="key"||a==="__self"||a==="__source"||a==="ref"&&e.ref===void 0||(l[a]=e[a]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var u=Array(a),o=0;o<a;o++)u[o]=arguments[o+2];l.children=u}return Ro(t.type,i,void 0,void 0,r,l)};X.createContext=function(t){return t={$$typeof:Qx,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null},t.Provider=t,t.Consumer={$$typeof:Gx,_context:t},t};X.createElement=function(t,e,n){var l,i={},r=null;if(e!=null)for(l in e.key!==void 0&&(r=""+e.key),e)cp.call(e,l)&&l!=="key"&&l!=="__self"&&l!=="__source"&&(i[l]=e[l]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var u=Array(a),o=0;o<a;o++)u[o]=arguments[o+2];i.children=u}if(t&&t.defaultProps)for(l in a=t.defaultProps,a)i[l]===void 0&&(i[l]=a[l]);return Ro(t,r,void 0,void 0,null,i)};X.createRef=function(){return{current:null}};X.forwardRef=function(t){return{$$typeof:Zx,render:t}};X.isValidElement=No;X.lazy=function(t){return{$$typeof:ip,_payload:{_status:-1,_result:t},_init:$x}};X.memo=function(t,e){return{$$typeof:Fx,type:t,compare:e===void 0?null:e}};X.startTransition=function(t){var e=ft.T,n={};ft.T=n;try{var l=t(),i=ft.S;i!==null&&i(n,l),typeof l=="object"&&l!==null&&typeof l.then=="function"&&l.then(tb,lp)}catch(r){lp(r)}finally{ft.T=e}};X.unstable_useCacheRefresh=function(){return ft.H.useCacheRefresh()};X.use=function(t){return ft.H.use(t)};X.useActionState=function(t,e,n){return ft.H.useActionState(t,e,n)};X.useCallback=function(t,e){return ft.H.useCallback(t,e)};X.useContext=function(t){return ft.H.useContext(t)};X.useDebugValue=function(){};X.useDeferredValue=function(t,e){return ft.H.useDeferredValue(t,e)};X.useEffect=function(t,e,n){var l=ft.H;if(typeof n=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return l.useEffect(t,e)};X.useId=function(){return ft.H.useId()};X.useImperativeHandle=function(t,e,n){return ft.H.useImperativeHandle(t,e,n)};X.useInsertionEffect=function(t,e){return ft.H.useInsertionEffect(t,e)};X.useLayoutEffect=function(t,e){return ft.H.useLayoutEffect(t,e)};X.useMemo=function(t,e){return ft.H.useMemo(t,e)};X.useOptimistic=function(t,e){return ft.H.useOptimistic(t,e)};X.useReducer=function(t,e,n){return ft.H.useReducer(t,e,n)};X.useRef=function(t){return ft.H.useRef(t)};X.useState=function(t){return ft.H.useState(t)};X.useSyncExternalStore=function(t,e,n){return ft.H.useSyncExternalStore(t,e,n)};X.useTransition=function(){return ft.H.useTransition()};X.version="19.1.0"});var Gi=Kt((fA,sp)=>{"use strict";sp.exports=fp()});var Sp=Kt(st=>{"use strict";function Ho(t,e){var n=t.length;t.push(e);t:for(;0<n;){var l=n-1>>>1,i=t[l];if(0<ga(i,e))t[l]=e,t[n]=i,n=l;else break t}}function Be(t){return t.length===0?null:t[0]}function xa(t){if(t.length===0)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var l=0,i=t.length,r=i>>>1;l<r;){var a=2*(l+1)-1,u=t[a],o=a+1,c=t[o];if(0>ga(u,n))o<i&&0>ga(c,u)?(t[l]=c,t[o]=n,l=o):(t[l]=u,t[a]=n,l=a);else if(o<i&&0>ga(c,n))t[l]=c,t[o]=n,l=o;else break t}}return e}function ga(t,e){var n=t.sortIndex-e.sortIndex;return n!==0?n:t.id-e.id}st.unstable_now=void 0;typeof performance=="object"&&typeof performance.now=="function"?(mp=performance,st.unstable_now=function(){return mp.now()}):(Lo=Date,pp=Lo.now(),st.unstable_now=function(){return Lo.now()-pp});var mp,Lo,pp,Pe=[],vn=[],eb=1,ve=null,Vt=3,qo=!1,Qi=!1,Zi=!1,jo=!1,gp=typeof setTimeout=="function"?setTimeout:null,yp=typeof clearTimeout=="function"?clearTimeout:null,hp=typeof setImmediate!="undefined"?setImmediate:null;function ya(t){for(var e=Be(vn);e!==null;){if(e.callback===null)xa(vn);else if(e.startTime<=t)xa(vn),e.sortIndex=e.expirationTime,Ho(Pe,e);else break;e=Be(vn)}}function Yo(t){if(Zi=!1,ya(t),!Qi)if(Be(Pe)!==null)Qi=!0,jl||(jl=!0,ql());else{var e=Be(vn);e!==null&&Vo(Yo,e.startTime-t)}}var jl=!1,Ki=-1,xp=5,bp=-1;function vp(){return jo?!0:!(st.unstable_now()-bp<xp)}function Uo(){if(jo=!1,jl){var t=st.unstable_now();bp=t;var e=!0;try{t:{Qi=!1,Zi&&(Zi=!1,yp(Ki),Ki=-1),qo=!0;var n=Vt;try{e:{for(ya(t),ve=Be(Pe);ve!==null&&!(ve.expirationTime>t&&vp());){var l=ve.callback;if(typeof l=="function"){ve.callback=null,Vt=ve.priorityLevel;var i=l(ve.expirationTime<=t);if(t=st.unstable_now(),typeof i=="function"){ve.callback=i,ya(t),e=!0;break e}ve===Be(Pe)&&xa(Pe),ya(t)}else xa(Pe);ve=Be(Pe)}if(ve!==null)e=!0;else{var r=Be(vn);r!==null&&Vo(Yo,r.startTime-t),e=!1}}break t}finally{ve=null,Vt=n,qo=!1}e=void 0}}finally{e?ql():jl=!1}}}var ql;typeof hp=="function"?ql=function(){hp(Uo)}:typeof MessageChannel!="undefined"?(Bo=new MessageChannel,dp=Bo.port2,Bo.port1.onmessage=Uo,ql=function(){dp.postMessage(null)}):ql=function(){gp(Uo,0)};var Bo,dp;function Vo(t,e){Ki=gp(function(){t(st.unstable_now())},e)}st.unstable_IdlePriority=5;st.unstable_ImmediatePriority=1;st.unstable_LowPriority=4;st.unstable_NormalPriority=3;st.unstable_Profiling=null;st.unstable_UserBlockingPriority=2;st.unstable_cancelCallback=function(t){t.callback=null};st.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):xp=0<t?Math.floor(1e3/t):5};st.unstable_getCurrentPriorityLevel=function(){return Vt};st.unstable_next=function(t){switch(Vt){case 1:case 2:case 3:var e=3;break;default:e=Vt}var n=Vt;Vt=e;try{return t()}finally{Vt=n}};st.unstable_requestPaint=function(){jo=!0};st.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=Vt;Vt=t;try{return e()}finally{Vt=n}};st.unstable_scheduleCallback=function(t,e,n){var l=st.unstable_now();switch(typeof n=="object"&&n!==null?(n=n.delay,n=typeof n=="number"&&0<n?l+n:l):n=l,t){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return i=n+i,t={id:eb++,callback:e,priorityLevel:t,startTime:n,expirationTime:i,sortIndex:-1},n>l?(t.sortIndex=n,Ho(vn,t),Be(Pe)===null&&t===Be(vn)&&(Zi?(yp(Ki),Ki=-1):Zi=!0,Vo(Yo,n-l))):(t.sortIndex=i,Ho(Pe,t),Qi||qo||(Qi=!0,jl||(jl=!0,ql()))),t};st.unstable_shouldYield=vp;st.unstable_wrapCallback=function(t){var e=Vt;return function(){var n=Vt;Vt=e;try{return t.apply(this,arguments)}finally{Vt=n}}}});var Ep=Kt((mA,kp)=>{"use strict";kp.exports=Sp()});var Ap=Kt(It=>{"use strict";var nb=Gi();function Tp(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Sn(){}var Ft={d:{f:Sn,r:function(){throw Error(Tp(522))},D:Sn,C:Sn,L:Sn,m:Sn,X:Sn,S:Sn,M:Sn},p:0,findDOMNode:null},lb=Symbol.for("react.portal");function ib(t,e,n){var l=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:lb,key:l==null?null:""+l,children:t,containerInfo:e,implementation:n}}var Fi=nb.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function ba(t,e){if(t==="font")return"";if(typeof e=="string")return e==="use-credentials"?e:""}It.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Ft;It.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)throw Error(Tp(299));return ib(t,e,null,n)};It.flushSync=function(t){var e=Fi.T,n=Ft.p;try{if(Fi.T=null,Ft.p=2,t)return t()}finally{Fi.T=e,Ft.p=n,Ft.d.f()}};It.preconnect=function(t,e){typeof t=="string"&&(e?(e=e.crossOrigin,e=typeof e=="string"?e==="use-credentials"?e:"":void 0):e=null,Ft.d.C(t,e))};It.prefetchDNS=function(t){typeof t=="string"&&Ft.d.D(t)};It.preinit=function(t,e){if(typeof t=="string"&&e&&typeof e.as=="string"){var n=e.as,l=ba(n,e.crossOrigin),i=typeof e.integrity=="string"?e.integrity:void 0,r=typeof e.fetchPriority=="string"?e.fetchPriority:void 0;n==="style"?Ft.d.S(t,typeof e.precedence=="string"?e.precedence:void 0,{crossOrigin:l,integrity:i,fetchPriority:r}):n==="script"&&Ft.d.X(t,{crossOrigin:l,integrity:i,fetchPriority:r,nonce:typeof e.nonce=="string"?e.nonce:void 0})}};It.preinitModule=function(t,e){if(typeof t=="string")if(typeof e=="object"&&e!==null){if(e.as==null||e.as==="script"){var n=ba(e.as,e.crossOrigin);Ft.d.M(t,{crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0})}}else e==null&&Ft.d.M(t)};It.preload=function(t,e){if(typeof t=="string"&&typeof e=="object"&&e!==null&&typeof e.as=="string"){var n=e.as,l=ba(n,e.crossOrigin);Ft.d.L(t,n,{crossOrigin:l,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0,type:typeof e.type=="string"?e.type:void 0,fetchPriority:typeof e.fetchPriority=="string"?e.fetchPriority:void 0,referrerPolicy:typeof e.referrerPolicy=="string"?e.referrerPolicy:void 0,imageSrcSet:typeof e.imageSrcSet=="string"?e.imageSrcSet:void 0,imageSizes:typeof e.imageSizes=="string"?e.imageSizes:void 0,media:typeof e.media=="string"?e.media:void 0})}};It.preloadModule=function(t,e){if(typeof t=="string")if(e){var n=ba(e.as,e.crossOrigin);Ft.d.m(t,{as:typeof e.as=="string"&&e.as!=="script"?e.as:void 0,crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0})}else Ft.d.m(t)};It.requestFormReset=function(t){Ft.d.r(t)};It.unstable_batchedUpdates=function(t,e){return t(e)};It.useFormState=function(t,e,n){return Fi.H.useFormState(t,e,n)};It.useFormStatus=function(){return Fi.H.useHostTransitionStatus()};It.version="19.1.0"});var Cp=Kt((hA,zp)=>{"use strict";function wp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(wp)}catch(t){console.error(t)}}wp(),zp.exports=Ap()});var My=Kt(Yu=>{"use strict";var Mt=Ep(),Jh=Gi(),rb=Cp();function w(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Wh(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Lr(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function Ph(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Dp(t){if(Lr(t)!==t)throw Error(w(188))}function ab(t){var e=t.alternate;if(!e){if(e=Lr(t),e===null)throw Error(w(188));return e!==t?null:t}for(var n=t,l=e;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return Dp(i),t;if(r===l)return Dp(i),e;r=r.sibling}throw Error(w(188))}if(n.return!==l.return)n=i,l=r;else{for(var a=!1,u=i.child;u;){if(u===n){a=!0,n=i,l=r;break}if(u===l){a=!0,l=i,n=r;break}u=u.sibling}if(!a){for(u=r.child;u;){if(u===n){a=!0,n=r,l=i;break}if(u===l){a=!0,l=r,n=i;break}u=u.sibling}if(!a)throw Error(w(189))}}if(n.alternate!==l)throw Error(w(190))}if(n.tag!==3)throw Error(w(188));return n.stateNode.current===n?t:e}function $h(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=$h(t),e!==null)return e;t=t.sibling}return null}var ct=Object.assign,ub=Symbol.for("react.element"),va=Symbol.for("react.transitional.element"),lr=Symbol.for("react.portal"),Kl=Symbol.for("react.fragment"),td=Symbol.for("react.strict_mode"),vc=Symbol.for("react.profiler"),ob=Symbol.for("react.provider"),ed=Symbol.for("react.consumer"),ln=Symbol.for("react.context"),gf=Symbol.for("react.forward_ref"),Sc=Symbol.for("react.suspense"),kc=Symbol.for("react.suspense_list"),yf=Symbol.for("react.memo"),Tn=Symbol.for("react.lazy");Symbol.for("react.scope");var Ec=Symbol.for("react.activity");Symbol.for("react.legacy_hidden");Symbol.for("react.tracing_marker");var cb=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var Mp=Symbol.iterator;function Ii(t){return t===null||typeof t!="object"?null:(t=Mp&&t[Mp]||t["@@iterator"],typeof t=="function"?t:null)}var fb=Symbol.for("react.client.reference");function Tc(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===fb?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Kl:return"Fragment";case vc:return"Profiler";case td:return"StrictMode";case Sc:return"Suspense";case kc:return"SuspenseList";case Ec:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case lr:return"Portal";case ln:return(t.displayName||"Context")+".Provider";case ed:return(t._context.displayName||"Context")+".Consumer";case gf:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case yf:return e=t.displayName||null,e!==null?e:Tc(t.type)||"Memo";case Tn:e=t._payload,t=t._init;try{return Tc(t(e))}catch(n){}}return null}var ir=Array.isArray,H=Jh.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,tt=rb.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ul={pending:!1,data:null,method:null,action:null},Ac=[],Fl=-1;function Ge(t){return{current:t}}function Ut(t){0>Fl||(t.current=Ac[Fl],Ac[Fl]=null,Fl--)}function pt(t,e){Fl++,Ac[Fl]=t.current,t.current=e}var Ye=Ge(null),Sr=Ge(null),Nn=Ge(null),Ja=Ge(null);function Wa(t,e){switch(pt(Nn,e),pt(Sr,t),pt(Ye,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Uh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Uh(e),t=xy(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Ut(Ye),pt(Ye,t)}function pi(){Ut(Ye),Ut(Sr),Ut(Nn)}function wc(t){t.memoizedState!==null&&pt(Ja,t);var e=Ye.current,n=xy(e,t.type);e!==n&&(pt(Sr,t),pt(Ye,n))}function Pa(t){Sr.current===t&&(Ut(Ye),Ut(Sr)),Ja.current===t&&(Ut(Ja),Or._currentValue=ul)}var zc=Object.prototype.hasOwnProperty,xf=Mt.unstable_scheduleCallback,Xo=Mt.unstable_cancelCallback,sb=Mt.unstable_shouldYield,mb=Mt.unstable_requestPaint,Ve=Mt.unstable_now,pb=Mt.unstable_getCurrentPriorityLevel,nd=Mt.unstable_ImmediatePriority,ld=Mt.unstable_UserBlockingPriority,$a=Mt.unstable_NormalPriority,hb=Mt.unstable_LowPriority,id=Mt.unstable_IdlePriority,db=Mt.log,gb=Mt.unstable_setDisableYieldValue,Ur=null,he=null;function Mn(t){if(typeof db=="function"&&gb(t),he&&typeof he.setStrictMode=="function")try{he.setStrictMode(Ur,t)}catch(e){}}var de=Math.clz32?Math.clz32:bb,yb=Math.log,xb=Math.LN2;function bb(t){return t>>>=0,t===0?32:31-(yb(t)/xb|0)|0}var Sa=256,ka=4194304;function il(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function wu(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var i=0,r=t.suspendedLanes,a=t.pingedLanes;t=t.warmLanes;var u=l&134217727;return u!==0?(l=u&~r,l!==0?i=il(l):(a&=u,a!==0?i=il(a):n||(n=u&~t,n!==0&&(i=il(n))))):(u=l&~r,u!==0?i=il(u):a!==0?i=il(a):n||(n=l&~t,n!==0&&(i=il(n)))),i===0?0:e!==0&&e!==i&&!(e&r)&&(r=i&-i,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:i}function Br(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function vb(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function rd(){var t=Sa;return Sa<<=1,!(Sa&4194048)&&(Sa=256),t}function ad(){var t=ka;return ka<<=1,!(ka&62914560)&&(ka=4194304),t}function Go(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Hr(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Sb(t,e,n,l,i,r){var a=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var u=t.entanglements,o=t.expirationTimes,c=t.hiddenUpdates;for(n=a&~n;0<n;){var f=31-de(n),s=1<<f;u[f]=0,o[f]=-1;var p=c[f];if(p!==null)for(c[f]=null,f=0;f<p.length;f++){var m=p[f];m!==null&&(m.lane&=-536870913)}n&=~s}l!==0&&ud(t,l,0),r!==0&&i===0&&t.tag!==0&&(t.suspendedLanes|=r&~(a&~e))}function ud(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-de(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function od(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-de(n),i=1<<l;i&e|t[l]&e&&(t[l]|=e),n&=~i}}function bf(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function vf(t){return t&=-t,2<t?8<t?t&134217727?32:268435456:8:2}function cd(){var t=tt.p;return t!==0?t:(t=window.event,t===void 0?32:Cy(t.type))}function kb(t,e){var n=tt.p;try{return tt.p=t,e()}finally{tt.p=n}}var Qn=Math.random().toString(36).slice(2),Xt="__reactFiber$"+Qn,le="__reactProps$"+Qn,Ti="__reactContainer$"+Qn,Cc="__reactEvents$"+Qn,Eb="__reactListeners$"+Qn,Tb="__reactHandles$"+Qn,Op="__reactResources$"+Qn,qr="__reactMarker$"+Qn;function Sf(t){delete t[Xt],delete t[le],delete t[Cc],delete t[Eb],delete t[Tb]}function Il(t){var e=t[Xt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Ti]||n[Xt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=qh(t);t!==null;){if(n=t[Xt])return n;t=qh(t)}return e}t=n,n=t.parentNode}return null}function Ai(t){if(t=t[Xt]||t[Ti]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function rr(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(w(33))}function ri(t){var e=t[Op];return e||(e=t[Op]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Nt(t){t[qr]=!0}var fd=new Set,sd={};function xl(t,e){hi(t,e),hi(t+"Capture",e)}function hi(t,e){for(sd[t]=e,t=0;t<e.length;t++)fd.add(e[t])}var Ab=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),_p={},Rp={};function wb(t){return zc.call(Rp,t)?!0:zc.call(_p,t)?!1:Ab.test(t)?Rp[t]=!0:(_p[t]=!0,!1)}function Ba(t,e,n){if(wb(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Ea(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function $e(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var Qo,Np;function Gl(t){if(Qo===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);Qo=e&&e[1]||"",Np=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Qo+t+Np}var Zo=!1;function Ko(t,e){if(!t||Zo)return"";Zo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var s=function(){throw Error()};if(Object.defineProperty(s.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(s,[])}catch(m){var p=m}Reflect.construct(t,[],s)}else{try{s.call()}catch(m){p=m}t.call(s.prototype)}}else{try{throw Error()}catch(m){p=m}(s=t())&&typeof s.catch=="function"&&s.catch(function(){})}}catch(m){if(m&&p&&typeof m.stack=="string")return[m.stack,p.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),a=r[0],u=r[1];if(a&&u){var o=a.split(`
`),c=u.split(`
`);for(i=l=0;l<o.length&&!o[l].includes("DetermineComponentFrameRoot");)l++;for(;i<c.length&&!c[i].includes("DetermineComponentFrameRoot");)i++;if(l===o.length||i===c.length)for(l=o.length-1,i=c.length-1;1<=l&&0<=i&&o[l]!==c[i];)i--;for(;1<=l&&0<=i;l--,i--)if(o[l]!==c[i]){if(l!==1||i!==1)do if(l--,i--,0>i||o[l]!==c[i]){var f=`
`+o[l].replace(" at new "," at ");return t.displayName&&f.includes("<anonymous>")&&(f=f.replace("<anonymous>",t.displayName)),f}while(1<=l&&0<=i);break}}}finally{Zo=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Gl(n):""}function zb(t){switch(t.tag){case 26:case 27:case 5:return Gl(t.type);case 16:return Gl("Lazy");case 13:return Gl("Suspense");case 19:return Gl("SuspenseList");case 0:case 15:return Ko(t.type,!1);case 11:return Ko(t.type.render,!1);case 1:return Ko(t.type,!0);case 31:return Gl("Activity");default:return""}}function Lp(t){try{var e="";do e+=zb(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function ke(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function md(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Cb(t){var e=md(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(a){l=""+a,r.call(this,a)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(a){l=""+a},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function tu(t){t._valueTracker||(t._valueTracker=Cb(t))}function pd(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=md(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function eu(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}var Db=/[\n"\\]/g;function Ae(t){return t.replace(Db,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Dc(t,e,n,l,i,r,a,u){t.name="",a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"?t.type=a:t.removeAttribute("type"),e!=null?a==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ke(e)):t.value!==""+ke(e)&&(t.value=""+ke(e)):a!=="submit"&&a!=="reset"||t.removeAttribute("value"),e!=null?Mc(t,a,ke(e)):n!=null?Mc(t,a,ke(n)):l!=null&&t.removeAttribute("value"),i==null&&r!=null&&(t.defaultChecked=!!r),i!=null&&(t.checked=i&&typeof i!="function"&&typeof i!="symbol"),u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"?t.name=""+ke(u):t.removeAttribute("name")}function hd(t,e,n,l,i,r,a,u){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+ke(n):"",e=e!=null?""+ke(e):n,u||e===t.value||(t.value=e),t.defaultValue=e}l=l!=null?l:i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=u?t.checked:!!l,t.defaultChecked=!!l,a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"&&(t.name=a)}function Mc(t,e,n){e==="number"&&eu(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function ai(t,e,n,l){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&l&&(t[n].defaultSelected=!0)}else{for(n=""+ke(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,l&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function dd(t,e,n){if(e!=null&&(e=""+ke(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+ke(n):""}function gd(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(w(92));if(ir(l)){if(1<l.length)throw Error(w(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=ke(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function di(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var Mb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Up(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||Mb.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function yd(t,e,n){if(e!=null&&typeof e!="object")throw Error(w(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var i in e)l=e[i],e.hasOwnProperty(i)&&n[i]!==l&&Up(t,i,l)}else for(var r in e)e.hasOwnProperty(r)&&Up(t,r,e[r])}function kf(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ob=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),_b=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ha(t){return _b.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Oc=null;function Ef(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Jl=null,ui=null;function Bp(t){var e=Ai(t);if(e&&(t=e.stateNode)){var n=t[le]||null;t:switch(t=e.stateNode,e.type){case"input":if(Dc(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ae(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var i=l[le]||null;if(!i)throw Error(w(90));Dc(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&pd(l)}break t;case"textarea":dd(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&ai(t,!!n.multiple,e,!1)}}}var Fo=!1;function xd(t,e,n){if(Fo)return t(e,n);Fo=!0;try{var l=t(e);return l}finally{if(Fo=!1,(Jl!==null||ui!==null)&&(Uu(),Jl&&(e=Jl,t=ui,ui=Jl=null,Bp(e),t)))for(e=0;e<t.length;e++)Bp(t[e])}}function kr(t,e){var n=t.stateNode;if(n===null)return null;var l=n[le]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(w(231,e,typeof n));return n}var sn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),_c=!1;if(sn)try{Yl={},Object.defineProperty(Yl,"passive",{get:function(){_c=!0}}),window.addEventListener("test",Yl,Yl),window.removeEventListener("test",Yl,Yl)}catch(t){_c=!1}var Yl,On=null,Tf=null,qa=null;function bd(){if(qa)return qa;var t,e=Tf,n=e.length,l,i="value"in On?On.value:On.textContent,r=i.length;for(t=0;t<n&&e[t]===i[t];t++);var a=n-t;for(l=1;l<=a&&e[n-l]===i[r-l];l++);return qa=i.slice(t,1<l?1-l:void 0)}function ja(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ta(){return!0}function Hp(){return!1}function ie(t){function e(n,l,i,r,a){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=r,this.target=a,this.currentTarget=null;for(var u in t)t.hasOwnProperty(u)&&(n=t[u],this[u]=n?n(r):r[u]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Ta:Hp,this.isPropagationStopped=Hp,this}return ct(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ta)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ta)},persist:function(){},isPersistent:Ta}),e}var bl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},zu=ie(bl),jr=ct({},bl,{view:0,detail:0}),Rb=ie(jr),Io,Jo,Ji,Cu=ct({},jr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Af,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ji&&(Ji&&t.type==="mousemove"?(Io=t.screenX-Ji.screenX,Jo=t.screenY-Ji.screenY):Jo=Io=0,Ji=t),Io)},movementY:function(t){return"movementY"in t?t.movementY:Jo}}),qp=ie(Cu),Nb=ct({},Cu,{dataTransfer:0}),Lb=ie(Nb),Ub=ct({},jr,{relatedTarget:0}),Wo=ie(Ub),Bb=ct({},bl,{animationName:0,elapsedTime:0,pseudoElement:0}),Hb=ie(Bb),qb=ct({},bl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),jb=ie(qb),Yb=ct({},bl,{data:0}),jp=ie(Yb),Vb={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Xb={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Gb={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qb(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Gb[t])?!!e[t]:!1}function Af(){return Qb}var Zb=ct({},jr,{key:function(t){if(t.key){var e=Vb[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ja(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Xb[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Af,charCode:function(t){return t.type==="keypress"?ja(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ja(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Kb=ie(Zb),Fb=ct({},Cu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Yp=ie(Fb),Ib=ct({},jr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Af}),Jb=ie(Ib),Wb=ct({},bl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Pb=ie(Wb),$b=ct({},Cu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),tv=ie($b),ev=ct({},bl,{newState:0,oldState:0}),nv=ie(ev),lv=[9,13,27,32],wf=sn&&"CompositionEvent"in window,ur=null;sn&&"documentMode"in document&&(ur=document.documentMode);var iv=sn&&"TextEvent"in window&&!ur,vd=sn&&(!wf||ur&&8<ur&&11>=ur),Vp=" ",Xp=!1;function Sd(t,e){switch(t){case"keyup":return lv.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function kd(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Wl=!1;function rv(t,e){switch(t){case"compositionend":return kd(e);case"keypress":return e.which!==32?null:(Xp=!0,Vp);case"textInput":return t=e.data,t===Vp&&Xp?null:t;default:return null}}function av(t,e){if(Wl)return t==="compositionend"||!wf&&Sd(t,e)?(t=bd(),qa=Tf=On=null,Wl=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return vd&&e.locale!=="ko"?null:e.data;default:return null}}var uv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Gp(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!uv[t.type]:e==="textarea"}function Ed(t,e,n,l){Jl?ui?ui.push(l):ui=[l]:Jl=l,e=bu(e,"onChange"),0<e.length&&(n=new zu("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var or=null,Er=null;function ov(t){dy(t,0)}function Du(t){var e=rr(t);if(pd(e))return t}function Qp(t,e){if(t==="change")return e}var Td=!1;sn&&(sn?(wa="oninput"in document,wa||(Po=document.createElement("div"),Po.setAttribute("oninput","return;"),wa=typeof Po.oninput=="function"),Aa=wa):Aa=!1,Td=Aa&&(!document.documentMode||9<document.documentMode));var Aa,wa,Po;function Zp(){or&&(or.detachEvent("onpropertychange",Ad),Er=or=null)}function Ad(t){if(t.propertyName==="value"&&Du(Er)){var e=[];Ed(e,Er,t,Ef(t)),xd(ov,e)}}function cv(t,e,n){t==="focusin"?(Zp(),or=e,Er=n,or.attachEvent("onpropertychange",Ad)):t==="focusout"&&Zp()}function fv(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Du(Er)}function sv(t,e){if(t==="click")return Du(e)}function mv(t,e){if(t==="input"||t==="change")return Du(e)}function pv(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var xe=typeof Object.is=="function"?Object.is:pv;function Tr(t,e){if(xe(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!zc.call(e,i)||!xe(t[i],e[i]))return!1}return!0}function Kp(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Fp(t,e){var n=Kp(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Kp(n)}}function wd(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?wd(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function zd(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=eu(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch(l){n=!1}if(n)t=e.contentWindow;else break;e=eu(t.document)}return e}function zf(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var hv=sn&&"documentMode"in document&&11>=document.documentMode,Pl=null,Rc=null,cr=null,Nc=!1;function Ip(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Nc||Pl==null||Pl!==eu(l)||(l=Pl,"selectionStart"in l&&zf(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),cr&&Tr(cr,l)||(cr=l,l=bu(Rc,"onSelect"),0<l.length&&(e=new zu("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=Pl)))}function ll(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var $l={animationend:ll("Animation","AnimationEnd"),animationiteration:ll("Animation","AnimationIteration"),animationstart:ll("Animation","AnimationStart"),transitionrun:ll("Transition","TransitionRun"),transitionstart:ll("Transition","TransitionStart"),transitioncancel:ll("Transition","TransitionCancel"),transitionend:ll("Transition","TransitionEnd")},$o={},Cd={};sn&&(Cd=document.createElement("div").style,"AnimationEvent"in window||(delete $l.animationend.animation,delete $l.animationiteration.animation,delete $l.animationstart.animation),"TransitionEvent"in window||delete $l.transitionend.transition);function vl(t){if($o[t])return $o[t];if(!$l[t])return t;var e=$l[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Cd)return $o[t]=e[n];return t}var Dd=vl("animationend"),Md=vl("animationiteration"),Od=vl("animationstart"),dv=vl("transitionrun"),gv=vl("transitionstart"),yv=vl("transitioncancel"),_d=vl("transitionend"),Rd=new Map,Lc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Lc.push("scrollEnd");function Le(t,e){Rd.set(t,e),xl(e,[t])}var Jp=new WeakMap;function we(t,e){if(typeof t=="object"&&t!==null){var n=Jp.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Lp(e)},Jp.set(t,e),e)}return{value:t,source:e,stack:Lp(e)}}var Se=[],ti=0,Cf=0;function Mu(){for(var t=ti,e=Cf=ti=0;e<t;){var n=Se[e];Se[e++]=null;var l=Se[e];Se[e++]=null;var i=Se[e];Se[e++]=null;var r=Se[e];if(Se[e++]=null,l!==null&&i!==null){var a=l.pending;a===null?i.next=i:(i.next=a.next,a.next=i),l.pending=i}r!==0&&Nd(n,i,r)}}function Ou(t,e,n,l){Se[ti++]=t,Se[ti++]=e,Se[ti++]=n,Se[ti++]=l,Cf|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Df(t,e,n,l){return Ou(t,e,n,l),nu(t)}function wi(t,e){return Ou(t,null,null,e),nu(t)}function Nd(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var i=!1,r=t.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(i=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,i&&e!==null&&(i=31-de(n),t=r.hiddenUpdates,l=t[i],l===null?t[i]=[e]:l.push(e),e.lane=n|536870912),r):null}function nu(t){if(50<br)throw br=0,nf=null,Error(w(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var ei={};function xv(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pe(t,e,n,l){return new xv(t,e,n,l)}function Mf(t){return t=t.prototype,!(!t||!t.isReactComponent)}function cn(t,e){var n=t.alternate;return n===null?(n=pe(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Ld(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ya(t,e,n,l,i,r){var a=0;if(l=t,typeof t=="function")Mf(t)&&(a=1);else if(typeof t=="string")a=xS(t,n,Ye.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Ec:return t=pe(31,n,e,i),t.elementType=Ec,t.lanes=r,t;case Kl:return ol(n.children,i,r,e);case td:a=8,i|=24;break;case vc:return t=pe(12,n,e,i|2),t.elementType=vc,t.lanes=r,t;case Sc:return t=pe(13,n,e,i),t.elementType=Sc,t.lanes=r,t;case kc:return t=pe(19,n,e,i),t.elementType=kc,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case ob:case ln:a=10;break t;case ed:a=9;break t;case gf:a=11;break t;case yf:a=14;break t;case Tn:a=16,l=null;break t}a=29,n=Error(w(130,t===null?"null":typeof t,"")),l=null}return e=pe(a,n,e,i),e.elementType=t,e.type=l,e.lanes=r,e}function ol(t,e,n,l){return t=pe(7,t,l,e),t.lanes=n,t}function tc(t,e,n){return t=pe(6,t,null,e),t.lanes=n,t}function ec(t,e,n){return e=pe(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ni=[],li=0,lu=null,iu=0,Ee=[],Te=0,cl=null,rn=1,an="";function rl(t,e){ni[li++]=iu,ni[li++]=lu,lu=t,iu=e}function Ud(t,e,n){Ee[Te++]=rn,Ee[Te++]=an,Ee[Te++]=cl,cl=t;var l=rn;t=an;var i=32-de(l)-1;l&=~(1<<i),n+=1;var r=32-de(e)+i;if(30<r){var a=i-i%5;r=(l&(1<<a)-1).toString(32),l>>=a,i-=a,rn=1<<32-de(e)+i|n<<i|l,an=r+t}else rn=1<<r|n<<i|l,an=t}function Of(t){t.return!==null&&(rl(t,1),Ud(t,1,0))}function _f(t){for(;t===lu;)lu=ni[--li],ni[li]=null,iu=ni[--li],ni[li]=null;for(;t===cl;)cl=Ee[--Te],Ee[Te]=null,an=Ee[--Te],Ee[Te]=null,rn=Ee[--Te],Ee[Te]=null}var Jt=null,yt=null,$=!1,fl=null,qe=!1,Uc=Error(w(519));function hl(t){var e=Error(w(418,""));throw Ar(we(e,t)),Uc}function Wp(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[Xt]=t,e[le]=l,n){case"dialog":K("cancel",e),K("close",e);break;case"iframe":case"object":case"embed":K("load",e);break;case"video":case"audio":for(n=0;n<Cr.length;n++)K(Cr[n],e);break;case"source":K("error",e);break;case"img":case"image":case"link":K("error",e),K("load",e);break;case"details":K("toggle",e);break;case"input":K("invalid",e),hd(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),tu(e);break;case"select":K("invalid",e);break;case"textarea":K("invalid",e),gd(e,l.value,l.defaultValue,l.children),tu(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||yy(e.textContent,n)?(l.popover!=null&&(K("beforetoggle",e),K("toggle",e)),l.onScroll!=null&&K("scroll",e),l.onScrollEnd!=null&&K("scrollend",e),l.onClick!=null&&(e.onclick=qu),e=!0):e=!1,e||hl(t)}function Pp(t){for(Jt=t.return;Jt;)switch(Jt.tag){case 5:case 13:qe=!1;return;case 27:case 3:qe=!0;return;default:Jt=Jt.return}}function Wi(t){if(t!==Jt)return!1;if(!$)return Pp(t),$=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||cf(t.type,t.memoizedProps)),n=!n),n&&yt&&hl(t),Pp(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(w(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){yt=Ne(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}yt=null}}else e===27?(e=yt,Zn(t.type)?(t=mf,mf=null,yt=t):yt=e):yt=Jt?Ne(t.stateNode.nextSibling):null;return!0}function Yr(){yt=Jt=null,$=!1}function $p(){var t=fl;return t!==null&&(ne===null?ne=t:ne.push.apply(ne,t),fl=null),t}function Ar(t){fl===null?fl=[t]:fl.push(t)}var Bc=Ge(null),Sl=null,un=null;function wn(t,e,n){pt(Bc,e._currentValue),e._currentValue=n}function fn(t){t._currentValue=Bc.current,Ut(Bc)}function Hc(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function qc(t,e,n,l){var i=t.child;for(i!==null&&(i.return=t);i!==null;){var r=i.dependencies;if(r!==null){var a=i.child;r=r.firstContext;t:for(;r!==null;){var u=r;r=i;for(var o=0;o<e.length;o++)if(u.context===e[o]){r.lanes|=n,u=r.alternate,u!==null&&(u.lanes|=n),Hc(r.return,n,t),l||(a=null);break t}r=u.next}}else if(i.tag===18){if(a=i.return,a===null)throw Error(w(341));a.lanes|=n,r=a.alternate,r!==null&&(r.lanes|=n),Hc(a,n,t),a=null}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}}function Vr(t,e,n,l){t=null;for(var i=e,r=!1;i!==null;){if(!r){if(i.flags&524288)r=!0;else if(i.flags&262144)break}if(i.tag===10){var a=i.alternate;if(a===null)throw Error(w(387));if(a=a.memoizedProps,a!==null){var u=i.type;xe(i.pendingProps.value,a.value)||(t!==null?t.push(u):t=[u])}}else if(i===Ja.current){if(a=i.alternate,a===null)throw Error(w(387));a.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(t!==null?t.push(Or):t=[Or])}i=i.return}t!==null&&qc(e,t,n,l),e.flags|=262144}function ru(t){for(t=t.firstContext;t!==null;){if(!xe(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function dl(t){Sl=t,un=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Gt(t){return Bd(Sl,t)}function za(t,e){return Sl===null&&dl(t),Bd(t,e)}function Bd(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},un===null){if(t===null)throw Error(w(308));un=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else un=un.next=e;return n}var bv=typeof AbortController!="undefined"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},vv=Mt.unstable_scheduleCallback,Sv=Mt.unstable_NormalPriority,Ct={$$typeof:ln,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Rf(){return{controller:new bv,data:new Map,refCount:0}}function Xr(t){t.refCount--,t.refCount===0&&vv(Sv,function(){t.controller.abort()})}var fr=null,jc=0,gi=0,oi=null;function kv(t,e){if(fr===null){var n=fr=[];jc=0,gi=ns(),oi={status:"pending",value:void 0,then:function(l){n.push(l)}}}return jc++,e.then(th,th),e}function th(){if(--jc===0&&fr!==null){oi!==null&&(oi.status="fulfilled");var t=fr;fr=null,gi=0,oi=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Ev(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var i=0;i<n.length;i++)(0,n[i])(e)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var eh=H.S;H.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&kv(t,e),eh!==null&&eh(t,e)};var sl=Ge(null);function Nf(){var t=sl.current;return t!==null?t:ot.pooledCache}function Va(t,e){e===null?pt(sl,sl.current):pt(sl,e.pool)}function Hd(){var t=Nf();return t===null?null:{parent:Ct._currentValue,pool:t}}var Gr=Error(w(460)),qd=Error(w(474)),_u=Error(w(542)),Yc={then:function(){}};function nh(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ca(){}function jd(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Ca,Ca),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ih(t),t;default:if(typeof e.status=="string")e.then(Ca,Ca);else{if(t=ot,t!==null&&100<t.shellSuspendCounter)throw Error(w(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var i=e;i.status="fulfilled",i.value=l}},function(l){if(e.status==="pending"){var i=e;i.status="rejected",i.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ih(t),t}throw sr=e,Gr}}var sr=null;function lh(){if(sr===null)throw Error(w(459));var t=sr;return sr=null,t}function ih(t){if(t===Gr||t===_u)throw Error(w(483))}var An=!1;function Lf(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Vc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Ln(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Un(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,nt&2){var i=l.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),l.pending=e,e=nu(t),Nd(t,null,n),e}return Ou(t,l,e,n),nu(t)}function mr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,od(t,n)}}function nc(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var a={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?i=r=a:r=r.next=a,n=n.next}while(n!==null);r===null?i=r=e:r=r.next=e}else i=r=e;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Xc=!1;function pr(){if(Xc){var t=oi;if(t!==null)throw t}}function hr(t,e,n,l){Xc=!1;var i=t.updateQueue;An=!1;var r=i.firstBaseUpdate,a=i.lastBaseUpdate,u=i.shared.pending;if(u!==null){i.shared.pending=null;var o=u,c=o.next;o.next=null,a===null?r=c:a.next=c,a=o;var f=t.alternate;f!==null&&(f=f.updateQueue,u=f.lastBaseUpdate,u!==a&&(u===null?f.firstBaseUpdate=c:u.next=c,f.lastBaseUpdate=o))}if(r!==null){var s=i.baseState;a=0,f=c=o=null,u=r;do{var p=u.lane&-536870913,m=p!==u.lane;if(m?(W&p)===p:(l&p)===p){p!==0&&p===gi&&(Xc=!0),f!==null&&(f=f.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});t:{var y=t,v=u;p=e;var T=n;switch(v.tag){case 1:if(y=v.payload,typeof y=="function"){s=y.call(T,s,p);break t}s=y;break t;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,p=typeof y=="function"?y.call(T,s,p):y,p==null)break t;s=ct({},s,p);break t;case 2:An=!0}}p=u.callback,p!==null&&(t.flags|=64,m&&(t.flags|=8192),m=i.callbacks,m===null?i.callbacks=[p]:m.push(p))}else m={lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},f===null?(c=f=m,o=s):f=f.next=m,a|=p;if(u=u.next,u===null){if(u=i.shared.pending,u===null)break;m=u,u=m.next,m.next=null,i.lastBaseUpdate=m,i.shared.pending=null}}while(!0);f===null&&(o=s),i.baseState=o,i.firstBaseUpdate=c,i.lastBaseUpdate=f,r===null&&(i.shared.lanes=0),Gn|=a,t.lanes=a,t.memoizedState=s}}function Yd(t,e){if(typeof t!="function")throw Error(w(191,t));t.call(e)}function Vd(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Yd(n[t],e)}var yi=Ge(null),au=Ge(0);function rh(t,e){t=hn,pt(au,t),pt(yi,e),hn=t|e.baseLanes}function Gc(){pt(au,hn),pt(yi,yi.current)}function Uf(){hn=au.current,Ut(yi),Ut(au)}var Vn=0,G=null,rt=null,wt=null,uu=!1,ci=!1,gl=!1,ou=0,wr=0,fi=null,Tv=0;function kt(){throw Error(w(321))}function Bf(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!xe(t[n],e[n]))return!1;return!0}function Hf(t,e,n,l,i,r){return Vn=r,G=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,H.H=t===null||t.memoizedState===null?bg:vg,gl=!1,r=n(l,i),gl=!1,ci&&(r=Gd(e,n,l,i)),Xd(t),r}function Xd(t){H.H=cu;var e=rt!==null&&rt.next!==null;if(Vn=0,wt=rt=G=null,uu=!1,wr=0,fi=null,e)throw Error(w(300));t===null||Lt||(t=t.dependencies,t!==null&&ru(t)&&(Lt=!0))}function Gd(t,e,n,l){G=t;var i=0;do{if(ci&&(fi=null),wr=0,ci=!1,25<=i)throw Error(w(301));if(i+=1,wt=rt=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}H.H=Ov,r=e(n,l)}while(ci);return r}function Av(){var t=H.H,e=t.useState()[0];return e=typeof e.then=="function"?Qr(e):e,t=t.useState()[0],(rt!==null?rt.memoizedState:null)!==t&&(G.flags|=1024),e}function qf(){var t=ou!==0;return ou=0,t}function jf(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function Yf(t){if(uu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}uu=!1}Vn=0,wt=rt=G=null,ci=!1,wr=ou=0,fi=null}function te(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return wt===null?G.memoizedState=wt=t:wt=wt.next=t,wt}function zt(){if(rt===null){var t=G.alternate;t=t!==null?t.memoizedState:null}else t=rt.next;var e=wt===null?G.memoizedState:wt.next;if(e!==null)wt=e,rt=t;else{if(t===null)throw G.alternate===null?Error(w(467)):Error(w(310));rt=t,t={memoizedState:rt.memoizedState,baseState:rt.baseState,baseQueue:rt.baseQueue,queue:rt.queue,next:null},wt===null?G.memoizedState=wt=t:wt=wt.next=t}return wt}function Vf(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Qr(t){var e=wr;return wr+=1,fi===null&&(fi=[]),t=jd(fi,t,e),e=G,(wt===null?e.memoizedState:wt.next)===null&&(e=e.alternate,H.H=e===null||e.memoizedState===null?bg:vg),t}function Ru(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Qr(t);if(t.$$typeof===ln)return Gt(t)}throw Error(w(438,String(t)))}function Xf(t){var e=null,n=G.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=G.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(i){return i.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=Vf(),G.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=cb;return e.index++,n}function mn(t,e){return typeof e=="function"?e(t):e}function Xa(t){var e=zt();return Gf(e,rt,t)}function Gf(t,e,n){var l=t.queue;if(l===null)throw Error(w(311));l.lastRenderedReducer=n;var i=t.baseQueue,r=l.pending;if(r!==null){if(i!==null){var a=i.next;i.next=r.next,r.next=a}e.baseQueue=i=r,l.pending=null}if(r=t.baseState,i===null)t.memoizedState=r;else{e=i.next;var u=a=null,o=null,c=e,f=!1;do{var s=c.lane&-536870913;if(s!==c.lane?(W&s)===s:(Vn&s)===s){var p=c.revertLane;if(p===0)o!==null&&(o=o.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),s===gi&&(f=!0);else if((Vn&p)===p){c=c.next,p===gi&&(f=!0);continue}else s={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},o===null?(u=o=s,a=r):o=o.next=s,G.lanes|=p,Gn|=p;s=c.action,gl&&n(r,s),r=c.hasEagerState?c.eagerState:n(r,s)}else p={lane:s,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},o===null?(u=o=p,a=r):o=o.next=p,G.lanes|=s,Gn|=s;c=c.next}while(c!==null&&c!==e);if(o===null?a=r:o.next=u,!xe(r,t.memoizedState)&&(Lt=!0,f&&(n=oi,n!==null)))throw n;t.memoizedState=r,t.baseState=a,t.baseQueue=o,l.lastRenderedState=r}return i===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function lc(t){var e=zt(),n=e.queue;if(n===null)throw Error(w(311));n.lastRenderedReducer=t;var l=n.dispatch,i=n.pending,r=e.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do r=t(r,a.action),a=a.next;while(a!==i);xe(r,e.memoizedState)||(Lt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,l]}function Qd(t,e,n){var l=G,i=zt(),r=$;if(r){if(n===void 0)throw Error(w(407));n=n()}else n=e();var a=!xe((rt||i).memoizedState,n);a&&(i.memoizedState=n,Lt=!0),i=i.queue;var u=Fd.bind(null,l,i,t);if(Zr(2048,8,u,[t]),i.getSnapshot!==e||a||wt!==null&&wt.memoizedState.tag&1){if(l.flags|=2048,xi(9,Nu(),Kd.bind(null,l,i,n,e),null),ot===null)throw Error(w(349));r||Vn&124||Zd(l,e,n)}return n}function Zd(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=G.updateQueue,e===null?(e=Vf(),G.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Kd(t,e,n,l){e.value=n,e.getSnapshot=l,Id(e)&&Jd(t)}function Fd(t,e,n){return n(function(){Id(e)&&Jd(t)})}function Id(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!xe(t,n)}catch(l){return!0}}function Jd(t){var e=wi(t,2);e!==null&&ye(e,t,2)}function Qc(t){var e=te();if(typeof t=="function"){var n=t;if(t=n(),gl){Mn(!0);try{n()}finally{Mn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:mn,lastRenderedState:t},e}function Wd(t,e,n,l){return t.baseState=n,Gf(t,rt,typeof l=="function"?l:mn)}function wv(t,e,n,l,i){if(Lu(t))throw Error(w(485));if(t=e.action,t!==null){var r={payload:i,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(a){r.listeners.push(a)}};H.T!==null?n(!0):r.isTransition=!1,l(r),n=e.pending,n===null?(r.next=e.pending=r,Pd(e,r)):(r.next=n.next,e.pending=n.next=r)}}function Pd(t,e){var n=e.action,l=e.payload,i=t.state;if(e.isTransition){var r=H.T,a={};H.T=a;try{var u=n(i,l),o=H.S;o!==null&&o(a,u),ah(t,e,u)}catch(c){Zc(t,e,c)}finally{H.T=r}}else try{r=n(i,l),ah(t,e,r)}catch(c){Zc(t,e,c)}}function ah(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){uh(t,e,l)},function(l){return Zc(t,e,l)}):uh(t,e,n)}function uh(t,e,n){e.status="fulfilled",e.value=n,$d(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,Pd(t,n)))}function Zc(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,$d(e),e=e.next;while(e!==l)}t.action=null}function $d(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function tg(t,e){return e}function oh(t,e){if($){var n=ot.formState;if(n!==null){t:{var l=G;if($){if(yt){e:{for(var i=yt,r=qe;i.nodeType!==8;){if(!r){i=null;break e}if(i=Ne(i.nextSibling),i===null){i=null;break e}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){yt=Ne(i.nextSibling),l=i.data==="F!";break t}}hl(l)}l=!1}l&&(e=n[0])}}return n=te(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:tg,lastRenderedState:e},n.queue=l,n=gg.bind(null,G,l),l.dispatch=n,l=Qc(!1),r=Ff.bind(null,G,!1,l.queue),l=te(),i={state:e,dispatch:null,action:t,pending:null},l.queue=i,n=wv.bind(null,G,i,r,n),i.dispatch=n,l.memoizedState=t,[e,n,!1]}function ch(t){var e=zt();return eg(e,rt,t)}function eg(t,e,n){if(e=Gf(t,e,tg)[0],t=Xa(mn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=Qr(e)}catch(a){throw a===Gr?_u:a}else l=e;e=zt();var i=e.queue,r=i.dispatch;return n!==e.memoizedState&&(G.flags|=2048,xi(9,Nu(),zv.bind(null,i,n),null)),[l,r,t]}function zv(t,e){t.action=e}function fh(t){var e=zt(),n=rt;if(n!==null)return eg(e,n,t);zt(),e=e.memoizedState,n=zt();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function xi(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=G.updateQueue,e===null&&(e=Vf(),G.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Nu(){return{destroy:void 0,resource:void 0}}function ng(){return zt().memoizedState}function Ga(t,e,n,l){var i=te();l=l===void 0?null:l,G.flags|=t,i.memoizedState=xi(1|e,Nu(),n,l)}function Zr(t,e,n,l){var i=zt();l=l===void 0?null:l;var r=i.memoizedState.inst;rt!==null&&l!==null&&Bf(l,rt.memoizedState.deps)?i.memoizedState=xi(e,r,n,l):(G.flags|=t,i.memoizedState=xi(1|e,r,n,l))}function sh(t,e){Ga(8390656,8,t,e)}function lg(t,e){Zr(2048,8,t,e)}function ig(t,e){return Zr(4,2,t,e)}function rg(t,e){return Zr(4,4,t,e)}function ag(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function ug(t,e,n){n=n!=null?n.concat([t]):null,Zr(4,4,ag.bind(null,e,t),n)}function Qf(){}function og(t,e){var n=zt();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Bf(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function cg(t,e){var n=zt();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Bf(e,l[1]))return l[0];if(l=t(),gl){Mn(!0);try{t()}finally{Mn(!1)}}return n.memoizedState=[l,e],l}function Zf(t,e,n){return n===void 0||Vn&1073741824?t.memoizedState=e:(t.memoizedState=n,t=$g(),G.lanes|=t,Gn|=t,n)}function fg(t,e,n,l){return xe(n,e)?n:yi.current!==null?(t=Zf(t,n,l),xe(t,e)||(Lt=!0),t):Vn&42?(t=$g(),G.lanes|=t,Gn|=t,e):(Lt=!0,t.memoizedState=n)}function sg(t,e,n,l,i){var r=tt.p;tt.p=r!==0&&8>r?r:8;var a=H.T,u={};H.T=u,Ff(t,!1,e,n);try{var o=i(),c=H.S;if(c!==null&&c(u,o),o!==null&&typeof o=="object"&&typeof o.then=="function"){var f=Ev(o,l);dr(t,e,f,ge(t))}else dr(t,e,l,ge(t))}catch(s){dr(t,e,{then:function(){},status:"rejected",reason:s},ge())}finally{tt.p=r,H.T=a}}function Cv(){}function Kc(t,e,n,l){if(t.tag!==5)throw Error(w(476));var i=mg(t).queue;sg(t,i,e,ul,n===null?Cv:function(){return pg(t),n(l)})}function mg(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:ul,baseState:ul,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:mn,lastRenderedState:ul},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:mn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function pg(t){var e=mg(t).next.queue;dr(t,e,{},ge())}function Kf(){return Gt(Or)}function hg(){return zt().memoizedState}function dg(){return zt().memoizedState}function Dv(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ge();t=Ln(n);var l=Un(e,t,n);l!==null&&(ye(l,e,n),mr(l,e,n)),e={cache:Rf()},t.payload=e;return}e=e.return}}function Mv(t,e,n){var l=ge();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Lu(t)?yg(e,n):(n=Df(t,e,n,l),n!==null&&(ye(n,t,l),xg(n,e,l)))}function gg(t,e,n){var l=ge();dr(t,e,n,l)}function dr(t,e,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Lu(t))yg(e,i);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var a=e.lastRenderedState,u=r(a,n);if(i.hasEagerState=!0,i.eagerState=u,xe(u,a))return Ou(t,e,i,0),ot===null&&Mu(),!1}catch(o){}finally{}if(n=Df(t,e,i,l),n!==null)return ye(n,t,l),xg(n,e,l),!0}return!1}function Ff(t,e,n,l){if(l={lane:2,revertLane:ns(),action:l,hasEagerState:!1,eagerState:null,next:null},Lu(t)){if(e)throw Error(w(479))}else e=Df(t,n,l,2),e!==null&&ye(e,t,2)}function Lu(t){var e=t.alternate;return t===G||e!==null&&e===G}function yg(t,e){ci=uu=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function xg(t,e,n){if(n&4194048){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,od(t,n)}}var cu={readContext:Gt,use:Ru,useCallback:kt,useContext:kt,useEffect:kt,useImperativeHandle:kt,useLayoutEffect:kt,useInsertionEffect:kt,useMemo:kt,useReducer:kt,useRef:kt,useState:kt,useDebugValue:kt,useDeferredValue:kt,useTransition:kt,useSyncExternalStore:kt,useId:kt,useHostTransitionStatus:kt,useFormState:kt,useActionState:kt,useOptimistic:kt,useMemoCache:kt,useCacheRefresh:kt},bg={readContext:Gt,use:Ru,useCallback:function(t,e){return te().memoizedState=[t,e===void 0?null:e],t},useContext:Gt,useEffect:sh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,Ga(4194308,4,ag.bind(null,e,t),n)},useLayoutEffect:function(t,e){return Ga(4194308,4,t,e)},useInsertionEffect:function(t,e){Ga(4,2,t,e)},useMemo:function(t,e){var n=te();e=e===void 0?null:e;var l=t();if(gl){Mn(!0);try{t()}finally{Mn(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=te();if(n!==void 0){var i=n(e);if(gl){Mn(!0);try{n(e)}finally{Mn(!1)}}}else i=e;return l.memoizedState=l.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},l.queue=t,t=t.dispatch=Mv.bind(null,G,t),[l.memoizedState,t]},useRef:function(t){var e=te();return t={current:t},e.memoizedState=t},useState:function(t){t=Qc(t);var e=t.queue,n=gg.bind(null,G,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:Qf,useDeferredValue:function(t,e){var n=te();return Zf(n,t,e)},useTransition:function(){var t=Qc(!1);return t=sg.bind(null,G,t.queue,!0,!1),te().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=G,i=te();if($){if(n===void 0)throw Error(w(407));n=n()}else{if(n=e(),ot===null)throw Error(w(349));W&124||Zd(l,e,n)}i.memoizedState=n;var r={value:n,getSnapshot:e};return i.queue=r,sh(Fd.bind(null,l,r,t),[t]),l.flags|=2048,xi(9,Nu(),Kd.bind(null,l,r,n,e),null),n},useId:function(){var t=te(),e=ot.identifierPrefix;if($){var n=an,l=rn;n=(l&~(1<<32-de(l)-1)).toString(32)+n,e="\xAB"+e+"R"+n,n=ou++,0<n&&(e+="H"+n.toString(32)),e+="\xBB"}else n=Tv++,e="\xAB"+e+"r"+n.toString(32)+"\xBB";return t.memoizedState=e},useHostTransitionStatus:Kf,useFormState:oh,useActionState:oh,useOptimistic:function(t){var e=te();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=Ff.bind(null,G,!0,n),n.dispatch=e,[t,e]},useMemoCache:Xf,useCacheRefresh:function(){return te().memoizedState=Dv.bind(null,G)}},vg={readContext:Gt,use:Ru,useCallback:og,useContext:Gt,useEffect:lg,useImperativeHandle:ug,useInsertionEffect:ig,useLayoutEffect:rg,useMemo:cg,useReducer:Xa,useRef:ng,useState:function(){return Xa(mn)},useDebugValue:Qf,useDeferredValue:function(t,e){var n=zt();return fg(n,rt.memoizedState,t,e)},useTransition:function(){var t=Xa(mn)[0],e=zt().memoizedState;return[typeof t=="boolean"?t:Qr(t),e]},useSyncExternalStore:Qd,useId:hg,useHostTransitionStatus:Kf,useFormState:ch,useActionState:ch,useOptimistic:function(t,e){var n=zt();return Wd(n,rt,t,e)},useMemoCache:Xf,useCacheRefresh:dg},Ov={readContext:Gt,use:Ru,useCallback:og,useContext:Gt,useEffect:lg,useImperativeHandle:ug,useInsertionEffect:ig,useLayoutEffect:rg,useMemo:cg,useReducer:lc,useRef:ng,useState:function(){return lc(mn)},useDebugValue:Qf,useDeferredValue:function(t,e){var n=zt();return rt===null?Zf(n,t,e):fg(n,rt.memoizedState,t,e)},useTransition:function(){var t=lc(mn)[0],e=zt().memoizedState;return[typeof t=="boolean"?t:Qr(t),e]},useSyncExternalStore:Qd,useId:hg,useHostTransitionStatus:Kf,useFormState:fh,useActionState:fh,useOptimistic:function(t,e){var n=zt();return rt!==null?Wd(n,rt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:Xf,useCacheRefresh:dg},si=null,zr=0;function Da(t){var e=zr;return zr+=1,si===null&&(si=[]),jd(si,t,e)}function Pi(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Ma(t,e){throw e.$$typeof===ub?Error(w(525)):(t=Object.prototype.toString.call(e),Error(w(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function mh(t){var e=t._init;return e(t._payload)}function Sg(t){function e(h,d){if(t){var g=h.deletions;g===null?(h.deletions=[d],h.flags|=16):g.push(d)}}function n(h,d){if(!t)return null;for(;d!==null;)e(h,d),d=d.sibling;return null}function l(h){for(var d=new Map;h!==null;)h.key!==null?d.set(h.key,h):d.set(h.index,h),h=h.sibling;return d}function i(h,d){return h=cn(h,d),h.index=0,h.sibling=null,h}function r(h,d,g){return h.index=g,t?(g=h.alternate,g!==null?(g=g.index,g<d?(h.flags|=67108866,d):g):(h.flags|=67108866,d)):(h.flags|=1048576,d)}function a(h){return t&&h.alternate===null&&(h.flags|=67108866),h}function u(h,d,g,E){return d===null||d.tag!==6?(d=tc(g,h.mode,E),d.return=h,d):(d=i(d,g),d.return=h,d)}function o(h,d,g,E){var C=g.type;return C===Kl?f(h,d,g.props.children,E,g.key):d!==null&&(d.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Tn&&mh(C)===d.type)?(d=i(d,g.props),Pi(d,g),d.return=h,d):(d=Ya(g.type,g.key,g.props,null,h.mode,E),Pi(d,g),d.return=h,d)}function c(h,d,g,E){return d===null||d.tag!==4||d.stateNode.containerInfo!==g.containerInfo||d.stateNode.implementation!==g.implementation?(d=ec(g,h.mode,E),d.return=h,d):(d=i(d,g.children||[]),d.return=h,d)}function f(h,d,g,E,C){return d===null||d.tag!==7?(d=ol(g,h.mode,E,C),d.return=h,d):(d=i(d,g),d.return=h,d)}function s(h,d,g){if(typeof d=="string"&&d!==""||typeof d=="number"||typeof d=="bigint")return d=tc(""+d,h.mode,g),d.return=h,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case va:return g=Ya(d.type,d.key,d.props,null,h.mode,g),Pi(g,d),g.return=h,g;case lr:return d=ec(d,h.mode,g),d.return=h,d;case Tn:var E=d._init;return d=E(d._payload),s(h,d,g)}if(ir(d)||Ii(d))return d=ol(d,h.mode,g,null),d.return=h,d;if(typeof d.then=="function")return s(h,Da(d),g);if(d.$$typeof===ln)return s(h,za(h,d),g);Ma(h,d)}return null}function p(h,d,g,E){var C=d!==null?d.key:null;if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return C!==null?null:u(h,d,""+g,E);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case va:return g.key===C?o(h,d,g,E):null;case lr:return g.key===C?c(h,d,g,E):null;case Tn:return C=g._init,g=C(g._payload),p(h,d,g,E)}if(ir(g)||Ii(g))return C!==null?null:f(h,d,g,E,null);if(typeof g.then=="function")return p(h,d,Da(g),E);if(g.$$typeof===ln)return p(h,d,za(h,g),E);Ma(h,g)}return null}function m(h,d,g,E,C){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return h=h.get(g)||null,u(d,h,""+E,C);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case va:return h=h.get(E.key===null?g:E.key)||null,o(d,h,E,C);case lr:return h=h.get(E.key===null?g:E.key)||null,c(d,h,E,C);case Tn:var k=E._init;return E=k(E._payload),m(h,d,g,E,C)}if(ir(E)||Ii(E))return h=h.get(g)||null,f(d,h,E,C,null);if(typeof E.then=="function")return m(h,d,g,Da(E),C);if(E.$$typeof===ln)return m(h,d,g,za(d,E),C);Ma(d,E)}return null}function y(h,d,g,E){for(var C=null,k=null,D=d,O=d=0,L=null;D!==null&&O<g.length;O++){D.index>O?(L=D,D=null):L=D.sibling;var S=p(h,D,g[O],E);if(S===null){D===null&&(D=L);break}t&&D&&S.alternate===null&&e(h,D),d=r(S,d,O),k===null?C=S:k.sibling=S,k=S,D=L}if(O===g.length)return n(h,D),$&&rl(h,O),C;if(D===null){for(;O<g.length;O++)D=s(h,g[O],E),D!==null&&(d=r(D,d,O),k===null?C=D:k.sibling=D,k=D);return $&&rl(h,O),C}for(D=l(D);O<g.length;O++)L=m(D,h,O,g[O],E),L!==null&&(t&&L.alternate!==null&&D.delete(L.key===null?O:L.key),d=r(L,d,O),k===null?C=L:k.sibling=L,k=L);return t&&D.forEach(function(P){return e(h,P)}),$&&rl(h,O),C}function v(h,d,g,E){if(g==null)throw Error(w(151));for(var C=null,k=null,D=d,O=d=0,L=null,S=g.next();D!==null&&!S.done;O++,S=g.next()){D.index>O?(L=D,D=null):L=D.sibling;var P=p(h,D,S.value,E);if(P===null){D===null&&(D=L);break}t&&D&&P.alternate===null&&e(h,D),d=r(P,d,O),k===null?C=P:k.sibling=P,k=P,D=L}if(S.done)return n(h,D),$&&rl(h,O),C;if(D===null){for(;!S.done;O++,S=g.next())S=s(h,S.value,E),S!==null&&(d=r(S,d,O),k===null?C=S:k.sibling=S,k=S);return $&&rl(h,O),C}for(D=l(D);!S.done;O++,S=g.next())S=m(D,h,O,S.value,E),S!==null&&(t&&S.alternate!==null&&D.delete(S.key===null?O:S.key),d=r(S,d,O),k===null?C=S:k.sibling=S,k=S);return t&&D.forEach(function(Q){return e(h,Q)}),$&&rl(h,O),C}function T(h,d,g,E){if(typeof g=="object"&&g!==null&&g.type===Kl&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case va:t:{for(var C=g.key;d!==null;){if(d.key===C){if(C=g.type,C===Kl){if(d.tag===7){n(h,d.sibling),E=i(d,g.props.children),E.return=h,h=E;break t}}else if(d.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Tn&&mh(C)===d.type){n(h,d.sibling),E=i(d,g.props),Pi(E,g),E.return=h,h=E;break t}n(h,d);break}else e(h,d);d=d.sibling}g.type===Kl?(E=ol(g.props.children,h.mode,E,g.key),E.return=h,h=E):(E=Ya(g.type,g.key,g.props,null,h.mode,E),Pi(E,g),E.return=h,h=E)}return a(h);case lr:t:{for(C=g.key;d!==null;){if(d.key===C)if(d.tag===4&&d.stateNode.containerInfo===g.containerInfo&&d.stateNode.implementation===g.implementation){n(h,d.sibling),E=i(d,g.children||[]),E.return=h,h=E;break t}else{n(h,d);break}else e(h,d);d=d.sibling}E=ec(g,h.mode,E),E.return=h,h=E}return a(h);case Tn:return C=g._init,g=C(g._payload),T(h,d,g,E)}if(ir(g))return y(h,d,g,E);if(Ii(g)){if(C=Ii(g),typeof C!="function")throw Error(w(150));return g=C.call(g),v(h,d,g,E)}if(typeof g.then=="function")return T(h,d,Da(g),E);if(g.$$typeof===ln)return T(h,d,za(h,g),E);Ma(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint"?(g=""+g,d!==null&&d.tag===6?(n(h,d.sibling),E=i(d,g),E.return=h,h=E):(n(h,d),E=tc(g,h.mode,E),E.return=h,h=E),a(h)):n(h,d)}return function(h,d,g,E){try{zr=0;var C=T(h,d,g,E);return si=null,C}catch(D){if(D===Gr||D===_u)throw D;var k=pe(29,D,null,h.mode);return k.lanes=E,k.return=h,k}finally{}}}var bi=Sg(!0),kg=Sg(!1),Ce=Ge(null),Xe=null;function zn(t){var e=t.alternate;pt(Dt,Dt.current&1),pt(Ce,t),Xe===null&&(e===null||yi.current!==null||e.memoizedState!==null)&&(Xe=t)}function Eg(t){if(t.tag===22){if(pt(Dt,Dt.current),pt(Ce,t),Xe===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Xe=t)}}else Cn(t)}function Cn(){pt(Dt,Dt.current),pt(Ce,Ce.current)}function on(t){Ut(Ce),Xe===t&&(Xe=null),Ut(Dt)}var Dt=Ge(0);function fu(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||sf(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function ic(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:ct({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Fc={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=ge(),i=Ln(l);i.payload=e,n!=null&&(i.callback=n),e=Un(t,i,l),e!==null&&(ye(e,t,l),mr(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=ge(),i=Ln(l);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=Un(t,i,l),e!==null&&(ye(e,t,l),mr(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ge(),l=Ln(n);l.tag=2,e!=null&&(l.callback=e),e=Un(t,l,n),e!==null&&(ye(e,t,n),mr(e,t,n))}};function ph(t,e,n,l,i,r,a){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,r,a):e.prototype&&e.prototype.isPureReactComponent?!Tr(n,l)||!Tr(i,r):!0}function hh(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&Fc.enqueueReplaceState(e,e.state,null)}function yl(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=ct({},n));for(var i in t)n[i]===void 0&&(n[i]=t[i])}return n}var su=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Tg(t){su(t)}function Ag(t){console.error(t)}function wg(t){su(t)}function mu(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function dh(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function Ic(t,e,n){return n=Ln(n),n.tag=3,n.payload={element:null},n.callback=function(){mu(t,e)},n}function zg(t){return t=Ln(t),t.tag=3,t}function Cg(t,e,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var r=l.value;t.payload=function(){return i(r)},t.callback=function(){dh(e,n,l)}}var a=n.stateNode;a!==null&&typeof a.componentDidCatch=="function"&&(t.callback=function(){dh(e,n,l),typeof i!="function"&&(Bn===null?Bn=new Set([this]):Bn.add(this));var u=l.stack;this.componentDidCatch(l.value,{componentStack:u!==null?u:""})})}function _v(t,e,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&Vr(e,n,i,!0),n=Ce.current,n!==null){switch(n.tag){case 13:return Xe===null?lf():n.alternate===null&&xt===0&&(xt=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===Yc?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),dc(t,l,i)),!1;case 22:return n.flags|=65536,l===Yc?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),dc(t,l,i)),!1}throw Error(w(435,n.tag))}return dc(t,l,i),lf(),!1}if($)return e=Ce.current,e!==null?(!(e.flags&65536)&&(e.flags|=256),e.flags|=65536,e.lanes=i,l!==Uc&&(t=Error(w(422),{cause:l}),Ar(we(t,n)))):(l!==Uc&&(e=Error(w(423),{cause:l}),Ar(we(e,n))),t=t.current.alternate,t.flags|=65536,i&=-i,t.lanes|=i,l=we(l,n),i=Ic(t.stateNode,l,i),nc(t,i),xt!==4&&(xt=2)),!1;var r=Error(w(520),{cause:l});if(r=we(r,n),xr===null?xr=[r]:xr.push(r),xt!==4&&(xt=2),e===null)return!0;l=we(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=i&-i,n.lanes|=t,t=Ic(n.stateNode,l,t),nc(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Bn===null||!Bn.has(r))))return n.flags|=65536,i&=-i,n.lanes|=i,i=zg(i),Cg(i,t,n,l),nc(n,i),!1}n=n.return}while(n!==null);return!1}var Dg=Error(w(461)),Lt=!1;function qt(t,e,n,l){e.child=t===null?kg(e,null,n,l):bi(e,t.child,n,l)}function gh(t,e,n,l,i){n=n.render;var r=e.ref;if("ref"in l){var a={};for(var u in l)u!=="ref"&&(a[u]=l[u])}else a=l;return dl(e),l=Hf(t,e,n,a,r,i),u=qf(),t!==null&&!Lt?(jf(t,e,i),pn(t,e,i)):($&&u&&Of(e),e.flags|=1,qt(t,e,l,i),e.child)}function yh(t,e,n,l,i){if(t===null){var r=n.type;return typeof r=="function"&&!Mf(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,Mg(t,e,r,l,i)):(t=Ya(n.type,null,l,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!If(t,i)){var a=r.memoizedProps;if(n=n.compare,n=n!==null?n:Tr,n(a,l)&&t.ref===e.ref)return pn(t,e,i)}return e.flags|=1,t=cn(r,l),t.ref=e.ref,t.return=e,e.child=t}function Mg(t,e,n,l,i){if(t!==null){var r=t.memoizedProps;if(Tr(r,l)&&t.ref===e.ref)if(Lt=!1,e.pendingProps=l=r,If(t,i))t.flags&131072&&(Lt=!0);else return e.lanes=t.lanes,pn(t,e,i)}return Jc(t,e,n,l,i)}function Og(t,e,n){var l=e.pendingProps,i=l.children,r=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if(e.flags&128){if(l=r!==null?r.baseLanes|n:n,t!==null){for(i=e.child=t.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;e.childLanes=r&~l}else e.childLanes=0,e.child=null;return xh(t,e,l,n)}if(n&536870912)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Va(e,r!==null?r.cachePool:null),r!==null?rh(e,r):Gc(),Eg(e);else return e.lanes=e.childLanes=536870912,xh(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(Va(e,r.cachePool),rh(e,r),Cn(e),e.memoizedState=null):(t!==null&&Va(e,null),Gc(),Cn(e));return qt(t,e,i,n),e.child}function xh(t,e,n,l){var i=Nf();return i=i===null?null:{parent:Ct._currentValue,pool:i},e.memoizedState={baseLanes:n,cachePool:i},t!==null&&Va(e,null),Gc(),Eg(e),t!==null&&Vr(t,e,l,!0),null}function Qa(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(w(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Jc(t,e,n,l,i){return dl(e),n=Hf(t,e,n,l,void 0,i),l=qf(),t!==null&&!Lt?(jf(t,e,i),pn(t,e,i)):($&&l&&Of(e),e.flags|=1,qt(t,e,n,i),e.child)}function bh(t,e,n,l,i,r){return dl(e),e.updateQueue=null,n=Gd(e,l,n,i),Xd(t),l=qf(),t!==null&&!Lt?(jf(t,e,r),pn(t,e,r)):($&&l&&Of(e),e.flags|=1,qt(t,e,n,r),e.child)}function vh(t,e,n,l,i){if(dl(e),e.stateNode===null){var r=ei,a=n.contextType;typeof a=="object"&&a!==null&&(r=Gt(a)),r=new n(l,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Fc,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=l,r.state=e.memoizedState,r.refs={},Lf(e),a=n.contextType,r.context=typeof a=="object"&&a!==null?Gt(a):ei,r.state=e.memoizedState,a=n.getDerivedStateFromProps,typeof a=="function"&&(ic(e,n,a,l),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(a=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),a!==r.state&&Fc.enqueueReplaceState(r,r.state,null),hr(e,l,r,i),pr(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){r=e.stateNode;var u=e.memoizedProps,o=yl(n,u);r.props=o;var c=r.context,f=n.contextType;a=ei,typeof f=="object"&&f!==null&&(a=Gt(f));var s=n.getDerivedStateFromProps;f=typeof s=="function"||typeof r.getSnapshotBeforeUpdate=="function",u=e.pendingProps!==u,f||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(u||c!==a)&&hh(e,r,l,a),An=!1;var p=e.memoizedState;r.state=p,hr(e,l,r,i),pr(),c=e.memoizedState,u||p!==c||An?(typeof s=="function"&&(ic(e,n,s,l),c=e.memoizedState),(o=An||ph(e,n,o,l,p,c,a))?(f||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=c),r.props=l,r.state=c,r.context=a,l=o):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{r=e.stateNode,Vc(t,e),a=e.memoizedProps,f=yl(n,a),r.props=f,s=e.pendingProps,p=r.context,c=n.contextType,o=ei,typeof c=="object"&&c!==null&&(o=Gt(c)),u=n.getDerivedStateFromProps,(c=typeof u=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(a!==s||p!==o)&&hh(e,r,l,o),An=!1,p=e.memoizedState,r.state=p,hr(e,l,r,i),pr();var m=e.memoizedState;a!==s||p!==m||An||t!==null&&t.dependencies!==null&&ru(t.dependencies)?(typeof u=="function"&&(ic(e,n,u,l),m=e.memoizedState),(f=An||ph(e,n,f,l,p,m,o)||t!==null&&t.dependencies!==null&&ru(t.dependencies))?(c||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,m,o),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,m,o)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=m),r.props=l,r.state=m,r.context=o,l=f):(typeof r.componentDidUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),l=!1)}return r=l,Qa(t,e),l=(e.flags&128)!==0,r||l?(r=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&l?(e.child=bi(e,t.child,null,i),e.child=bi(e,null,n,i)):qt(t,e,n,i),e.memoizedState=r.state,t=e.child):t=pn(t,e,i),t}function Sh(t,e,n,l){return Yr(),e.flags|=256,qt(t,e,n,l),e.child}var rc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function ac(t){return{baseLanes:t,cachePool:Hd()}}function uc(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=ze),t}function _g(t,e,n){var l=e.pendingProps,i=!1,r=(e.flags&128)!==0,a;if((a=r)||(a=t!==null&&t.memoizedState===null?!1:(Dt.current&2)!==0),a&&(i=!0,e.flags&=-129),a=(e.flags&32)!==0,e.flags&=-33,t===null){if($){if(i?zn(e):Cn(e),$){var u=yt,o;if(o=u){t:{for(o=u,u=qe;o.nodeType!==8;){if(!u){u=null;break t}if(o=Ne(o.nextSibling),o===null){u=null;break t}}u=o}u!==null?(e.memoizedState={dehydrated:u,treeContext:cl!==null?{id:rn,overflow:an}:null,retryLane:536870912,hydrationErrors:null},o=pe(18,null,null,0),o.stateNode=u,o.return=e,e.child=o,Jt=e,yt=null,o=!0):o=!1}o||hl(e)}if(u=e.memoizedState,u!==null&&(u=u.dehydrated,u!==null))return sf(u)?e.lanes=32:e.lanes=536870912,null;on(e)}return u=l.children,l=l.fallback,i?(Cn(e),i=e.mode,u=pu({mode:"hidden",children:u},i),l=ol(l,i,n,null),u.return=e,l.return=e,u.sibling=l,e.child=u,i=e.child,i.memoizedState=ac(n),i.childLanes=uc(t,a,n),e.memoizedState=rc,l):(zn(e),Wc(e,u))}if(o=t.memoizedState,o!==null&&(u=o.dehydrated,u!==null)){if(r)e.flags&256?(zn(e),e.flags&=-257,e=oc(t,e,n)):e.memoizedState!==null?(Cn(e),e.child=t.child,e.flags|=128,e=null):(Cn(e),i=l.fallback,u=e.mode,l=pu({mode:"visible",children:l.children},u),i=ol(i,u,n,null),i.flags|=2,l.return=e,i.return=e,l.sibling=i,e.child=l,bi(e,t.child,null,n),l=e.child,l.memoizedState=ac(n),l.childLanes=uc(t,a,n),e.memoizedState=rc,e=i);else if(zn(e),sf(u)){if(a=u.nextSibling&&u.nextSibling.dataset,a)var c=a.dgst;a=c,l=Error(w(419)),l.stack="",l.digest=a,Ar({value:l,source:null,stack:null}),e=oc(t,e,n)}else if(Lt||Vr(t,e,n,!1),a=(n&t.childLanes)!==0,Lt||a){if(a=ot,a!==null&&(l=n&-n,l=l&42?1:bf(l),l=l&(a.suspendedLanes|n)?0:l,l!==0&&l!==o.retryLane))throw o.retryLane=l,wi(t,l),ye(a,t,l),Dg;u.data==="$?"||lf(),e=oc(t,e,n)}else u.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=o.treeContext,yt=Ne(u.nextSibling),Jt=e,$=!0,fl=null,qe=!1,t!==null&&(Ee[Te++]=rn,Ee[Te++]=an,Ee[Te++]=cl,rn=t.id,an=t.overflow,cl=e),e=Wc(e,l.children),e.flags|=4096);return e}return i?(Cn(e),i=l.fallback,u=e.mode,o=t.child,c=o.sibling,l=cn(o,{mode:"hidden",children:l.children}),l.subtreeFlags=o.subtreeFlags&65011712,c!==null?i=cn(c,i):(i=ol(i,u,n,null),i.flags|=2),i.return=e,l.return=e,l.sibling=i,e.child=l,l=i,i=e.child,u=t.child.memoizedState,u===null?u=ac(n):(o=u.cachePool,o!==null?(c=Ct._currentValue,o=o.parent!==c?{parent:c,pool:c}:o):o=Hd(),u={baseLanes:u.baseLanes|n,cachePool:o}),i.memoizedState=u,i.childLanes=uc(t,a,n),e.memoizedState=rc,l):(zn(e),n=t.child,t=n.sibling,n=cn(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(a=e.deletions,a===null?(e.deletions=[t],e.flags|=16):a.push(t)),e.child=n,e.memoizedState=null,n)}function Wc(t,e){return e=pu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function pu(t,e){return t=pe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function oc(t,e,n){return bi(e,t.child,null,n),t=Wc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function kh(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Hc(t.return,e,n)}function cc(t,e,n,l,i){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=i)}function Rg(t,e,n){var l=e.pendingProps,i=l.revealOrder,r=l.tail;if(qt(t,e,l.children,n),l=Dt.current,l&2)l=l&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&kh(t,n,e);else if(t.tag===19)kh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(pt(Dt,l),i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&fu(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),cc(e,!1,i,n,r);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&fu(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}cc(e,!0,n,null,r);break;case"together":cc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function pn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Gn|=e.lanes,!(n&e.childLanes))if(t!==null){if(Vr(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(w(153));if(e.child!==null){for(t=e.child,n=cn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=cn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function If(t,e){return t.lanes&e?!0:(t=t.dependencies,!!(t!==null&&ru(t)))}function Rv(t,e,n){switch(e.tag){case 3:Wa(e,e.stateNode.containerInfo),wn(e,Ct,t.memoizedState.cache),Yr();break;case 27:case 5:wc(e);break;case 4:Wa(e,e.stateNode.containerInfo);break;case 10:wn(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(zn(e),e.flags|=128,null):n&e.child.childLanes?_g(t,e,n):(zn(e),t=pn(t,e,n),t!==null?t.sibling:null);zn(e);break;case 19:var i=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(Vr(t,e,n,!1),l=(n&e.childLanes)!==0),i){if(l)return Rg(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),pt(Dt,Dt.current),l)break;return null;case 22:case 23:return e.lanes=0,Og(t,e,n);case 24:wn(e,Ct,t.memoizedState.cache)}return pn(t,e,n)}function Ng(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Lt=!0;else{if(!If(t,n)&&!(e.flags&128))return Lt=!1,Rv(t,e,n);Lt=!!(t.flags&131072)}else Lt=!1,$&&e.flags&1048576&&Ud(e,iu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,i=l._init;if(l=i(l._payload),e.type=l,typeof l=="function")Mf(l)?(t=yl(l,t),e.tag=1,e=vh(null,e,l,t,n)):(e.tag=0,e=Jc(null,e,l,t,n));else{if(l!=null){if(i=l.$$typeof,i===gf){e.tag=11,e=gh(null,e,l,t,n);break t}else if(i===yf){e.tag=14,e=yh(null,e,l,t,n);break t}}throw e=Tc(l)||l,Error(w(306,e,""))}}return e;case 0:return Jc(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,i=yl(l,e.pendingProps),vh(t,e,l,i,n);case 3:t:{if(Wa(e,e.stateNode.containerInfo),t===null)throw Error(w(387));l=e.pendingProps;var r=e.memoizedState;i=r.element,Vc(t,e),hr(e,l,null,n);var a=e.memoizedState;if(l=a.cache,wn(e,Ct,l),l!==r.cache&&qc(e,[Ct],n,!0),pr(),l=a.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:a.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=Sh(t,e,l,n);break t}else if(l!==i){i=we(Error(w(424)),e),Ar(i),e=Sh(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(yt=Ne(t.firstChild),Jt=e,$=!0,fl=null,qe=!0,n=kg(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Yr(),l===i){e=pn(t,e,n);break t}qt(t,e,l,n)}e=e.child}return e;case 26:return Qa(t,e),t===null?(n=Yh(e.type,null,e.pendingProps,null))?e.memoizedState=n:$||(n=e.type,t=e.pendingProps,l=vu(Nn.current).createElement(n),l[Xt]=e,l[le]=t,Yt(l,n,t),Nt(l),e.stateNode=l):e.memoizedState=Yh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return wc(e),t===null&&$&&(l=e.stateNode=vy(e.type,e.pendingProps,Nn.current),Jt=e,qe=!0,i=yt,Zn(e.type)?(mf=i,yt=Ne(l.firstChild)):yt=i),qt(t,e,e.pendingProps.children,n),Qa(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&$&&((i=l=yt)&&(l=rS(l,e.type,e.pendingProps,qe),l!==null?(e.stateNode=l,Jt=e,yt=Ne(l.firstChild),qe=!1,i=!0):i=!1),i||hl(e)),wc(e),i=e.type,r=e.pendingProps,a=t!==null?t.memoizedProps:null,l=r.children,cf(i,r)?l=null:a!==null&&cf(i,a)&&(e.flags|=32),e.memoizedState!==null&&(i=Hf(t,e,Av,null,null,n),Or._currentValue=i),Qa(t,e),qt(t,e,l,n),e.child;case 6:return t===null&&$&&((t=n=yt)&&(n=aS(n,e.pendingProps,qe),n!==null?(e.stateNode=n,Jt=e,yt=null,t=!0):t=!1),t||hl(e)),null;case 13:return _g(t,e,n);case 4:return Wa(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=bi(e,null,l,n):qt(t,e,l,n),e.child;case 11:return gh(t,e,e.type,e.pendingProps,n);case 7:return qt(t,e,e.pendingProps,n),e.child;case 8:return qt(t,e,e.pendingProps.children,n),e.child;case 12:return qt(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,wn(e,e.type,l.value),qt(t,e,l.children,n),e.child;case 9:return i=e.type._context,l=e.pendingProps.children,dl(e),i=Gt(i),l=l(i),e.flags|=1,qt(t,e,l,n),e.child;case 14:return yh(t,e,e.type,e.pendingProps,n);case 15:return Mg(t,e,e.type,e.pendingProps,n);case 19:return Rg(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=pu(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=cn(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Og(t,e,n);case 24:return dl(e),l=Gt(Ct),t===null?(i=Nf(),i===null&&(i=ot,r=Rf(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=n),i=r),e.memoizedState={parent:l,cache:i},Lf(e),wn(e,Ct,i)):(t.lanes&n&&(Vc(t,e),hr(e,null,null,n),pr()),i=t.memoizedState,r=e.memoizedState,i.parent!==l?(i={parent:l,cache:l},e.memoizedState=i,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=i),wn(e,Ct,l)):(l=r.cache,wn(e,Ct,l),l!==i.cache&&qc(e,[Ct],n,!0))),qt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(w(156,e.tag))}function tn(t){t.flags|=4}function Eh(t,e){if(e.type!=="stylesheet"||e.state.loading&4)t.flags&=-16777217;else if(t.flags|=16777216,!Ey(e)){if(e=Ce.current,e!==null&&((W&4194048)===W?Xe!==null:(W&62914560)!==W&&!(W&536870912)||e!==Xe))throw sr=Yc,qd;t.flags|=8192}}function Oa(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?ad():536870912,t.lanes|=e,vi|=e)}function $i(t,e){if(!$)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function dt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function Nv(t,e,n){var l=e.pendingProps;switch(_f(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return dt(e),null;case 1:return dt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),fn(Ct),pi(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Wi(e)?tn(e):t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,$p())),dt(e),null;case 26:return n=e.memoizedState,t===null?(tn(e),n!==null?(dt(e),Eh(e,n)):(dt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(tn(e),dt(e),Eh(e,n)):(dt(e),e.flags&=-16777217):(t.memoizedProps!==l&&tn(e),dt(e),e.flags&=-16777217),null;case 27:Pa(e),n=Nn.current;var i=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&tn(e);else{if(!l){if(e.stateNode===null)throw Error(w(166));return dt(e),null}t=Ye.current,Wi(e)?Wp(e,t):(t=vy(i,l,n),e.stateNode=t,tn(e))}return dt(e),null;case 5:if(Pa(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&tn(e);else{if(!l){if(e.stateNode===null)throw Error(w(166));return dt(e),null}if(t=Ye.current,Wi(e))Wp(e,t);else{switch(i=vu(Nn.current),t){case 1:t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}t[Xt]=e,t[le]=l;t:for(i=e.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===e)break t;for(;i.sibling===null;){if(i.return===null||i.return===e)break t;i=i.return}i.sibling.return=i.return,i=i.sibling}e.stateNode=t;t:switch(Yt(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&tn(e)}}return dt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&tn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(w(166));if(t=Nn.current,Wi(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,i=Jt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}t[Xt]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||yy(t.nodeValue,n)),t||hl(e)}else t=vu(t).createTextNode(l),t[Xt]=e,e.stateNode=t}return dt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(i=Wi(e),l!==null&&l.dehydrated!==null){if(t===null){if(!i)throw Error(w(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(w(317));i[Xt]=e}else Yr(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;dt(e),i=!1}else i=$p(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=i),i=!0;if(!i)return e.flags&256?(on(e),e):(on(e),null)}if(on(e),e.flags&128)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==i&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Oa(e,e.updateQueue),dt(e),null;case 4:return pi(),t===null&&ls(e.stateNode.containerInfo),dt(e),null;case 10:return fn(e.type),dt(e),null;case 19:if(Ut(Dt),i=e.memoizedState,i===null)return dt(e),null;if(l=(e.flags&128)!==0,r=i.rendering,r===null)if(l)$i(i,!1);else{if(xt!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(r=fu(t),r!==null){for(e.flags|=128,$i(i,!1),t=r.updateQueue,e.updateQueue=t,Oa(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Ld(n,t),n=n.sibling;return pt(Dt,Dt.current&1|2),e.child}t=t.sibling}i.tail!==null&&Ve()>du&&(e.flags|=128,l=!0,$i(i,!1),e.lanes=4194304)}else{if(!l)if(t=fu(r),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Oa(e,t),$i(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!$)return dt(e),null}else 2*Ve()-i.renderingStartTime>du&&n!==536870912&&(e.flags|=128,l=!0,$i(i,!1),e.lanes=4194304);i.isBackwards?(r.sibling=e.child,e.child=r):(t=i.last,t!==null?t.sibling=r:e.child=r,i.last=r)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=Ve(),e.sibling=null,t=Dt.current,pt(Dt,l?t&1|2:t&1),e):(dt(e),null);case 22:case 23:return on(e),Uf(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?n&536870912&&!(e.flags&128)&&(dt(e),e.subtreeFlags&6&&(e.flags|=8192)):dt(e),n=e.updateQueue,n!==null&&Oa(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&Ut(sl),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),fn(Ct),dt(e),null;case 25:return null;case 30:return null}throw Error(w(156,e.tag))}function Lv(t,e){switch(_f(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return fn(Ct),pi(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Pa(e),null;case 13:if(on(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(w(340));Yr()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Ut(Dt),null;case 4:return pi(),null;case 10:return fn(e.type),null;case 22:case 23:return on(e),Uf(),t!==null&&Ut(sl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return fn(Ct),null;case 25:return null;default:return null}}function Lg(t,e){switch(_f(e),e.tag){case 3:fn(Ct),pi();break;case 26:case 27:case 5:Pa(e);break;case 4:pi();break;case 13:on(e);break;case 19:Ut(Dt);break;case 10:fn(e.type);break;case 22:case 23:on(e),Uf(),t!==null&&Ut(sl);break;case 24:fn(Ct)}}function Kr(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&t)===t){l=void 0;var r=n.create,a=n.inst;l=r(),a.destroy=l}n=n.next}while(n!==i)}}catch(u){ut(e,e.return,u)}}function Xn(t,e,n){try{var l=e.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var r=i.next;l=r;do{if((l.tag&t)===t){var a=l.inst,u=a.destroy;if(u!==void 0){a.destroy=void 0,i=e;var o=n,c=u;try{c()}catch(f){ut(i,o,f)}}}l=l.next}while(l!==r)}}catch(f){ut(e,e.return,f)}}function Ug(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Vd(e,n)}catch(l){ut(t,t.return,l)}}}function Bg(t,e,n){n.props=yl(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){ut(t,e,l)}}function gr(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(i){ut(t,e,i)}}function je(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){ut(t,e,i)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){ut(t,e,i)}else n.current=null}function Hg(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){ut(t,t.return,i)}}function fc(t,e,n){try{var l=t.stateNode;tS(l,t.type,n,e),l[le]=e}catch(i){ut(t,t.return,i)}}function qg(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Zn(t.type)||t.tag===4}function sc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||qg(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Zn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Pc(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=qu));else if(l!==4&&(l===27&&Zn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(Pc(t,e,n),t=t.sibling;t!==null;)Pc(t,e,n),t=t.sibling}function hu(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&Zn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(hu(t,e,n),t=t.sibling;t!==null;)hu(t,e,n),t=t.sibling}function jg(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,i=e.attributes;i.length;)e.removeAttributeNode(i[0]);Yt(e,l,n),e[Xt]=t,e[le]=n}catch(r){ut(t,t.return,r)}}var nn=!1,Et=!1,mc=!1,Th=typeof WeakSet=="function"?WeakSet:Set,Rt=null;function Uv(t,e){if(t=t.containerInfo,uf=Tu,t=zd(t),zf(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch(v){n=null;break t}var a=0,u=-1,o=-1,c=0,f=0,s=t,p=null;e:for(;;){for(var m;s!==n||i!==0&&s.nodeType!==3||(u=a+i),s!==r||l!==0&&s.nodeType!==3||(o=a+l),s.nodeType===3&&(a+=s.nodeValue.length),(m=s.firstChild)!==null;)p=s,s=m;for(;;){if(s===t)break e;if(p===n&&++c===i&&(u=a),p===r&&++f===l&&(o=a),(m=s.nextSibling)!==null)break;s=p,p=s.parentNode}s=m}n=u===-1||o===-1?null:{start:u,end:o}}else n=null}n=n||{start:0,end:0}}else n=null;for(of={focusedElem:t,selectionRange:n},Tu=!1,Rt=e;Rt!==null;)if(e=Rt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Rt=t;else for(;Rt!==null;){switch(e=Rt,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if(t&1024&&r!==null){t=void 0,n=e,i=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var y=yl(n.type,i,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(y,r),l.__reactInternalSnapshotBeforeUpdate=t}catch(v){ut(n,n.return,v)}}break;case 3:if(t&1024){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)ff(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":ff(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(t&1024)throw Error(w(163))}if(t=e.sibling,t!==null){t.return=e.return,Rt=t;break}Rt=e.return}}function Yg(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:kn(t,n),l&4&&Kr(5,n);break;case 1:if(kn(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(a){ut(n,n.return,a)}else{var i=yl(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(a){ut(n,n.return,a)}}l&64&&Ug(n),l&512&&gr(n,n.return);break;case 3:if(kn(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Vd(t,e)}catch(a){ut(n,n.return,a)}}break;case 27:e===null&&l&4&&jg(n);case 26:case 5:kn(t,n),e===null&&l&4&&Hg(n),l&512&&gr(n,n.return);break;case 12:kn(t,n);break;case 13:kn(t,n),l&4&&Gg(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=Qv.bind(null,n),uS(t,n))));break;case 22:if(l=n.memoizedState!==null||nn,!l){e=e!==null&&e.memoizedState!==null||Et,i=nn;var r=Et;nn=l,(Et=e)&&!r?En(t,n,(n.subtreeFlags&8772)!==0):kn(t,n),nn=i,Et=r}break;case 30:break;default:kn(t,n)}}function Vg(t){var e=t.alternate;e!==null&&(t.alternate=null,Vg(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Sf(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var mt=null,ee=!1;function en(t,e,n){for(n=n.child;n!==null;)Xg(t,e,n),n=n.sibling}function Xg(t,e,n){if(he&&typeof he.onCommitFiberUnmount=="function")try{he.onCommitFiberUnmount(Ur,n)}catch(r){}switch(n.tag){case 26:Et||je(n,e),en(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Et||je(n,e);var l=mt,i=ee;Zn(n.type)&&(mt=n.stateNode,ee=!1),en(t,e,n),vr(n.stateNode),mt=l,ee=i;break;case 5:Et||je(n,e);case 6:if(l=mt,i=ee,mt=null,en(t,e,n),mt=l,ee=i,mt!==null)if(ee)try{(mt.nodeType===9?mt.body:mt.nodeName==="HTML"?mt.ownerDocument.body:mt).removeChild(n.stateNode)}catch(r){ut(n,e,r)}else try{mt.removeChild(n.stateNode)}catch(r){ut(n,e,r)}break;case 18:mt!==null&&(ee?(t=mt,Hh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Nr(t)):Hh(mt,n.stateNode));break;case 4:l=mt,i=ee,mt=n.stateNode.containerInfo,ee=!0,en(t,e,n),mt=l,ee=i;break;case 0:case 11:case 14:case 15:Et||Xn(2,n,e),Et||Xn(4,n,e),en(t,e,n);break;case 1:Et||(je(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Bg(n,e,l)),en(t,e,n);break;case 21:en(t,e,n);break;case 22:Et=(l=Et)||n.memoizedState!==null,en(t,e,n),Et=l;break;default:en(t,e,n)}}function Gg(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Nr(t)}catch(n){ut(e,e.return,n)}}function Bv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Th),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Th),e;default:throw Error(w(435,t.tag))}}function pc(t,e){var n=Bv(t);e.forEach(function(l){var i=Zv.bind(null,t,l);n.has(l)||(n.add(l),l.then(i,i))})}function fe(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],r=t,a=e,u=a;t:for(;u!==null;){switch(u.tag){case 27:if(Zn(u.type)){mt=u.stateNode,ee=!1;break t}break;case 5:mt=u.stateNode,ee=!1;break t;case 3:case 4:mt=u.stateNode.containerInfo,ee=!0;break t}u=u.return}if(mt===null)throw Error(w(160));Xg(r,a,i),mt=null,ee=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Qg(e,t),e=e.sibling}var Re=null;function Qg(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:fe(e,t),se(t),l&4&&(Xn(3,t,t.return),Kr(3,t),Xn(5,t,t.return));break;case 1:fe(e,t),se(t),l&512&&(Et||n===null||je(n,n.return)),l&64&&nn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Re;if(fe(e,t),se(t),l&512&&(Et||n===null||je(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,i=i.ownerDocument||i;e:switch(l){case"title":r=i.getElementsByTagName("title")[0],(!r||r[qr]||r[Xt]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(l),i.head.insertBefore(r,i.querySelector("head > title"))),Yt(r,l,n),r[Xt]=t,Nt(r),l=r;break t;case"link":var a=Xh("link","href",i).get(l+(n.href||""));if(a){for(var u=0;u<a.length;u++)if(r=a[u],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){a.splice(u,1);break e}}r=i.createElement(l),Yt(r,l,n),i.head.appendChild(r);break;case"meta":if(a=Xh("meta","content",i).get(l+(n.content||""))){for(u=0;u<a.length;u++)if(r=a[u],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){a.splice(u,1);break e}}r=i.createElement(l),Yt(r,l,n),i.head.appendChild(r);break;default:throw Error(w(468,l))}r[Xt]=t,Nt(r),l=r}t.stateNode=l}else Gh(i,t.type,t.stateNode);else t.stateNode=Vh(i,l,t.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?Gh(i,t.type,t.stateNode):Vh(i,l,t.memoizedProps)):l===null&&t.stateNode!==null&&fc(t,t.memoizedProps,n.memoizedProps)}break;case 27:fe(e,t),se(t),l&512&&(Et||n===null||je(n,n.return)),n!==null&&l&4&&fc(t,t.memoizedProps,n.memoizedProps);break;case 5:if(fe(e,t),se(t),l&512&&(Et||n===null||je(n,n.return)),t.flags&32){i=t.stateNode;try{di(i,"")}catch(m){ut(t,t.return,m)}}l&4&&t.stateNode!=null&&(i=t.memoizedProps,fc(t,i,n!==null?n.memoizedProps:i)),l&1024&&(mc=!0);break;case 6:if(fe(e,t),se(t),l&4){if(t.stateNode===null)throw Error(w(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(m){ut(t,t.return,m)}}break;case 3:if(Fa=null,i=Re,Re=Su(e.containerInfo),fe(e,t),Re=i,se(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Nr(e.containerInfo)}catch(m){ut(t,t.return,m)}mc&&(mc=!1,Zg(t));break;case 4:l=Re,Re=Su(t.stateNode.containerInfo),fe(e,t),se(t),Re=l;break;case 12:fe(e,t),se(t);break;case 13:fe(e,t),se(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(ts=Ve()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,pc(t,l)));break;case 22:i=t.memoizedState!==null;var o=n!==null&&n.memoizedState!==null,c=nn,f=Et;if(nn=c||i,Et=f||o,fe(e,t),Et=f,nn=c,se(t),l&8192)t:for(e=t.stateNode,e._visibility=i?e._visibility&-2:e._visibility|1,i&&(n===null||o||nn||Et||al(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){o=n=e;try{if(r=o.stateNode,i)a=r.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none";else{u=o.stateNode;var s=o.memoizedProps.style,p=s!=null&&s.hasOwnProperty("display")?s.display:null;u.style.display=p==null||typeof p=="boolean"?"":(""+p).trim()}}catch(m){ut(o,o.return,m)}}}else if(e.tag===6){if(n===null){o=e;try{o.stateNode.nodeValue=i?"":o.memoizedProps}catch(m){ut(o,o.return,m)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,pc(t,n))));break;case 19:fe(e,t),se(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,pc(t,l)));break;case 30:break;case 21:break;default:fe(e,t),se(t)}}function se(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(qg(l)){n=l;break}l=l.return}if(n==null)throw Error(w(160));switch(n.tag){case 27:var i=n.stateNode,r=sc(t);hu(t,r,i);break;case 5:var a=n.stateNode;n.flags&32&&(di(a,""),n.flags&=-33);var u=sc(t);hu(t,u,a);break;case 3:case 4:var o=n.stateNode.containerInfo,c=sc(t);Pc(t,c,o);break;default:throw Error(w(161))}}catch(f){ut(t,t.return,f)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Zg(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Zg(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function kn(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Yg(t,e.alternate,e),e=e.sibling}function al(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Xn(4,e,e.return),al(e);break;case 1:je(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Bg(e,e.return,n),al(e);break;case 27:vr(e.stateNode);case 26:case 5:je(e,e.return),al(e);break;case 22:e.memoizedState===null&&al(e);break;case 30:al(e);break;default:al(e)}t=t.sibling}}function En(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,i=t,r=e,a=r.flags;switch(r.tag){case 0:case 11:case 15:En(i,r,n),Kr(4,r);break;case 1:if(En(i,r,n),l=r,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(c){ut(l,l.return,c)}if(l=r,i=l.updateQueue,i!==null){var u=l.stateNode;try{var o=i.shared.hiddenCallbacks;if(o!==null)for(i.shared.hiddenCallbacks=null,i=0;i<o.length;i++)Yd(o[i],u)}catch(c){ut(l,l.return,c)}}n&&a&64&&Ug(r),gr(r,r.return);break;case 27:jg(r);case 26:case 5:En(i,r,n),n&&l===null&&a&4&&Hg(r),gr(r,r.return);break;case 12:En(i,r,n);break;case 13:En(i,r,n),n&&a&4&&Gg(i,r);break;case 22:r.memoizedState===null&&En(i,r,n),gr(r,r.return);break;case 30:break;default:En(i,r,n)}e=e.sibling}}function Jf(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Xr(n))}function Wf(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Xr(t))}function He(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Kg(t,e,n,l),e=e.sibling}function Kg(t,e,n,l){var i=e.flags;switch(e.tag){case 0:case 11:case 15:He(t,e,n,l),i&2048&&Kr(9,e);break;case 1:He(t,e,n,l);break;case 3:He(t,e,n,l),i&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Xr(t)));break;case 12:if(i&2048){He(t,e,n,l),t=e.stateNode;try{var r=e.memoizedProps,a=r.id,u=r.onPostCommit;typeof u=="function"&&u(a,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(o){ut(e,e.return,o)}}else He(t,e,n,l);break;case 13:He(t,e,n,l);break;case 23:break;case 22:r=e.stateNode,a=e.alternate,e.memoizedState!==null?r._visibility&2?He(t,e,n,l):yr(t,e):r._visibility&2?He(t,e,n,l):(r._visibility|=2,Ql(t,e,n,l,(e.subtreeFlags&10256)!==0)),i&2048&&Jf(a,e);break;case 24:He(t,e,n,l),i&2048&&Wf(e.alternate,e);break;default:He(t,e,n,l)}}function Ql(t,e,n,l,i){for(i=i&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,a=e,u=n,o=l,c=a.flags;switch(a.tag){case 0:case 11:case 15:Ql(r,a,u,o,i),Kr(8,a);break;case 23:break;case 22:var f=a.stateNode;a.memoizedState!==null?f._visibility&2?Ql(r,a,u,o,i):yr(r,a):(f._visibility|=2,Ql(r,a,u,o,i)),i&&c&2048&&Jf(a.alternate,a);break;case 24:Ql(r,a,u,o,i),i&&c&2048&&Wf(a.alternate,a);break;default:Ql(r,a,u,o,i)}e=e.sibling}}function yr(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,i=l.flags;switch(l.tag){case 22:yr(n,l),i&2048&&Jf(l.alternate,l);break;case 24:yr(n,l),i&2048&&Wf(l.alternate,l);break;default:yr(n,l)}e=e.sibling}}var ar=8192;function Vl(t){if(t.subtreeFlags&ar)for(t=t.child;t!==null;)Fg(t),t=t.sibling}function Fg(t){switch(t.tag){case 26:Vl(t),t.flags&ar&&t.memoizedState!==null&&vS(Re,t.memoizedState,t.memoizedProps);break;case 5:Vl(t);break;case 3:case 4:var e=Re;Re=Su(t.stateNode.containerInfo),Vl(t),Re=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=ar,ar=16777216,Vl(t),ar=e):Vl(t));break;default:Vl(t)}}function Ig(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function tr(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Rt=l,Wg(l,t)}Ig(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Jg(t),t=t.sibling}function Jg(t){switch(t.tag){case 0:case 11:case 15:tr(t),t.flags&2048&&Xn(9,t,t.return);break;case 3:tr(t);break;case 12:tr(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Za(t)):tr(t);break;default:tr(t)}}function Za(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Rt=l,Wg(l,t)}Ig(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Xn(8,e,e.return),Za(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Za(e));break;default:Za(e)}t=t.sibling}}function Wg(t,e){for(;Rt!==null;){var n=Rt;switch(n.tag){case 0:case 11:case 15:Xn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Xr(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Rt=l;else t:for(n=t;Rt!==null;){l=Rt;var i=l.sibling,r=l.return;if(Vg(l),l===n){Rt=null;break t}if(i!==null){i.return=r,Rt=i;break t}Rt=r}}}var Hv={getCacheForType:function(t){var e=Gt(Ct),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},qv=typeof WeakMap=="function"?WeakMap:Map,nt=0,ot=null,F=null,W=0,et=0,me=null,_n=!1,zi=!1,Pf=!1,hn=0,xt=0,Gn=0,ml=0,$f=0,ze=0,vi=0,xr=null,ne=null,$c=!1,ts=0,du=1/0,gu=null,Bn=null,jt=0,Hn=null,Si=null,mi=0,tf=0,ef=null,Pg=null,br=0,nf=null;function ge(){if(nt&2&&W!==0)return W&-W;if(H.T!==null){var t=gi;return t!==0?t:ns()}return cd()}function $g(){ze===0&&(ze=!(W&536870912)||$?rd():536870912);var t=Ce.current;return t!==null&&(t.flags|=32),ze}function ye(t,e,n){(t===ot&&(et===2||et===9)||t.cancelPendingCommit!==null)&&(ki(t,0),Rn(t,W,ze,!1)),Hr(t,n),(!(nt&2)||t!==ot)&&(t===ot&&(!(nt&2)&&(ml|=n),xt===4&&Rn(t,W,ze,!1)),Qe(t))}function ty(t,e,n){if(nt&6)throw Error(w(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||Br(t,e),i=l?Vv(t,e):hc(t,e,!0),r=l;do{if(i===0){zi&&!l&&Rn(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!jv(n)){i=hc(t,e,!1),r=!1;continue}if(i===2){if(r=e,t.errorRecoveryDisabledLanes&r)var a=0;else a=t.pendingLanes&-536870913,a=a!==0?a:a&536870912?536870912:0;if(a!==0){e=a;t:{var u=t;i=xr;var o=u.current.memoizedState.isDehydrated;if(o&&(ki(u,a).flags|=256),a=hc(u,a,!1),a!==2){if(Pf&&!o){u.errorRecoveryDisabledLanes|=r,ml|=r,i=4;break t}r=ne,ne=i,r!==null&&(ne===null?ne=r:ne.push.apply(ne,r))}i=a}if(r=!1,i!==2)continue}}if(i===1){ki(t,0),Rn(t,e,0,!0);break}t:{switch(l=t,r=i,r){case 0:case 1:throw Error(w(345));case 4:if((e&4194048)!==e)break;case 6:Rn(l,e,ze,!_n);break t;case 2:ne=null;break;case 3:case 5:break;default:throw Error(w(329))}if((e&62914560)===e&&(i=ts+300-Ve(),10<i)){if(Rn(l,e,ze,!_n),wu(l,0,!0)!==0)break t;l.timeoutHandle=by(Ah.bind(null,l,n,ne,gu,$c,e,ze,ml,vi,_n,r,2,-0,0),i);break t}Ah(l,n,ne,gu,$c,e,ze,ml,vi,_n,r,0,-0,0)}}break}while(!0);Qe(t)}function Ah(t,e,n,l,i,r,a,u,o,c,f,s,p,m){if(t.timeoutHandle=-1,s=e.subtreeFlags,(s&8192||(s&16785408)===16785408)&&(Mr={stylesheets:null,count:0,unsuspend:bS},Fg(e),s=SS(),s!==null)){t.cancelPendingCommit=s(zh.bind(null,t,e,r,n,l,i,a,u,o,f,1,p,m)),Rn(t,r,a,!c);return}zh(t,e,r,n,l,i,a,u,o)}function jv(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],r=i.getSnapshot;i=i.value;try{if(!xe(r(),i))return!1}catch(a){return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Rn(t,e,n,l){e&=~$f,e&=~ml,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var i=e;0<i;){var r=31-de(i),a=1<<r;l[r]=-1,i&=~a}n!==0&&ud(t,n,e)}function Uu(){return nt&6?!0:(Fr(0,!1),!1)}function es(){if(F!==null){if(et===0)var t=F.return;else t=F,un=Sl=null,Yf(t),si=null,zr=0,t=F;for(;t!==null;)Lg(t.alternate,t),t=t.return;F=null}}function ki(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,nS(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),es(),ot=t,F=n=cn(t.current,null),W=e,et=0,me=null,_n=!1,zi=Br(t,e),Pf=!1,vi=ze=$f=ml=Gn=xt=0,ne=xr=null,$c=!1,e&8&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var i=31-de(l),r=1<<i;e|=t[i],l&=~r}return hn=e,Mu(),n}function ey(t,e){G=null,H.H=cu,e===Gr||e===_u?(e=lh(),et=3):e===qd?(e=lh(),et=4):et=e===Dg?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,me=e,F===null&&(xt=1,mu(t,we(e,t.current)))}function ny(){var t=H.H;return H.H=cu,t===null?cu:t}function ly(){var t=H.A;return H.A=Hv,t}function lf(){xt=4,_n||(W&4194048)!==W&&Ce.current!==null||(zi=!0),!(Gn&134217727)&&!(ml&134217727)||ot===null||Rn(ot,W,ze,!1)}function hc(t,e,n){var l=nt;nt|=2;var i=ny(),r=ly();(ot!==t||W!==e)&&(gu=null,ki(t,e)),e=!1;var a=xt;t:do try{if(et!==0&&F!==null){var u=F,o=me;switch(et){case 8:es(),a=6;break t;case 3:case 2:case 9:case 6:Ce.current===null&&(e=!0);var c=et;if(et=0,me=null,ii(t,u,o,c),n&&zi){a=0;break t}break;default:c=et,et=0,me=null,ii(t,u,o,c)}}Yv(),a=xt;break}catch(f){ey(t,f)}while(!0);return e&&t.shellSuspendCounter++,un=Sl=null,nt=l,H.H=i,H.A=r,F===null&&(ot=null,W=0,Mu()),a}function Yv(){for(;F!==null;)iy(F)}function Vv(t,e){var n=nt;nt|=2;var l=ny(),i=ly();ot!==t||W!==e?(gu=null,du=Ve()+500,ki(t,e)):zi=Br(t,e);t:do try{if(et!==0&&F!==null){e=F;var r=me;e:switch(et){case 1:et=0,me=null,ii(t,e,r,1);break;case 2:case 9:if(nh(r)){et=0,me=null,wh(e);break}e=function(){et!==2&&et!==9||ot!==t||(et=7),Qe(t)},r.then(e,e);break t;case 3:et=7;break t;case 4:et=5;break t;case 7:nh(r)?(et=0,me=null,wh(e)):(et=0,me=null,ii(t,e,r,7));break;case 5:var a=null;switch(F.tag){case 26:a=F.memoizedState;case 5:case 27:var u=F;if(!a||Ey(a)){et=0,me=null;var o=u.sibling;if(o!==null)F=o;else{var c=u.return;c!==null?(F=c,Bu(c)):F=null}break e}}et=0,me=null,ii(t,e,r,5);break;case 6:et=0,me=null,ii(t,e,r,6);break;case 8:es(),xt=6;break t;default:throw Error(w(462))}}Xv();break}catch(f){ey(t,f)}while(!0);return un=Sl=null,H.H=l,H.A=i,nt=n,F!==null?0:(ot=null,W=0,Mu(),xt)}function Xv(){for(;F!==null&&!sb();)iy(F)}function iy(t){var e=Ng(t.alternate,t,hn);t.memoizedProps=t.pendingProps,e===null?Bu(t):F=e}function wh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=bh(n,e,e.pendingProps,e.type,void 0,W);break;case 11:e=bh(n,e,e.pendingProps,e.type.render,e.ref,W);break;case 5:Yf(e);default:Lg(n,e),e=F=Ld(e,hn),e=Ng(n,e,hn)}t.memoizedProps=t.pendingProps,e===null?Bu(t):F=e}function ii(t,e,n,l){un=Sl=null,Yf(e),si=null,zr=0;var i=e.return;try{if(_v(t,i,e,n,W)){xt=1,mu(t,we(n,t.current)),F=null;return}}catch(r){if(i!==null)throw F=i,r;xt=1,mu(t,we(n,t.current)),F=null;return}e.flags&32768?($||l===1?t=!0:zi||W&536870912?t=!1:(_n=t=!0,(l===2||l===9||l===3||l===6)&&(l=Ce.current,l!==null&&l.tag===13&&(l.flags|=16384))),ry(e,t)):Bu(e)}function Bu(t){var e=t;do{if(e.flags&32768){ry(e,_n);return}t=e.return;var n=Nv(e.alternate,e,hn);if(n!==null){F=n;return}if(e=e.sibling,e!==null){F=e;return}F=e=t}while(e!==null);xt===0&&(xt=5)}function ry(t,e){do{var n=Lv(t.alternate,t);if(n!==null){n.flags&=32767,F=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){F=t;return}F=t=n}while(t!==null);xt=6,F=null}function zh(t,e,n,l,i,r,a,u,o){t.cancelPendingCommit=null;do Hu();while(jt!==0);if(nt&6)throw Error(w(327));if(e!==null){if(e===t.current)throw Error(w(177));if(r=e.lanes|e.childLanes,r|=Cf,Sb(t,n,r,a,u,o),t===ot&&(F=ot=null,W=0),Si=e,Hn=t,mi=n,tf=r,ef=i,Pg=l,e.subtreeFlags&10256||e.flags&10256?(t.callbackNode=null,t.callbackPriority=0,Kv($a,function(){return fy(!0),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,e.subtreeFlags&13878||l){l=H.T,H.T=null,i=tt.p,tt.p=2,a=nt,nt|=4;try{Uv(t,e,n)}finally{nt=a,tt.p=i,H.T=l}}jt=1,ay(),uy(),oy()}}function ay(){if(jt===1){jt=0;var t=Hn,e=Si,n=(e.flags&13878)!==0;if(e.subtreeFlags&13878||n){n=H.T,H.T=null;var l=tt.p;tt.p=2;var i=nt;nt|=4;try{Qg(e,t);var r=of,a=zd(t.containerInfo),u=r.focusedElem,o=r.selectionRange;if(a!==u&&u&&u.ownerDocument&&wd(u.ownerDocument.documentElement,u)){if(o!==null&&zf(u)){var c=o.start,f=o.end;if(f===void 0&&(f=c),"selectionStart"in u)u.selectionStart=c,u.selectionEnd=Math.min(f,u.value.length);else{var s=u.ownerDocument||document,p=s&&s.defaultView||window;if(p.getSelection){var m=p.getSelection(),y=u.textContent.length,v=Math.min(o.start,y),T=o.end===void 0?v:Math.min(o.end,y);!m.extend&&v>T&&(a=T,T=v,v=a);var h=Fp(u,v),d=Fp(u,T);if(h&&d&&(m.rangeCount!==1||m.anchorNode!==h.node||m.anchorOffset!==h.offset||m.focusNode!==d.node||m.focusOffset!==d.offset)){var g=s.createRange();g.setStart(h.node,h.offset),m.removeAllRanges(),v>T?(m.addRange(g),m.extend(d.node,d.offset)):(g.setEnd(d.node,d.offset),m.addRange(g))}}}}for(s=[],m=u;m=m.parentNode;)m.nodeType===1&&s.push({element:m,left:m.scrollLeft,top:m.scrollTop});for(typeof u.focus=="function"&&u.focus(),u=0;u<s.length;u++){var E=s[u];E.element.scrollLeft=E.left,E.element.scrollTop=E.top}}Tu=!!uf,of=uf=null}finally{nt=i,tt.p=l,H.T=n}}t.current=e,jt=2}}function uy(){if(jt===2){jt=0;var t=Hn,e=Si,n=(e.flags&8772)!==0;if(e.subtreeFlags&8772||n){n=H.T,H.T=null;var l=tt.p;tt.p=2;var i=nt;nt|=4;try{Yg(t,e.alternate,e)}finally{nt=i,tt.p=l,H.T=n}}jt=3}}function oy(){if(jt===4||jt===3){jt=0,mb();var t=Hn,e=Si,n=mi,l=Pg;e.subtreeFlags&10256||e.flags&10256?jt=5:(jt=0,Si=Hn=null,cy(t,t.pendingLanes));var i=t.pendingLanes;if(i===0&&(Bn=null),vf(n),e=e.stateNode,he&&typeof he.onCommitFiberRoot=="function")try{he.onCommitFiberRoot(Ur,e,void 0,(e.current.flags&128)===128)}catch(o){}if(l!==null){e=H.T,i=tt.p,tt.p=2,H.T=null;try{for(var r=t.onRecoverableError,a=0;a<l.length;a++){var u=l[a];r(u.value,{componentStack:u.stack})}}finally{H.T=e,tt.p=i}}mi&3&&Hu(),Qe(t),i=t.pendingLanes,n&4194090&&i&42?t===nf?br++:(br=0,nf=t):br=0,Fr(0,!1)}}function cy(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Xr(e)))}function Hu(t){return ay(),uy(),oy(),fy(t)}function fy(){if(jt!==5)return!1;var t=Hn,e=tf;tf=0;var n=vf(mi),l=H.T,i=tt.p;try{tt.p=32>n?32:n,H.T=null,n=ef,ef=null;var r=Hn,a=mi;if(jt=0,Si=Hn=null,mi=0,nt&6)throw Error(w(331));var u=nt;if(nt|=4,Jg(r.current),Kg(r,r.current,a,n),nt=u,Fr(0,!1),he&&typeof he.onPostCommitFiberRoot=="function")try{he.onPostCommitFiberRoot(Ur,r)}catch(o){}return!0}finally{tt.p=i,H.T=l,cy(t,e)}}function Ch(t,e,n){e=we(n,e),e=Ic(t.stateNode,e,2),t=Un(t,e,2),t!==null&&(Hr(t,2),Qe(t))}function ut(t,e,n){if(t.tag===3)Ch(t,t,n);else for(;e!==null;){if(e.tag===3){Ch(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Bn===null||!Bn.has(l))){t=we(n,t),n=zg(2),l=Un(e,n,2),l!==null&&(Cg(n,l,e,t),Hr(l,2),Qe(l));break}}e=e.return}}function dc(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new qv;var i=new Set;l.set(e,i)}else i=l.get(e),i===void 0&&(i=new Set,l.set(e,i));i.has(n)||(Pf=!0,i.add(n),t=Gv.bind(null,t,e,n),e.then(t,t))}function Gv(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,ot===t&&(W&n)===n&&(xt===4||xt===3&&(W&62914560)===W&&300>Ve()-ts?!(nt&2)&&ki(t,0):$f|=n,vi===W&&(vi=0)),Qe(t)}function sy(t,e){e===0&&(e=ad()),t=wi(t,e),t!==null&&(Hr(t,e),Qe(t))}function Qv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),sy(t,n)}function Zv(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(w(314))}l!==null&&l.delete(e),sy(t,n)}function Kv(t,e){return xf(t,e)}var yu=null,Zl=null,rf=!1,xu=!1,gc=!1,pl=0;function Qe(t){t!==Zl&&t.next===null&&(Zl===null?yu=Zl=t:Zl=Zl.next=t),xu=!0,rf||(rf=!0,Iv())}function Fr(t,e){if(!gc&&xu){gc=!0;do for(var n=!1,l=yu;l!==null;){if(!e)if(t!==0){var i=l.pendingLanes;if(i===0)var r=0;else{var a=l.suspendedLanes,u=l.pingedLanes;r=(1<<31-de(42|t)+1)-1,r&=i&~(a&~u),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Dh(l,r))}else r=W,r=wu(l,l===ot?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),!(r&3)||Br(l,r)||(n=!0,Dh(l,r));l=l.next}while(n);gc=!1}}function Fv(){my()}function my(){xu=rf=!1;var t=0;pl!==0&&(eS()&&(t=pl),pl=0);for(var e=Ve(),n=null,l=yu;l!==null;){var i=l.next,r=py(l,e);r===0?(l.next=null,n===null?yu=i:n.next=i,i===null&&(Zl=n)):(n=l,(t!==0||r&3)&&(xu=!0)),l=i}Fr(t,!1)}function py(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,i=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var a=31-de(r),u=1<<a,o=i[a];o===-1?(!(u&n)||u&l)&&(i[a]=vb(u,e)):o<=e&&(t.expiredLanes|=u),r&=~u}if(e=ot,n=W,n=wu(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(et===2||et===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&Xo(l),t.callbackNode=null,t.callbackPriority=0;if(!(n&3)||Br(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&Xo(l),vf(n)){case 2:case 8:n=ld;break;case 32:n=$a;break;case 268435456:n=id;break;default:n=$a}return l=hy.bind(null,t),n=xf(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&Xo(l),t.callbackPriority=2,t.callbackNode=null,2}function hy(t,e){if(jt!==0&&jt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(Hu(!0)&&t.callbackNode!==n)return null;var l=W;return l=wu(t,t===ot?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(ty(t,l,e),py(t,Ve()),t.callbackNode!=null&&t.callbackNode===n?hy.bind(null,t):null)}function Dh(t,e){if(Hu())return null;ty(t,e,!0)}function Iv(){lS(function(){nt&6?xf(nd,Fv):my()})}function ns(){return pl===0&&(pl=rd()),pl}function Mh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Ha(""+t)}function Oh(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Jv(t,e,n,l,i){if(e==="submit"&&n&&n.stateNode===i){var r=Mh((i[le]||null).action),a=l.submitter;a&&(e=(e=a[le]||null)?Mh(e.formAction):a.getAttribute("formAction"),e!==null&&(r=e,a=null));var u=new zu("action","action",null,l,i);t.push({event:u,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(pl!==0){var o=a?Oh(i,a):new FormData(i);Kc(n,{pending:!0,data:o,method:i.method,action:r},null,o)}}else typeof r=="function"&&(u.preventDefault(),o=a?Oh(i,a):new FormData(i),Kc(n,{pending:!0,data:o,method:i.method,action:r},r,o))},currentTarget:i}]})}}for(_a=0;_a<Lc.length;_a++)Ra=Lc[_a],_h=Ra.toLowerCase(),Rh=Ra[0].toUpperCase()+Ra.slice(1),Le(_h,"on"+Rh);var Ra,_h,Rh,_a;Le(Dd,"onAnimationEnd");Le(Md,"onAnimationIteration");Le(Od,"onAnimationStart");Le("dblclick","onDoubleClick");Le("focusin","onFocus");Le("focusout","onBlur");Le(dv,"onTransitionRun");Le(gv,"onTransitionStart");Le(yv,"onTransitionCancel");Le(_d,"onTransitionEnd");hi("onMouseEnter",["mouseout","mouseover"]);hi("onMouseLeave",["mouseout","mouseover"]);hi("onPointerEnter",["pointerout","pointerover"]);hi("onPointerLeave",["pointerout","pointerover"]);xl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));xl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));xl("onBeforeInput",["compositionend","keypress","textInput","paste"]);xl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));xl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));xl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Cr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Wv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Cr));function dy(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],i=l.event;l=l.listeners;t:{var r=void 0;if(e)for(var a=l.length-1;0<=a;a--){var u=l[a],o=u.instance,c=u.currentTarget;if(u=u.listener,o!==r&&i.isPropagationStopped())break t;r=u,i.currentTarget=c;try{r(i)}catch(f){su(f)}i.currentTarget=null,r=o}else for(a=0;a<l.length;a++){if(u=l[a],o=u.instance,c=u.currentTarget,u=u.listener,o!==r&&i.isPropagationStopped())break t;r=u,i.currentTarget=c;try{r(i)}catch(f){su(f)}i.currentTarget=null,r=o}}}}function K(t,e){var n=e[Cc];n===void 0&&(n=e[Cc]=new Set);var l=t+"__bubble";n.has(l)||(gy(e,t,2,!1),n.add(l))}function yc(t,e,n){var l=0;e&&(l|=4),gy(n,t,l,e)}var Na="_reactListening"+Math.random().toString(36).slice(2);function ls(t){if(!t[Na]){t[Na]=!0,fd.forEach(function(n){n!=="selectionchange"&&(Wv.has(n)||yc(n,!1,t),yc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Na]||(e[Na]=!0,yc("selectionchange",!1,e))}}function gy(t,e,n,l){switch(Cy(e)){case 2:var i=TS;break;case 8:i=AS;break;default:i=us}n=i.bind(null,e,n,t),i=void 0,!_c||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),l?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function xc(t,e,n,l,i){var r=l;if(!(e&1)&&!(e&2)&&l!==null)t:for(;;){if(l===null)return;var a=l.tag;if(a===3||a===4){var u=l.stateNode.containerInfo;if(u===i)break;if(a===4)for(a=l.return;a!==null;){var o=a.tag;if((o===3||o===4)&&a.stateNode.containerInfo===i)return;a=a.return}for(;u!==null;){if(a=Il(u),a===null)return;if(o=a.tag,o===5||o===6||o===26||o===27){l=r=a;continue t}u=u.parentNode}}l=l.return}xd(function(){var c=r,f=Ef(n),s=[];t:{var p=Rd.get(t);if(p!==void 0){var m=zu,y=t;switch(t){case"keypress":if(ja(n)===0)break t;case"keydown":case"keyup":m=Kb;break;case"focusin":y="focus",m=Wo;break;case"focusout":y="blur",m=Wo;break;case"beforeblur":case"afterblur":m=Wo;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=qp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Lb;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Jb;break;case Dd:case Md:case Od:m=Hb;break;case _d:m=Pb;break;case"scroll":case"scrollend":m=Rb;break;case"wheel":m=tv;break;case"copy":case"cut":case"paste":m=jb;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=Yp;break;case"toggle":case"beforetoggle":m=nv}var v=(e&4)!==0,T=!v&&(t==="scroll"||t==="scrollend"),h=v?p!==null?p+"Capture":null:p;v=[];for(var d=c,g;d!==null;){var E=d;if(g=E.stateNode,E=E.tag,E!==5&&E!==26&&E!==27||g===null||h===null||(E=kr(d,h),E!=null&&v.push(Dr(d,E,g))),T)break;d=d.return}0<v.length&&(p=new m(p,y,null,n,f),s.push({event:p,listeners:v}))}}if(!(e&7)){t:{if(p=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",p&&n!==Oc&&(y=n.relatedTarget||n.fromElement)&&(Il(y)||y[Ti]))break t;if((m||p)&&(p=f.window===f?f:(p=f.ownerDocument)?p.defaultView||p.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=c,y=y?Il(y):null,y!==null&&(T=Lr(y),v=y.tag,y!==T||v!==5&&v!==27&&v!==6)&&(y=null)):(m=null,y=c),m!==y)){if(v=qp,E="onMouseLeave",h="onMouseEnter",d="mouse",(t==="pointerout"||t==="pointerover")&&(v=Yp,E="onPointerLeave",h="onPointerEnter",d="pointer"),T=m==null?p:rr(m),g=y==null?p:rr(y),p=new v(E,d+"leave",m,n,f),p.target=T,p.relatedTarget=g,E=null,Il(f)===c&&(v=new v(h,d+"enter",y,n,f),v.target=g,v.relatedTarget=T,E=v),T=E,m&&y)e:{for(v=m,h=y,d=0,g=v;g;g=Xl(g))d++;for(g=0,E=h;E;E=Xl(E))g++;for(;0<d-g;)v=Xl(v),d--;for(;0<g-d;)h=Xl(h),g--;for(;d--;){if(v===h||h!==null&&v===h.alternate)break e;v=Xl(v),h=Xl(h)}v=null}else v=null;m!==null&&Nh(s,p,m,v,!1),y!==null&&T!==null&&Nh(s,T,y,v,!0)}}t:{if(p=c?rr(c):window,m=p.nodeName&&p.nodeName.toLowerCase(),m==="select"||m==="input"&&p.type==="file")var C=Qp;else if(Gp(p))if(Td)C=mv;else{C=fv;var k=cv}else m=p.nodeName,!m||m.toLowerCase()!=="input"||p.type!=="checkbox"&&p.type!=="radio"?c&&kf(c.elementType)&&(C=Qp):C=sv;if(C&&(C=C(t,c))){Ed(s,C,n,f);break t}k&&k(t,p,c),t==="focusout"&&c&&p.type==="number"&&c.memoizedProps.value!=null&&Mc(p,"number",p.value)}switch(k=c?rr(c):window,t){case"focusin":(Gp(k)||k.contentEditable==="true")&&(Pl=k,Rc=c,cr=null);break;case"focusout":cr=Rc=Pl=null;break;case"mousedown":Nc=!0;break;case"contextmenu":case"mouseup":case"dragend":Nc=!1,Ip(s,n,f);break;case"selectionchange":if(hv)break;case"keydown":case"keyup":Ip(s,n,f)}var D;if(wf)t:{switch(t){case"compositionstart":var O="onCompositionStart";break t;case"compositionend":O="onCompositionEnd";break t;case"compositionupdate":O="onCompositionUpdate";break t}O=void 0}else Wl?Sd(t,n)&&(O="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(O="onCompositionStart");O&&(vd&&n.locale!=="ko"&&(Wl||O!=="onCompositionStart"?O==="onCompositionEnd"&&Wl&&(D=bd()):(On=f,Tf="value"in On?On.value:On.textContent,Wl=!0)),k=bu(c,O),0<k.length&&(O=new jp(O,t,null,n,f),s.push({event:O,listeners:k}),D?O.data=D:(D=kd(n),D!==null&&(O.data=D)))),(D=iv?rv(t,n):av(t,n))&&(O=bu(c,"onBeforeInput"),0<O.length&&(k=new jp("onBeforeInput","beforeinput",null,n,f),s.push({event:k,listeners:O}),k.data=D)),Jv(s,t,c,n,f)}dy(s,e)})}function Dr(t,e,n){return{instance:t,listener:e,currentTarget:n}}function bu(t,e){for(var n=e+"Capture",l=[];t!==null;){var i=t,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=kr(t,n),i!=null&&l.unshift(Dr(t,i,r)),i=kr(t,e),i!=null&&l.push(Dr(t,i,r))),t.tag===3)return l;t=t.return}return[]}function Xl(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Nh(t,e,n,l,i){for(var r=e._reactName,a=[];n!==null&&n!==l;){var u=n,o=u.alternate,c=u.stateNode;if(u=u.tag,o!==null&&o===l)break;u!==5&&u!==26&&u!==27||c===null||(o=c,i?(c=kr(n,r),c!=null&&a.unshift(Dr(n,c,o))):i||(c=kr(n,r),c!=null&&a.push(Dr(n,c,o)))),n=n.return}a.length!==0&&t.push({event:e,listeners:a})}var Pv=/\r\n?/g,$v=/\u0000|\uFFFD/g;function Lh(t){return(typeof t=="string"?t:""+t).replace(Pv,`
`).replace($v,"")}function yy(t,e){return e=Lh(e),Lh(t)===e}function qu(){}function it(t,e,n,l,i,r){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||di(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&di(t,""+l);break;case"className":Ea(t,"class",l);break;case"tabIndex":Ea(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ea(t,n,l);break;case"style":yd(t,l,r);break;case"data":if(e!=="object"){Ea(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Ha(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&it(t,e,"name",i.name,i,null),it(t,e,"formEncType",i.formEncType,i,null),it(t,e,"formMethod",i.formMethod,i,null),it(t,e,"formTarget",i.formTarget,i,null)):(it(t,e,"encType",i.encType,i,null),it(t,e,"method",i.method,i,null),it(t,e,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Ha(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=qu);break;case"onScroll":l!=null&&K("scroll",t);break;case"onScrollEnd":l!=null&&K("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(w(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(w(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=Ha(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":K("beforetoggle",t),K("toggle",t),Ba(t,"popover",l);break;case"xlinkActuate":$e(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":$e(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":$e(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":$e(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":$e(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":$e(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":$e(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":$e(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":$e(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Ba(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Ob.get(n)||n,Ba(t,n,l))}}function af(t,e,n,l,i,r){switch(n){case"style":yd(t,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(w(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(w(60));t.innerHTML=n}}break;case"children":typeof l=="string"?di(t,l):(typeof l=="number"||typeof l=="bigint")&&di(t,""+l);break;case"onScroll":l!=null&&K("scroll",t);break;case"onScrollEnd":l!=null&&K("scrollend",t);break;case"onClick":l!=null&&(t.onclick=qu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!sd.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),e=n.slice(2,i?n.length-7:void 0),r=t[le]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,i),typeof l=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,i);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Ba(t,n,l)}}}function Yt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":K("error",t),K("load",t);var l=!1,i=!1,r;for(r in n)if(n.hasOwnProperty(r)){var a=n[r];if(a!=null)switch(r){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(w(137,e));default:it(t,e,r,a,n,null)}}i&&it(t,e,"srcSet",n.srcSet,n,null),l&&it(t,e,"src",n.src,n,null);return;case"input":K("invalid",t);var u=r=a=i=null,o=null,c=null;for(l in n)if(n.hasOwnProperty(l)){var f=n[l];if(f!=null)switch(l){case"name":i=f;break;case"type":a=f;break;case"checked":o=f;break;case"defaultChecked":c=f;break;case"value":r=f;break;case"defaultValue":u=f;break;case"children":case"dangerouslySetInnerHTML":if(f!=null)throw Error(w(137,e));break;default:it(t,e,l,f,n,null)}}hd(t,r,u,o,c,a,i,!1),tu(t);return;case"select":K("invalid",t),l=a=r=null;for(i in n)if(n.hasOwnProperty(i)&&(u=n[i],u!=null))switch(i){case"value":r=u;break;case"defaultValue":a=u;break;case"multiple":l=u;default:it(t,e,i,u,n,null)}e=r,n=a,t.multiple=!!l,e!=null?ai(t,!!l,e,!1):n!=null&&ai(t,!!l,n,!0);return;case"textarea":K("invalid",t),r=i=l=null;for(a in n)if(n.hasOwnProperty(a)&&(u=n[a],u!=null))switch(a){case"value":l=u;break;case"defaultValue":i=u;break;case"children":r=u;break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(w(91));break;default:it(t,e,a,u,n,null)}gd(t,l,i,r),tu(t);return;case"option":for(o in n)if(n.hasOwnProperty(o)&&(l=n[o],l!=null))switch(o){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:it(t,e,o,l,n,null)}return;case"dialog":K("beforetoggle",t),K("toggle",t),K("cancel",t),K("close",t);break;case"iframe":case"object":K("load",t);break;case"video":case"audio":for(l=0;l<Cr.length;l++)K(Cr[l],t);break;case"image":K("error",t),K("load",t);break;case"details":K("toggle",t);break;case"embed":case"source":case"link":K("error",t),K("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&(l=n[c],l!=null))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(w(137,e));default:it(t,e,c,l,n,null)}return;default:if(kf(e)){for(f in n)n.hasOwnProperty(f)&&(l=n[f],l!==void 0&&af(t,e,f,l,n,void 0));return}}for(u in n)n.hasOwnProperty(u)&&(l=n[u],l!=null&&it(t,e,u,l,n,null))}function tS(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,a=null,u=null,o=null,c=null,f=null;for(m in n){var s=n[m];if(n.hasOwnProperty(m)&&s!=null)switch(m){case"checked":break;case"value":break;case"defaultValue":o=s;default:l.hasOwnProperty(m)||it(t,e,m,null,l,s)}}for(var p in l){var m=l[p];if(s=n[p],l.hasOwnProperty(p)&&(m!=null||s!=null))switch(p){case"type":r=m;break;case"name":i=m;break;case"checked":c=m;break;case"defaultChecked":f=m;break;case"value":a=m;break;case"defaultValue":u=m;break;case"children":case"dangerouslySetInnerHTML":if(m!=null)throw Error(w(137,e));break;default:m!==s&&it(t,e,p,m,l,s)}}Dc(t,a,u,o,c,f,r,i);return;case"select":m=a=u=p=null;for(r in n)if(o=n[r],n.hasOwnProperty(r)&&o!=null)switch(r){case"value":break;case"multiple":m=o;default:l.hasOwnProperty(r)||it(t,e,r,null,l,o)}for(i in l)if(r=l[i],o=n[i],l.hasOwnProperty(i)&&(r!=null||o!=null))switch(i){case"value":p=r;break;case"defaultValue":u=r;break;case"multiple":a=r;default:r!==o&&it(t,e,i,r,l,o)}e=u,n=a,l=m,p!=null?ai(t,!!n,p,!1):!!l!=!!n&&(e!=null?ai(t,!!n,e,!0):ai(t,!!n,n?[]:"",!1));return;case"textarea":m=p=null;for(u in n)if(i=n[u],n.hasOwnProperty(u)&&i!=null&&!l.hasOwnProperty(u))switch(u){case"value":break;case"children":break;default:it(t,e,u,null,l,i)}for(a in l)if(i=l[a],r=n[a],l.hasOwnProperty(a)&&(i!=null||r!=null))switch(a){case"value":p=i;break;case"defaultValue":m=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(w(91));break;default:i!==r&&it(t,e,a,i,l,r)}dd(t,p,m);return;case"option":for(var y in n)if(p=n[y],n.hasOwnProperty(y)&&p!=null&&!l.hasOwnProperty(y))switch(y){case"selected":t.selected=!1;break;default:it(t,e,y,null,l,p)}for(o in l)if(p=l[o],m=n[o],l.hasOwnProperty(o)&&p!==m&&(p!=null||m!=null))switch(o){case"selected":t.selected=p&&typeof p!="function"&&typeof p!="symbol";break;default:it(t,e,o,p,l,m)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var v in n)p=n[v],n.hasOwnProperty(v)&&p!=null&&!l.hasOwnProperty(v)&&it(t,e,v,null,l,p);for(c in l)if(p=l[c],m=n[c],l.hasOwnProperty(c)&&p!==m&&(p!=null||m!=null))switch(c){case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(w(137,e));break;default:it(t,e,c,p,l,m)}return;default:if(kf(e)){for(var T in n)p=n[T],n.hasOwnProperty(T)&&p!==void 0&&!l.hasOwnProperty(T)&&af(t,e,T,void 0,l,p);for(f in l)p=l[f],m=n[f],!l.hasOwnProperty(f)||p===m||p===void 0&&m===void 0||af(t,e,f,p,l,m);return}}for(var h in n)p=n[h],n.hasOwnProperty(h)&&p!=null&&!l.hasOwnProperty(h)&&it(t,e,h,null,l,p);for(s in l)p=l[s],m=n[s],!l.hasOwnProperty(s)||p===m||p==null&&m==null||it(t,e,s,p,l,m)}var uf=null,of=null;function vu(t){return t.nodeType===9?t:t.ownerDocument}function Uh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function xy(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function cf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var bc=null;function eS(){var t=window.event;return t&&t.type==="popstate"?t===bc?!1:(bc=t,!0):(bc=null,!1)}var by=typeof setTimeout=="function"?setTimeout:void 0,nS=typeof clearTimeout=="function"?clearTimeout:void 0,Bh=typeof Promise=="function"?Promise:void 0,lS=typeof queueMicrotask=="function"?queueMicrotask:typeof Bh!="undefined"?function(t){return Bh.resolve(null).then(t).catch(iS)}:by;function iS(t){setTimeout(function(){throw t})}function Zn(t){return t==="head"}function Hh(t,e){var n=e,l=0,i=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var a=t.ownerDocument;if(n&1&&vr(a.documentElement),n&2&&vr(a.body),n&4)for(n=a.head,vr(n),a=n.firstChild;a;){var u=a.nextSibling,o=a.nodeName;a[qr]||o==="SCRIPT"||o==="STYLE"||o==="LINK"&&a.rel.toLowerCase()==="stylesheet"||n.removeChild(a),a=u}}if(i===0){t.removeChild(r),Nr(e);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);Nr(e)}function ff(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ff(n),Sf(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function rS(t,e,n,l){for(;t.nodeType===1;){var i=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[qr])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==i.rel||t.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||t.getAttribute("title")!==(i.title==null?null:i.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(i.src==null?null:i.src)||t.getAttribute("type")!==(i.type==null?null:i.type)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Ne(t.nextSibling),t===null)break}return null}function aS(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ne(t.nextSibling),t===null))return null;return t}function sf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function uS(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Ne(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var mf=null;function qh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function vy(t,e,n){switch(e=vu(n),t){case"html":if(t=e.documentElement,!t)throw Error(w(452));return t;case"head":if(t=e.head,!t)throw Error(w(453));return t;case"body":if(t=e.body,!t)throw Error(w(454));return t;default:throw Error(w(451))}}function vr(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Sf(t)}var De=new Map,jh=new Set;function Su(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var dn=tt.d;tt.d={f:oS,r:cS,D:fS,C:sS,L:mS,m:pS,X:dS,S:hS,M:gS};function oS(){var t=dn.f(),e=Uu();return t||e}function cS(t){var e=Ai(t);e!==null&&e.tag===5&&e.type==="form"?pg(e):dn.r(t)}var Ci=typeof document=="undefined"?null:document;function Sy(t,e,n){var l=Ci;if(l&&typeof e=="string"&&e){var i=Ae(e);i='link[rel="'+t+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),jh.has(i)||(jh.add(i),t={rel:t,crossOrigin:n,href:e},l.querySelector(i)===null&&(e=l.createElement("link"),Yt(e,"link",t),Nt(e),l.head.appendChild(e)))}}function fS(t){dn.D(t),Sy("dns-prefetch",t,null)}function sS(t,e){dn.C(t,e),Sy("preconnect",t,e)}function mS(t,e,n){dn.L(t,e,n);var l=Ci;if(l&&t&&e){var i='link[rel="preload"][as="'+Ae(e)+'"]';e==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Ae(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Ae(n.imageSizes)+'"]')):i+='[href="'+Ae(t)+'"]';var r=i;switch(e){case"style":r=Ei(t);break;case"script":r=Di(t)}De.has(r)||(t=ct({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),De.set(r,t),l.querySelector(i)!==null||e==="style"&&l.querySelector(Ir(r))||e==="script"&&l.querySelector(Jr(r))||(e=l.createElement("link"),Yt(e,"link",t),Nt(e),l.head.appendChild(e)))}}function pS(t,e){dn.m(t,e);var n=Ci;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",i='link[rel="modulepreload"][as="'+Ae(l)+'"][href="'+Ae(t)+'"]',r=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=Di(t)}if(!De.has(r)&&(t=ct({rel:"modulepreload",href:t},e),De.set(r,t),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Jr(r)))return}l=n.createElement("link"),Yt(l,"link",t),Nt(l),n.head.appendChild(l)}}}function hS(t,e,n){dn.S(t,e,n);var l=Ci;if(l&&t){var i=ri(l).hoistableStyles,r=Ei(t);e=e||"default";var a=i.get(r);if(!a){var u={loading:0,preload:null};if(a=l.querySelector(Ir(r)))u.loading=5;else{t=ct({rel:"stylesheet",href:t,"data-precedence":e},n),(n=De.get(r))&&is(t,n);var o=a=l.createElement("link");Nt(o),Yt(o,"link",t),o._p=new Promise(function(c,f){o.onload=c,o.onerror=f}),o.addEventListener("load",function(){u.loading|=1}),o.addEventListener("error",function(){u.loading|=2}),u.loading|=4,Ka(a,e,l)}a={type:"stylesheet",instance:a,count:1,state:u},i.set(r,a)}}}function dS(t,e){dn.X(t,e);var n=Ci;if(n&&t){var l=ri(n).hoistableScripts,i=Di(t),r=l.get(i);r||(r=n.querySelector(Jr(i)),r||(t=ct({src:t,async:!0},e),(e=De.get(i))&&rs(t,e),r=n.createElement("script"),Nt(r),Yt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function gS(t,e){dn.M(t,e);var n=Ci;if(n&&t){var l=ri(n).hoistableScripts,i=Di(t),r=l.get(i);r||(r=n.querySelector(Jr(i)),r||(t=ct({src:t,async:!0,type:"module"},e),(e=De.get(i))&&rs(t,e),r=n.createElement("script"),Nt(r),Yt(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function Yh(t,e,n,l){var i=(i=Nn.current)?Su(i):null;if(!i)throw Error(w(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Ei(n.href),n=ri(i).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Ei(n.href);var r=ri(i).hoistableStyles,a=r.get(t);if(a||(i=i.ownerDocument||i,a={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,a),(r=i.querySelector(Ir(t)))&&!r._p&&(a.instance=r,a.state.loading=5),De.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},De.set(t,n),r||yS(i,t,n,a.state))),e&&l===null)throw Error(w(528,""));return a}if(e&&l!==null)throw Error(w(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Di(n),n=ri(i).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(w(444,t))}}function Ei(t){return'href="'+Ae(t)+'"'}function Ir(t){return'link[rel="stylesheet"]['+t+"]"}function ky(t){return ct({},t,{"data-precedence":t.precedence,precedence:null})}function yS(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),Yt(e,"link",n),Nt(e),t.head.appendChild(e))}function Di(t){return'[src="'+Ae(t)+'"]'}function Jr(t){return"script[async]"+t}function Vh(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Ae(n.href)+'"]');if(l)return e.instance=l,Nt(l),l;var i=ct({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Nt(l),Yt(l,"style",i),Ka(l,n.precedence,t),e.instance=l;case"stylesheet":i=Ei(n.href);var r=t.querySelector(Ir(i));if(r)return e.state.loading|=4,e.instance=r,Nt(r),r;l=ky(n),(i=De.get(i))&&is(l,i),r=(t.ownerDocument||t).createElement("link"),Nt(r);var a=r;return a._p=new Promise(function(u,o){a.onload=u,a.onerror=o}),Yt(r,"link",l),e.state.loading|=4,Ka(r,n.precedence,t),e.instance=r;case"script":return r=Di(n.src),(i=t.querySelector(Jr(r)))?(e.instance=i,Nt(i),i):(l=n,(i=De.get(r))&&(l=ct({},n),rs(l,i)),t=t.ownerDocument||t,i=t.createElement("script"),Nt(i),Yt(i,"link",l),t.head.appendChild(i),e.instance=i);case"void":return null;default:throw Error(w(443,e.type))}else e.type==="stylesheet"&&!(e.state.loading&4)&&(l=e.instance,e.state.loading|=4,Ka(l,n.precedence,t));return e.instance}function Ka(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,r=i,a=0;a<l.length;a++){var u=l[a];if(u.dataset.precedence===e)r=u;else if(r!==i)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function is(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function rs(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Fa=null;function Xh(t,e,n){if(Fa===null){var l=new Map,i=Fa=new Map;i.set(n,l)}else i=Fa,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),i=0;i<n.length;i++){var r=n[i];if(!(r[qr]||r[Xt]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var a=r.getAttribute(e)||"";a=t+a;var u=l.get(a);u?u.push(r):l.set(a,[r])}}return l}function Gh(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function xS(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Ey(t){return!(t.type==="stylesheet"&&!(t.state.loading&3))}var Mr=null;function bS(){}function vS(t,e,n){if(Mr===null)throw Error(w(475));var l=Mr;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&!(e.state.loading&4)){if(e.instance===null){var i=Ei(n.href),r=t.querySelector(Ir(i));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=ku.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=r,Nt(r);return}r=t.ownerDocument||t,n=ky(n),(i=De.get(i))&&is(n,i),r=r.createElement("link"),Nt(r);var a=r;a._p=new Promise(function(u,o){a.onload=u,a.onerror=o}),Yt(r,"link",n),e.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&!(e.state.loading&3)&&(l.count++,e=ku.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function SS(){if(Mr===null)throw Error(w(475));var t=Mr;return t.stylesheets&&t.count===0&&pf(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&pf(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function ku(){if(this.count--,this.count===0){if(this.stylesheets)pf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Eu=null;function pf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Eu=new Map,e.forEach(kS,t),Eu=null,ku.call(t))}function kS(t,e){if(!(e.state.loading&4)){var n=Eu.get(t);if(n)var l=n.get(null);else{n=new Map,Eu.set(t,n);for(var i=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var a=i[r];(a.nodeName==="LINK"||a.getAttribute("media")!=="not all")&&(n.set(a.dataset.precedence,a),l=a)}l&&n.set(null,l)}i=e.instance,a=i.getAttribute("data-precedence"),r=n.get(a)||l,r===l&&n.set(null,i),n.set(a,i),this.count++,l=ku.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),r?r.parentNode.insertBefore(i,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(i,t.firstChild)),e.state.loading|=4}}var Or={$$typeof:ln,Provider:null,Consumer:null,_currentValue:ul,_currentValue2:ul,_threadCount:0};function ES(t,e,n,l,i,r,a,u){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Go(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Go(0),this.hiddenUpdates=Go(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=a,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map}function Ty(t,e,n,l,i,r,a,u,o,c,f,s){return t=new ES(t,e,n,a,u,o,c,s),e=1,r===!0&&(e|=24),r=pe(3,null,null,e),t.current=r,r.stateNode=t,e=Rf(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:e},Lf(r),t}function Ay(t){return t?(t=ei,t):ei}function wy(t,e,n,l,i,r){i=Ay(i),l.context===null?l.context=i:l.pendingContext=i,l=Ln(e),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=Un(t,l,e),n!==null&&(ye(n,t,e),mr(n,t,e))}function Qh(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function as(t,e){Qh(t,e),(t=t.alternate)&&Qh(t,e)}function zy(t){if(t.tag===13){var e=wi(t,67108864);e!==null&&ye(e,t,67108864),as(t,67108864)}}var Tu=!0;function TS(t,e,n,l){var i=H.T;H.T=null;var r=tt.p;try{tt.p=2,us(t,e,n,l)}finally{tt.p=r,H.T=i}}function AS(t,e,n,l){var i=H.T;H.T=null;var r=tt.p;try{tt.p=8,us(t,e,n,l)}finally{tt.p=r,H.T=i}}function us(t,e,n,l){if(Tu){var i=hf(l);if(i===null)xc(t,e,l,Au,n),Zh(t,l);else if(zS(i,t,e,n,l))l.stopPropagation();else if(Zh(t,l),e&4&&-1<wS.indexOf(t)){for(;i!==null;){var r=Ai(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var a=il(r.pendingLanes);if(a!==0){var u=r;for(u.pendingLanes|=2,u.entangledLanes|=2;a;){var o=1<<31-de(a);u.entanglements[1]|=o,a&=~o}Qe(r),!(nt&6)&&(du=Ve()+500,Fr(0,!1))}}break;case 13:u=wi(r,2),u!==null&&ye(u,r,2),Uu(),as(r,2)}if(r=hf(l),r===null&&xc(t,e,l,Au,n),r===i)break;i=r}i!==null&&l.stopPropagation()}else xc(t,e,l,null,n)}}function hf(t){return t=Ef(t),os(t)}var Au=null;function os(t){if(Au=null,t=Il(t),t!==null){var e=Lr(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=Ph(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Au=t,null}function Cy(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(pb()){case nd:return 2;case ld:return 8;case $a:case hb:return 32;case id:return 268435456;default:return 32}default:return 32}}var df=!1,qn=null,jn=null,Yn=null,_r=new Map,Rr=new Map,Dn=[],wS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Zh(t,e){switch(t){case"focusin":case"focusout":qn=null;break;case"dragenter":case"dragleave":jn=null;break;case"mouseover":case"mouseout":Yn=null;break;case"pointerover":case"pointerout":_r.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rr.delete(e.pointerId)}}function er(t,e,n,l,i,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[i]},e!==null&&(e=Ai(e),e!==null&&zy(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function zS(t,e,n,l,i){switch(e){case"focusin":return qn=er(qn,t,e,n,l,i),!0;case"dragenter":return jn=er(jn,t,e,n,l,i),!0;case"mouseover":return Yn=er(Yn,t,e,n,l,i),!0;case"pointerover":var r=i.pointerId;return _r.set(r,er(_r.get(r)||null,t,e,n,l,i)),!0;case"gotpointercapture":return r=i.pointerId,Rr.set(r,er(Rr.get(r)||null,t,e,n,l,i)),!0}return!1}function Dy(t){var e=Il(t.target);if(e!==null){var n=Lr(e);if(n!==null){if(e=n.tag,e===13){if(e=Ph(n),e!==null){t.blockedOn=e,kb(t.priority,function(){if(n.tag===13){var l=ge();l=bf(l);var i=wi(n,l);i!==null&&ye(i,n,l),as(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ia(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=hf(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Oc=l,n.target.dispatchEvent(l),Oc=null}else return e=Ai(n),e!==null&&zy(e),t.blockedOn=n,!1;e.shift()}return!0}function Kh(t,e,n){Ia(t)&&n.delete(e)}function CS(){df=!1,qn!==null&&Ia(qn)&&(qn=null),jn!==null&&Ia(jn)&&(jn=null),Yn!==null&&Ia(Yn)&&(Yn=null),_r.forEach(Kh),Rr.forEach(Kh)}function La(t,e){t.blockedOn===e&&(t.blockedOn=null,df||(df=!0,Mt.unstable_scheduleCallback(Mt.unstable_NormalPriority,CS)))}var Ua=null;function Fh(t){Ua!==t&&(Ua=t,Mt.unstable_scheduleCallback(Mt.unstable_NormalPriority,function(){Ua===t&&(Ua=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],i=t[e+2];if(typeof l!="function"){if(os(l||n)===null)continue;break}var r=Ai(n);r!==null&&(t.splice(e,3),e-=3,Kc(r,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Nr(t){function e(o){return La(o,t)}qn!==null&&La(qn,t),jn!==null&&La(jn,t),Yn!==null&&La(Yn,t),_r.forEach(e),Rr.forEach(e);for(var n=0;n<Dn.length;n++){var l=Dn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Dn.length&&(n=Dn[0],n.blockedOn===null);)Dy(n),n.blockedOn===null&&Dn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],r=n[l+1],a=i[le]||null;if(typeof r=="function")a||Fh(n);else if(a){var u=null;if(r&&r.hasAttribute("formAction")){if(i=r,a=r[le]||null)u=a.formAction;else if(os(i)!==null)continue}else u=a.action;typeof u=="function"?n[l+1]=u:(n.splice(l,3),l-=3),Fh(n)}}}function cs(t){this._internalRoot=t}ju.prototype.render=cs.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(w(409));var n=e.current,l=ge();wy(n,l,t,e,null,null)};ju.prototype.unmount=cs.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;wy(t.current,2,null,t,null,null),Uu(),e[Ti]=null}};function ju(t){this._internalRoot=t}ju.prototype.unstable_scheduleHydration=function(t){if(t){var e=cd();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Dn.length&&e!==0&&e<Dn[n].priority;n++);Dn.splice(n,0,t),n===0&&Dy(t)}};var Ih=Jh.version;if(Ih!=="19.1.0")throw Error(w(527,Ih,"19.1.0"));tt.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(w(188)):(t=Object.keys(t).join(","),Error(w(268,t)));return t=ab(e),t=t!==null?$h(t):null,t=t===null?null:t.stateNode,t};var DS={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:H,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&(nr=__REACT_DEVTOOLS_GLOBAL_HOOK__,!nr.isDisabled&&nr.supportsFiber))try{Ur=nr.inject(DS),he=nr}catch(t){}var nr;Yu.createRoot=function(t,e){if(!Wh(t))throw Error(w(299));var n=!1,l="",i=Tg,r=Ag,a=wg,u=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(i=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(a=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(u=e.unstable_transitionCallbacks)),e=Ty(t,1,!1,null,null,n,l,i,r,a,u,null),t[Ti]=e.current,ls(t),new cs(e)};Yu.hydrateRoot=function(t,e,n){if(!Wh(t))throw Error(w(299));var l=!1,i="",r=Tg,a=Ag,u=wg,o=null,c=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(a=n.onCaughtError),n.onRecoverableError!==void 0&&(u=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(o=n.unstable_transitionCallbacks),n.formState!==void 0&&(c=n.formState)),e=Ty(t,1,!0,e,n!=null?n:null,l,i,r,a,u,o,c),e.context=Ay(null),n=e.current,l=ge(),l=bf(l),i=Ln(l),i.callback=null,Un(n,i,l),n=l,e.current.lanes=n,Hr(e,n),Qe(e),t[Ti]=e.current,ls(t),new ju(e)};Yu.version="19.1.0"});var Ry=Kt((gA,_y)=>{"use strict";function Oy(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Oy)}catch(t){console.error(t)}}Oy(),_y.exports=My()});var Ky=Kt((Sw,Zy)=>{var Vy=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,qS=/\n/g,jS=/^\s*/,YS=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,VS=/^:\s*/,XS=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,GS=/^[;\s]*/,QS=/^\s+|\s+$/g,ZS=`
`,Xy="/",Gy="*",Tl="",KS="comment",FS="declaration";Zy.exports=function(t,e){if(typeof t!="string")throw new TypeError("First argument must be a string");if(!t)return[];e=e||{};var n=1,l=1;function i(v){var T=v.match(qS);T&&(n+=T.length);var h=v.lastIndexOf(ZS);l=~h?v.length-h:l+v.length}function r(){var v={line:n,column:l};return function(T){return T.position=new a(v),f(),T}}function a(v){this.start=v,this.end={line:n,column:l},this.source=e.source}a.prototype.content=t;var u=[];function o(v){var T=new Error(e.source+":"+n+":"+l+": "+v);if(T.reason=v,T.filename=e.source,T.line=n,T.column=l,T.source=t,e.silent)u.push(T);else throw T}function c(v){var T=v.exec(t);if(T){var h=T[0];return i(h),t=t.slice(h.length),T}}function f(){c(jS)}function s(v){var T;for(v=v||[];T=p();)T!==!1&&v.push(T);return v}function p(){var v=r();if(!(Xy!=t.charAt(0)||Gy!=t.charAt(1))){for(var T=2;Tl!=t.charAt(T)&&(Gy!=t.charAt(T)||Xy!=t.charAt(T+1));)++T;if(T+=2,Tl===t.charAt(T-1))return o("End of comment missing");var h=t.slice(2,T-2);return l+=2,i(h),t=t.slice(T),l+=2,v({type:KS,comment:h})}}function m(){var v=r(),T=c(YS);if(T){if(p(),!c(VS))return o("property missing ':'");var h=c(XS),d=v({type:FS,property:Qy(T[0].replace(Vy,Tl)),value:h?Qy(h[0].replace(Vy,Tl)):Tl});return c(GS),d}}function y(){var v=[];s(v);for(var T;T=m();)T!==!1&&(v.push(T),s(v));return v}return f(),y()};function Qy(t){return t?t.replace(QS,Tl):Tl}});var Fy=Kt($r=>{"use strict";var IS=$r&&$r.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty($r,"__esModule",{value:!0});$r.default=WS;var JS=IS(Ky());function WS(t,e){var n=null;if(!t||typeof t!="string")return n;var l=(0,JS.default)(t),i=typeof e=="function";return l.forEach(function(r){if(r.type==="declaration"){var a=r.property,u=r.value;i?e(a,u,r):u&&(n=n||{},n[a]=u)}}),n}});var Jy=Kt(Zu=>{"use strict";Object.defineProperty(Zu,"__esModule",{value:!0});Zu.camelCase=void 0;var PS=/^--[a-zA-Z0-9_-]+$/,$S=/-([a-z])/g,tk=/^[^-]+$/,ek=/^-(webkit|moz|ms|o|khtml)-/,nk=/^-(ms)-/,lk=function(t){return!t||tk.test(t)||PS.test(t)},ik=function(t,e){return e.toUpperCase()},Iy=function(t,e){return"".concat(e,"-")},rk=function(t,e){return e===void 0&&(e={}),lk(t)?t:(t=t.toLowerCase(),e.reactCompat?t=t.replace(nk,Iy):t=t.replace(ek,Iy),t.replace($S,ik))};Zu.camelCase=rk});var Py=Kt((Ss,Wy)=>{"use strict";var ak=Ss&&Ss.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},uk=ak(Fy()),ok=Jy();function vs(t,e){var n={};return!t||typeof t!="string"||(0,uk.default)(t,function(l,i){l&&i&&(n[(0,ok.camelCase)(l,e)]=i)}),n}vs.default=vs;Wy.exports=vs});var o1=Kt(Fu=>{"use strict";var Ck=Symbol.for("react.transitional.element"),Dk=Symbol.for("react.fragment");function u1(t,e,n){var l=null;if(n!==void 0&&(l=""+n),e.key!==void 0&&(l=""+e.key),"key"in e){n={};for(var i in e)i!=="key"&&(n[i]=e[i])}else n=e;return e=n.ref,{$$typeof:Ck,type:t,key:l,ref:e!==void 0?e:null,props:n}}Fu.Fragment=Dk;Fu.jsx=u1;Fu.jsxs=u1});var Iu=Kt((Iw,c1)=>{"use strict";c1.exports=o1()});var A0=Kt((fM,T0)=>{"use strict";var bo=Object.prototype.hasOwnProperty,E0=Object.prototype.toString,y0=Object.defineProperty,x0=Object.getOwnPropertyDescriptor,b0=function(e){return typeof Array.isArray=="function"?Array.isArray(e):E0.call(e)==="[object Array]"},v0=function(e){if(!e||E0.call(e)!=="[object Object]")return!1;var n=bo.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&bo.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!n&&!l)return!1;var i;for(i in e);return typeof i=="undefined"||bo.call(e,i)},S0=function(e,n){y0&&n.name==="__proto__"?y0(e,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):e[n.name]=n.newValue},k0=function(e,n){if(n==="__proto__")if(bo.call(e,n)){if(x0)return x0(e,n).value}else return;return e[n]};T0.exports=function t(){var e,n,l,i,r,a,u=arguments[0],o=1,c=arguments.length,f=!1;for(typeof u=="boolean"&&(f=u,u=arguments[1]||{},o=2),(u==null||typeof u!="object"&&typeof u!="function")&&(u={});o<c;++o)if(e=arguments[o],e!=null)for(n in e)l=k0(u,n),i=k0(e,n),u!==i&&(f&&i&&(v0(i)||(r=b0(i)))?(r?(r=!1,a=l&&b0(l)?l:[]):a=l&&v0(l)?l:{},S0(u,{name:n,newValue:t(f,a,i)})):typeof i!="undefined"&&S0(u,{name:n,newValue:i}));return u}});var be=bn(Gi()),Tx=bn(Ry());function Ny(t,e){let n=e||{};return(t[t.length-1]===""?[...t,""]:t).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}var MS=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,OS=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,_S={};function Vu(t,e){return((e||_S).jsx?OS:MS).test(t)}var RS=/[ \t\n\f\r]/g;function fs(t){return typeof t=="object"?t.type==="text"?Ly(t.value):!1:Ly(t)}function Ly(t){return t.replace(RS,"")===""}var gn=class{constructor(e,n,l){this.normal=n,this.property=e,l&&(this.space=l)}};gn.prototype.normal={};gn.prototype.property={};gn.prototype.space=void 0;function ss(t,e){let n={},l={};for(let i of t)Object.assign(n,i.property),Object.assign(l,i.normal);return new gn(n,l,e)}function Wr(t){return t.toLowerCase()}var Bt=class{constructor(e,n){this.attribute=n,this.property=e}};Bt.prototype.attribute="";Bt.prototype.booleanish=!1;Bt.prototype.boolean=!1;Bt.prototype.commaOrSpaceSeparated=!1;Bt.prototype.commaSeparated=!1;Bt.prototype.defined=!1;Bt.prototype.mustUseProperty=!1;Bt.prototype.number=!1;Bt.prototype.overloadedBoolean=!1;Bt.prototype.property="";Bt.prototype.spaceSeparated=!1;Bt.prototype.space=void 0;var Pr={};Pm(Pr,{boolean:()=>Y,booleanish:()=>bt,commaOrSpaceSeparated:()=>re,commaSeparated:()=>Kn,number:()=>M,overloadedBoolean:()=>ms,spaceSeparated:()=>lt});var NS=0,Y=kl(),bt=kl(),ms=kl(),M=kl(),lt=kl(),Kn=kl(),re=kl();function kl(){return Jm(2,++NS)}var ps=Object.keys(Pr),El=class extends Bt{constructor(e,n,l,i){let r=-1;if(super(e,n),Uy(this,"space",i),typeof l=="number")for(;++r<ps.length;){let a=ps[r];Uy(this,ps[r],(l&Pr[a])===Pr[a])}}};El.prototype.defined=!0;function Uy(t,e,n){n&&(t[e]=n)}function Me(t){let e={},n={};for(let[l,i]of Object.entries(t.properties)){let r=new El(l,t.transform(t.attributes||{},l),i,t.space);t.mustUseProperty&&t.mustUseProperty.includes(l)&&(r.mustUseProperty=!0),e[l]=r,n[Wr(l)]=l,n[Wr(r.attribute)]=l}return new gn(e,n,t.space)}var hs=Me({properties:{ariaActiveDescendant:null,ariaAtomic:bt,ariaAutoComplete:null,ariaBusy:bt,ariaChecked:bt,ariaColCount:M,ariaColIndex:M,ariaColSpan:M,ariaControls:lt,ariaCurrent:null,ariaDescribedBy:lt,ariaDetails:null,ariaDisabled:bt,ariaDropEffect:lt,ariaErrorMessage:null,ariaExpanded:bt,ariaFlowTo:lt,ariaGrabbed:bt,ariaHasPopup:null,ariaHidden:bt,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:lt,ariaLevel:M,ariaLive:null,ariaModal:bt,ariaMultiLine:bt,ariaMultiSelectable:bt,ariaOrientation:null,ariaOwns:lt,ariaPlaceholder:null,ariaPosInSet:M,ariaPressed:bt,ariaReadOnly:bt,ariaRelevant:null,ariaRequired:bt,ariaRoleDescription:lt,ariaRowCount:M,ariaRowIndex:M,ariaRowSpan:M,ariaSelected:bt,ariaSetSize:M,ariaSort:null,ariaValueMax:M,ariaValueMin:M,ariaValueNow:M,ariaValueText:null,role:null},transform(t,e){return e==="role"?e:"aria-"+e.slice(4).toLowerCase()}});function Xu(t,e){return e in t?t[e]:e}function Gu(t,e){return Xu(t,e.toLowerCase())}var By=Me({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Kn,acceptCharset:lt,accessKey:lt,action:null,allow:null,allowFullScreen:Y,allowPaymentRequest:Y,allowUserMedia:Y,alt:null,as:null,async:Y,autoCapitalize:null,autoComplete:lt,autoFocus:Y,autoPlay:Y,blocking:lt,capture:null,charSet:null,checked:Y,cite:null,className:lt,cols:M,colSpan:null,content:null,contentEditable:bt,controls:Y,controlsList:lt,coords:M|Kn,crossOrigin:null,data:null,dateTime:null,decoding:null,default:Y,defer:Y,dir:null,dirName:null,disabled:Y,download:ms,draggable:bt,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:Y,formTarget:null,headers:lt,height:M,hidden:Y,high:M,href:null,hrefLang:null,htmlFor:lt,httpEquiv:lt,id:null,imageSizes:null,imageSrcSet:null,inert:Y,inputMode:null,integrity:null,is:null,isMap:Y,itemId:null,itemProp:lt,itemRef:lt,itemScope:Y,itemType:lt,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:Y,low:M,manifest:null,max:null,maxLength:M,media:null,method:null,min:null,minLength:M,multiple:Y,muted:Y,name:null,nonce:null,noModule:Y,noValidate:Y,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:Y,optimum:M,pattern:null,ping:lt,placeholder:null,playsInline:Y,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:Y,referrerPolicy:null,rel:lt,required:Y,reversed:Y,rows:M,rowSpan:M,sandbox:lt,scope:null,scoped:Y,seamless:Y,selected:Y,shadowRootClonable:Y,shadowRootDelegatesFocus:Y,shadowRootMode:null,shape:null,size:M,sizes:null,slot:null,span:M,spellCheck:bt,src:null,srcDoc:null,srcLang:null,srcSet:null,start:M,step:null,style:null,tabIndex:M,target:null,title:null,translate:null,type:null,typeMustMatch:Y,useMap:null,value:bt,width:M,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:lt,axis:null,background:null,bgColor:null,border:M,borderColor:null,bottomMargin:M,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:Y,declare:Y,event:null,face:null,frame:null,frameBorder:null,hSpace:M,leftMargin:M,link:null,longDesc:null,lowSrc:null,marginHeight:M,marginWidth:M,noResize:Y,noHref:Y,noShade:Y,noWrap:Y,object:null,profile:null,prompt:null,rev:null,rightMargin:M,rules:null,scheme:null,scrolling:bt,standby:null,summary:null,text:null,topMargin:M,valueType:null,version:null,vAlign:null,vLink:null,vSpace:M,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:Y,disableRemotePlayback:Y,prefix:null,property:null,results:M,security:null,unselectable:null},space:"html",transform:Gu});var Hy=Me({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:re,accentHeight:M,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:M,amplitude:M,arabicForm:null,ascent:M,attributeName:null,attributeType:null,azimuth:M,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:M,by:null,calcMode:null,capHeight:M,className:lt,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:M,diffuseConstant:M,direction:null,display:null,dur:null,divisor:M,dominantBaseline:null,download:Y,dx:null,dy:null,edgeMode:null,editable:null,elevation:M,enableBackground:null,end:null,event:null,exponent:M,externalResourcesRequired:null,fill:null,fillOpacity:M,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Kn,g2:Kn,glyphName:Kn,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:M,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:M,horizOriginX:M,horizOriginY:M,id:null,ideographic:M,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:M,k:M,k1:M,k2:M,k3:M,k4:M,kernelMatrix:re,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:M,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:M,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:M,overlineThickness:M,paintOrder:null,panose1:null,path:null,pathLength:M,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:lt,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:M,pointsAtY:M,pointsAtZ:M,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:re,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:re,rev:re,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:re,requiredFeatures:re,requiredFonts:re,requiredFormats:re,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:M,specularExponent:M,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:M,strikethroughThickness:M,string:null,stroke:null,strokeDashArray:re,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:M,strokeOpacity:M,strokeWidth:null,style:null,surfaceScale:M,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:re,tabIndex:M,tableValues:null,target:null,targetX:M,targetY:M,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:re,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:M,underlineThickness:M,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:M,values:null,vAlphabetic:M,vMathematical:M,vectorEffect:null,vHanging:M,vIdeographic:M,version:null,vertAdvY:M,vertOriginX:M,vertOriginY:M,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:M,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Xu});var ds=Me({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(t,e){return"xlink:"+e.slice(5).toLowerCase()}});var gs=Me({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Gu});var ys=Me({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(t,e){return"xml:"+e.slice(3).toLowerCase()}});var xs={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var LS=/[A-Z]/g,qy=/-[a-z]/g,US=/^data[-\w.:]+$/i;function bs(t,e){let n=Wr(e),l=e,i=Bt;if(n in t.normal)return t.property[t.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&US.test(e)){if(e.charAt(4)==="-"){let r=e.slice(5).replace(qy,HS);l="data"+r.charAt(0).toUpperCase()+r.slice(1)}else{let r=e.slice(4);if(!qy.test(r)){let a=r.replace(LS,BS);a.charAt(0)!=="-"&&(a="-"+a),e="data"+a}}i=El}return new i(l,e)}function BS(t){return"-"+t.toLowerCase()}function HS(t){return t.charAt(1).toUpperCase()}var jy=ss([hs,By,ds,gs,ys],"html"),Qu=ss([hs,Hy,ds,gs,ys],"svg");function Yy(t){return t.join(" ").trim()}var n1=bn(Py(),1);var Ku=$y("end"),Mi=$y("start");function $y(t){return e;function e(n){let l=n&&n.position&&n.position[t]||{};if(typeof l.line=="number"&&l.line>0&&typeof l.column=="number"&&l.column>0)return{line:l.line,column:l.column,offset:typeof l.offset=="number"&&l.offset>-1?l.offset:void 0}}}function ks(t){let e=Mi(t),n=Ku(t);if(e&&n)return{start:e,end:n}}function Fn(t){return!t||typeof t!="object"?"":"position"in t||"type"in t?t1(t.position):"start"in t||"end"in t?t1(t):"line"in t||"column"in t?Es(t):""}function Es(t){return e1(t&&t.line)+":"+e1(t&&t.column)}function t1(t){return Es(t&&t.start)+"-"+Es(t&&t.end)}function e1(t){return t&&typeof t=="number"?t:1}var Tt=class extends Error{constructor(e,n,l){super(),typeof n=="string"&&(l=n,n=void 0);let i="",r={},a=!1;if(n&&("line"in n&&"column"in n?r={place:n}:"start"in n&&"end"in n?r={place:n}:"type"in n?r={ancestors:[n],place:n.position}:r=z({},n)),typeof e=="string"?i=e:!r.cause&&e&&(a=!0,i=e.message,r.cause=e),!r.ruleId&&!r.source&&typeof l=="string"){let o=l.indexOf(":");o===-1?r.ruleId=l:(r.source=l.slice(0,o),r.ruleId=l.slice(o+1))}if(!r.place&&r.ancestors&&r.ancestors){let o=r.ancestors[r.ancestors.length-1];o&&(r.place=o.position)}let u=r.place&&"start"in r.place?r.place.start:r.place;this.ancestors=r.ancestors||void 0,this.cause=r.cause||void 0,this.column=u?u.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=u?u.line:void 0,this.name=Fn(r.place)||"1:1",this.place=r.place||void 0,this.reason=this.message,this.ruleId=r.ruleId||void 0,this.source=r.source||void 0,this.stack=a&&r.cause&&typeof r.cause.stack=="string"?r.cause.stack:"",this.actual,this.expected,this.note,this.url}};Tt.prototype.file="";Tt.prototype.name="";Tt.prototype.reason="";Tt.prototype.message="";Tt.prototype.stack="";Tt.prototype.column=void 0;Tt.prototype.line=void 0;Tt.prototype.ancestors=void 0;Tt.prototype.cause=void 0;Tt.prototype.fatal=void 0;Tt.prototype.place=void 0;Tt.prototype.ruleId=void 0;Tt.prototype.source=void 0;var Ts={}.hasOwnProperty,ck=new Map,fk=/[A-Z]/g,sk=new Set(["table","tbody","thead","tfoot","tr"]),mk=new Set(["td","th"]),l1="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function As(t,e){if(!e||e.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");let n=e.filePath||void 0,l;if(e.development){if(typeof e.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");l=vk(n,e.jsxDEV)}else{if(typeof e.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof e.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");l=bk(n,e.jsx,e.jsxs)}let i={Fragment:e.Fragment,ancestors:[],components:e.components||{},create:l,elementAttributeNameCase:e.elementAttributeNameCase||"react",evaluater:e.createEvaluater?e.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:e.ignoreInvalidStyle||!1,passKeys:e.passKeys!==!1,passNode:e.passNode||!1,schema:e.space==="svg"?Qu:jy,stylePropertyNameCase:e.stylePropertyNameCase||"dom",tableCellAlignToStyle:e.tableCellAlignToStyle!==!1},r=i1(i,t,void 0);return r&&typeof r!="string"?r:i.create(t,i.Fragment,{children:r||void 0},void 0)}function i1(t,e,n){if(e.type==="element")return pk(t,e,n);if(e.type==="mdxFlowExpression"||e.type==="mdxTextExpression")return hk(t,e);if(e.type==="mdxJsxFlowElement"||e.type==="mdxJsxTextElement")return gk(t,e,n);if(e.type==="mdxjsEsm")return dk(t,e);if(e.type==="root")return yk(t,e,n);if(e.type==="text")return xk(t,e)}function pk(t,e,n){let l=t.schema,i=l;e.tagName.toLowerCase()==="svg"&&l.space==="html"&&(i=Qu,t.schema=i),t.ancestors.push(e);let r=a1(t,e.tagName,!1),a=Sk(t,e),u=zs(t,e);return sk.has(e.tagName)&&(u=u.filter(function(o){return typeof o=="string"?!fs(o):!0})),r1(t,a,r,e),ws(a,u),t.ancestors.pop(),t.schema=l,t.create(e,r,a,n)}function hk(t,e){if(e.data&&e.data.estree&&t.evaluater){let l=e.data.estree.body[0];return l.type,t.evaluater.evaluateExpression(l.expression)}ta(t,e.position)}function dk(t,e){if(e.data&&e.data.estree&&t.evaluater)return t.evaluater.evaluateProgram(e.data.estree);ta(t,e.position)}function gk(t,e,n){let l=t.schema,i=l;e.name==="svg"&&l.space==="html"&&(i=Qu,t.schema=i),t.ancestors.push(e);let r=e.name===null?t.Fragment:a1(t,e.name,!0),a=kk(t,e),u=zs(t,e);return r1(t,a,r,e),ws(a,u),t.ancestors.pop(),t.schema=l,t.create(e,r,a,n)}function yk(t,e,n){let l={};return ws(l,zs(t,e)),t.create(e,t.Fragment,l,n)}function xk(t,e){return e.value}function r1(t,e,n,l){typeof n!="string"&&n!==t.Fragment&&t.passNode&&(e.node=l)}function ws(t,e){if(e.length>0){let n=e.length>1?e:e[0];n&&(t.children=n)}}function bk(t,e,n){return l;function l(i,r,a,u){let c=Array.isArray(a.children)?n:e;return u?c(r,a,u):c(r,a)}}function vk(t,e){return n;function n(l,i,r,a){let u=Array.isArray(r.children),o=Mi(l);return e(i,r,a,u,{columnNumber:o?o.column-1:void 0,fileName:t,lineNumber:o?o.line:void 0},void 0)}}function Sk(t,e){let n={},l,i;for(i in e.properties)if(i!=="children"&&Ts.call(e.properties,i)){let r=Ek(t,i,e.properties[i]);if(r){let[a,u]=r;t.tableCellAlignToStyle&&a==="align"&&typeof u=="string"&&mk.has(e.tagName)?l=u:n[a]=u}}if(l){let r=n.style||(n.style={});r[t.stylePropertyNameCase==="css"?"text-align":"textAlign"]=l}return n}function kk(t,e){let n={};for(let l of e.attributes)if(l.type==="mdxJsxExpressionAttribute")if(l.data&&l.data.estree&&t.evaluater){let r=l.data.estree.body[0];r.type;let a=r.expression;a.type;let u=a.properties[0];u.type,Object.assign(n,t.evaluater.evaluateExpression(u.argument))}else ta(t,e.position);else{let i=l.name,r;if(l.value&&typeof l.value=="object")if(l.value.data&&l.value.data.estree&&t.evaluater){let u=l.value.data.estree.body[0];u.type,r=t.evaluater.evaluateExpression(u.expression)}else ta(t,e.position);else r=l.value===null?!0:l.value;n[i]=r}return n}function zs(t,e){let n=[],l=-1,i=t.passKeys?new Map:ck;for(;++l<e.children.length;){let r=e.children[l],a;if(t.passKeys){let o=r.type==="element"?r.tagName:r.type==="mdxJsxFlowElement"||r.type==="mdxJsxTextElement"?r.name:void 0;if(o){let c=i.get(o)||0;a=o+"-"+c,i.set(o,c+1)}}let u=i1(t,r,a);u!==void 0&&n.push(u)}return n}function Ek(t,e,n){let l=bs(t.schema,e);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=l.commaSeparated?Ny(n):Yy(n)),l.property==="style"){let i=typeof n=="object"?n:Tk(t,String(n));return t.stylePropertyNameCase==="css"&&(i=Ak(i)),["style",i]}return[t.elementAttributeNameCase==="react"&&l.space?xs[l.property]||l.property:l.attribute,n]}}function Tk(t,e){try{return(0,n1.default)(e,{reactCompat:!0})}catch(n){if(t.ignoreInvalidStyle)return{};let l=n,i=new Tt("Cannot parse `style` attribute",{ancestors:t.ancestors,cause:l,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=t.filePath||void 0,i.url=l1+"#cannot-parse-style-attribute",i}}function a1(t,e,n){let l;if(!n)l={type:"Literal",value:e};else if(e.includes(".")){let i=e.split("."),r=-1,a;for(;++r<i.length;){let u=Vu(i[r])?{type:"Identifier",name:i[r]}:{type:"Literal",value:i[r]};a=a?{type:"MemberExpression",object:a,property:u,computed:!!(r&&u.type==="Literal"),optional:!1}:u}l=a}else l=Vu(e)&&!/^[a-z]/.test(e)?{type:"Identifier",name:e}:{type:"Literal",value:e};if(l.type==="Literal"){let i=l.value;return Ts.call(t.components,i)?t.components[i]:i}if(t.evaluater)return t.evaluater.evaluateExpression(l);ta(t)}function ta(t,e){let n=new Tt("Cannot handle MDX estrees without `createEvaluater`",{ancestors:t.ancestors,place:e,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=t.filePath||void 0,n.url=l1+"#cannot-handle-mdx-estrees-without-createevaluater",n}function Ak(t){let e={},n;for(n in t)Ts.call(t,n)&&(e[wk(n)]=t[n]);return e}function wk(t){let e=t.replace(fk,zk);return e.slice(0,3)==="ms-"&&(e="-"+e),e}function zk(t){return"-"+t.toLowerCase()}var ea={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var Li=bn(Iu(),1),L0=bn(Gi(),1);var Mk={};function Al(t,e){let n=e||Mk,l=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return s1(t,l,i)}function s1(t,e,n){if(Ok(t)){if("value"in t)return t.type==="html"&&!n?"":t.value;if(e&&"alt"in t&&t.alt)return t.alt;if("children"in t)return f1(t.children,e,n)}return Array.isArray(t)?f1(t,e,n):""}function f1(t,e,n){let l=[],i=-1;for(;++i<t.length;)l[i]=s1(t[i],e,n);return l.join("")}function Ok(t){return!!(t&&typeof t=="object")}var m1=document.createElement("i");function Oi(t){let e="&"+t+";";m1.innerHTML=e;let n=m1.textContent;return n.charCodeAt(n.length-1)===59&&t!=="semi"||n===e?!1:n}function At(t,e,n,l){let i=t.length,r=0,a;if(e<0?e=-e>i?0:i+e:e=e>i?i:e,n=n>0?n:0,l.length<1e4)a=Array.from(l),a.unshift(e,n),t.splice(...a);else for(n&&t.splice(e,n);r<l.length;)a=l.slice(r,r+1e4),a.unshift(e,0),t.splice(...a),r+=1e4,e+=1e4}function Wt(t,e){return t.length>0?(At(t,t.length,0,e),t):e}var p1={}.hasOwnProperty;function Ju(t){let e={},n=-1;for(;++n<t.length;)_k(e,t[n]);return e}function _k(t,e){let n;for(n in e){let i=(p1.call(t,n)?t[n]:void 0)||(t[n]={}),r=e[n],a;if(r)for(a in r){p1.call(i,a)||(i[a]=[]);let u=r[a];Rk(i[a],Array.isArray(u)?u:u?[u]:[])}}}function Rk(t,e){let n=-1,l=[];for(;++n<e.length;)(e[n].add==="after"?t:l).push(e[n]);At(t,0,0,l)}function Wu(t,e){let n=Number.parseInt(t,e);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"\uFFFD":String.fromCodePoint(n)}function Qt(t){return t.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var Ot=In(/[A-Za-z]/),vt=In(/[\dA-Za-z]/),h1=In(/[#-'*+\--9=?A-Z^-~]/);function wl(t){return t!==null&&(t<32||t===127)}var na=In(/\d/),d1=In(/[\dA-Fa-f]/),g1=In(/[!-/:-@[-`{-~]/);function R(t){return t!==null&&t<-2}function Z(t){return t!==null&&(t<0||t===32)}function B(t){return t===-2||t===-1||t===32}var zl=In(new RegExp("\\p{P}|\\p{S}","u")),Ze=In(/\s/);function In(t){return e;function e(n){return n!==null&&n>-1&&t.test(String.fromCharCode(n))}}function Oe(t){let e=[],n=-1,l=0,i=0;for(;++n<t.length;){let r=t.charCodeAt(n),a="";if(r===37&&vt(t.charCodeAt(n+1))&&vt(t.charCodeAt(n+2)))i=2;else if(r<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(r))||(a=String.fromCharCode(r));else if(r>55295&&r<57344){let u=t.charCodeAt(n+1);r<56320&&u>56319&&u<57344?(a=String.fromCharCode(r,u),i=1):a="\uFFFD"}else a=String.fromCharCode(r);a&&(e.push(t.slice(l,n),encodeURIComponent(a)),l=n+i+1,a=""),i&&(n+=i,i=0)}return e.join("")+t.slice(l)}function U(t,e,n,l){let i=l?l-1:Number.POSITIVE_INFINITY,r=0;return a;function a(o){return B(o)?(t.enter(n),u(o)):e(o)}function u(o){return B(o)&&r++<i?(t.consume(o),u):(t.exit(n),e(o))}}var y1={tokenize:Nk};function Nk(t){let e=t.attempt(this.parser.constructs.contentInitial,l,i),n;return e;function l(u){if(u===null){t.consume(u);return}return t.enter("lineEnding"),t.consume(u),t.exit("lineEnding"),U(t,e,"linePrefix")}function i(u){return t.enter("paragraph"),r(u)}function r(u){let o=t.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=o),n=o,a(u)}function a(u){if(u===null){t.exit("chunkText"),t.exit("paragraph"),t.consume(u);return}return R(u)?(t.consume(u),t.exit("chunkText"),r):(t.consume(u),a)}}var b1={tokenize:Lk},x1={tokenize:Uk};function Lk(t){let e=this,n=[],l=0,i,r,a;return u;function u(g){if(l<n.length){let E=n[l];return e.containerState=E[1],t.attempt(E[0].continuation,o,c)(g)}return c(g)}function o(g){if(l++,e.containerState._closeFlow){e.containerState._closeFlow=void 0,i&&d();let E=e.events.length,C=E,k;for(;C--;)if(e.events[C][0]==="exit"&&e.events[C][1].type==="chunkFlow"){k=e.events[C][1].end;break}h(l);let D=E;for(;D<e.events.length;)e.events[D][1].end=z({},k),D++;return At(e.events,C+1,0,e.events.slice(E)),e.events.length=D,c(g)}return u(g)}function c(g){if(l===n.length){if(!i)return p(g);if(i.currentConstruct&&i.currentConstruct.concrete)return y(g);e.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return e.containerState={},t.check(x1,f,s)(g)}function f(g){return i&&d(),h(l),p(g)}function s(g){return e.parser.lazy[e.now().line]=l!==n.length,a=e.now().offset,y(g)}function p(g){return e.containerState={},t.attempt(x1,m,y)(g)}function m(g){return l++,n.push([e.currentConstruct,e.containerState]),p(g)}function y(g){if(g===null){i&&d(),h(0),t.consume(g);return}return i=i||e.parser.flow(e.now()),t.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:r}),v(g)}function v(g){if(g===null){T(t.exit("chunkFlow"),!0),h(0),t.consume(g);return}return R(g)?(t.consume(g),T(t.exit("chunkFlow")),l=0,e.interrupt=void 0,u):(t.consume(g),v)}function T(g,E){let C=e.sliceStream(g);if(E&&C.push(null),g.previous=r,r&&(r.next=g),r=g,i.defineSkip(g.start),i.write(C),e.parser.lazy[g.start.line]){let k=i.events.length;for(;k--;)if(i.events[k][1].start.offset<a&&(!i.events[k][1].end||i.events[k][1].end.offset>a))return;let D=e.events.length,O=D,L,S;for(;O--;)if(e.events[O][0]==="exit"&&e.events[O][1].type==="chunkFlow"){if(L){S=e.events[O][1].end;break}L=!0}for(h(l),k=D;k<e.events.length;)e.events[k][1].end=z({},S),k++;At(e.events,O+1,0,e.events.slice(D)),e.events.length=k}}function h(g){let E=n.length;for(;E-- >g;){let C=n[E];e.containerState=C[1],C[0].exit.call(e,t)}n.length=g}function d(){i.write([null]),r=void 0,i=void 0,e.containerState._closeFlow=void 0}}function Uk(t,e,n){return U(t,t.attempt(this.parser.constructs.document,e,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function yn(t){if(t===null||Z(t)||Ze(t))return 1;if(zl(t))return 2}function Jn(t,e,n){let l=[],i=-1;for(;++i<t.length;){let r=t[i].resolveAll;r&&!l.includes(r)&&(e=r(e,n),l.push(r))}return e}var la={name:"attention",resolveAll:Bk,tokenize:Hk};function Bk(t,e){let n=-1,l,i,r,a,u,o,c,f;for(;++n<t.length;)if(t[n][0]==="enter"&&t[n][1].type==="attentionSequence"&&t[n][1]._close){for(l=n;l--;)if(t[l][0]==="exit"&&t[l][1].type==="attentionSequence"&&t[l][1]._open&&e.sliceSerialize(t[l][1]).charCodeAt(0)===e.sliceSerialize(t[n][1]).charCodeAt(0)){if((t[l][1]._close||t[n][1]._open)&&(t[n][1].end.offset-t[n][1].start.offset)%3&&!((t[l][1].end.offset-t[l][1].start.offset+t[n][1].end.offset-t[n][1].start.offset)%3))continue;o=t[l][1].end.offset-t[l][1].start.offset>1&&t[n][1].end.offset-t[n][1].start.offset>1?2:1;let s=z({},t[l][1].end),p=z({},t[n][1].start);v1(s,-o),v1(p,o),a={type:o>1?"strongSequence":"emphasisSequence",start:s,end:z({},t[l][1].end)},u={type:o>1?"strongSequence":"emphasisSequence",start:z({},t[n][1].start),end:p},r={type:o>1?"strongText":"emphasisText",start:z({},t[l][1].end),end:z({},t[n][1].start)},i={type:o>1?"strong":"emphasis",start:z({},a.start),end:z({},u.end)},t[l][1].end=z({},a.start),t[n][1].start=z({},u.end),c=[],t[l][1].end.offset-t[l][1].start.offset&&(c=Wt(c,[["enter",t[l][1],e],["exit",t[l][1],e]])),c=Wt(c,[["enter",i,e],["enter",a,e],["exit",a,e],["enter",r,e]]),c=Wt(c,Jn(e.parser.constructs.insideSpan.null,t.slice(l+1,n),e)),c=Wt(c,[["exit",r,e],["enter",u,e],["exit",u,e],["exit",i,e]]),t[n][1].end.offset-t[n][1].start.offset?(f=2,c=Wt(c,[["enter",t[n][1],e],["exit",t[n][1],e]])):f=0,At(t,l-1,n-l+3,c),n=l+c.length-f-2;break}}for(n=-1;++n<t.length;)t[n][1].type==="attentionSequence"&&(t[n][1].type="data");return t}function Hk(t,e){let n=this.parser.constructs.attentionMarkers.null,l=this.previous,i=yn(l),r;return a;function a(o){return r=o,t.enter("attentionSequence"),u(o)}function u(o){if(o===r)return t.consume(o),u;let c=t.exit("attentionSequence"),f=yn(o),s=!f||f===2&&i||n.includes(o),p=!i||i===2&&f||n.includes(l);return c._open=!!(r===42?s:s&&(i||!p)),c._close=!!(r===42?p:p&&(f||!s)),e(o)}}function v1(t,e){t.column+=e,t.offset+=e,t._bufferIndex+=e}var Cs={name:"autolink",tokenize:qk};function qk(t,e,n){let l=0;return i;function i(m){return t.enter("autolink"),t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.enter("autolinkProtocol"),r}function r(m){return Ot(m)?(t.consume(m),a):m===64?n(m):c(m)}function a(m){return m===43||m===45||m===46||vt(m)?(l=1,u(m)):c(m)}function u(m){return m===58?(t.consume(m),l=0,o):(m===43||m===45||m===46||vt(m))&&l++<32?(t.consume(m),u):(l=0,c(m))}function o(m){return m===62?(t.exit("autolinkProtocol"),t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.exit("autolink"),e):m===null||m===32||m===60||wl(m)?n(m):(t.consume(m),o)}function c(m){return m===64?(t.consume(m),f):h1(m)?(t.consume(m),c):n(m)}function f(m){return vt(m)?s(m):n(m)}function s(m){return m===46?(t.consume(m),l=0,f):m===62?(t.exit("autolinkProtocol").type="autolinkEmail",t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.exit("autolink"),e):p(m)}function p(m){if((m===45||vt(m))&&l++<63){let y=m===45?p:s;return t.consume(m),y}return n(m)}}var Ke={partial:!0,tokenize:jk};function jk(t,e,n){return l;function l(r){return B(r)?U(t,i,"linePrefix")(r):i(r)}function i(r){return r===null||R(r)?e(r):n(r)}}var Pu={continuation:{tokenize:Vk},exit:Xk,name:"blockQuote",tokenize:Yk};function Yk(t,e,n){let l=this;return i;function i(a){if(a===62){let u=l.containerState;return u.open||(t.enter("blockQuote",{_container:!0}),u.open=!0),t.enter("blockQuotePrefix"),t.enter("blockQuoteMarker"),t.consume(a),t.exit("blockQuoteMarker"),r}return n(a)}function r(a){return B(a)?(t.enter("blockQuotePrefixWhitespace"),t.consume(a),t.exit("blockQuotePrefixWhitespace"),t.exit("blockQuotePrefix"),e):(t.exit("blockQuotePrefix"),e(a))}}function Vk(t,e,n){let l=this;return i;function i(a){return B(a)?U(t,r,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a):r(a)}function r(a){return t.attempt(Pu,e,n)(a)}}function Xk(t){t.exit("blockQuote")}var $u={name:"characterEscape",tokenize:Gk};function Gk(t,e,n){return l;function l(r){return t.enter("characterEscape"),t.enter("escapeMarker"),t.consume(r),t.exit("escapeMarker"),i}function i(r){return g1(r)?(t.enter("characterEscapeValue"),t.consume(r),t.exit("characterEscapeValue"),t.exit("characterEscape"),e):n(r)}}var to={name:"characterReference",tokenize:Qk};function Qk(t,e,n){let l=this,i=0,r,a;return u;function u(s){return t.enter("characterReference"),t.enter("characterReferenceMarker"),t.consume(s),t.exit("characterReferenceMarker"),o}function o(s){return s===35?(t.enter("characterReferenceMarkerNumeric"),t.consume(s),t.exit("characterReferenceMarkerNumeric"),c):(t.enter("characterReferenceValue"),r=31,a=vt,f(s))}function c(s){return s===88||s===120?(t.enter("characterReferenceMarkerHexadecimal"),t.consume(s),t.exit("characterReferenceMarkerHexadecimal"),t.enter("characterReferenceValue"),r=6,a=d1,f):(t.enter("characterReferenceValue"),r=7,a=na,f(s))}function f(s){if(s===59&&i){let p=t.exit("characterReferenceValue");return a===vt&&!Oi(l.sliceSerialize(p))?n(s):(t.enter("characterReferenceMarker"),t.consume(s),t.exit("characterReferenceMarker"),t.exit("characterReference"),e)}return a(s)&&i++<r?(t.consume(s),f):n(s)}}var S1={partial:!0,tokenize:Kk},eo={concrete:!0,name:"codeFenced",tokenize:Zk};function Zk(t,e,n){let l=this,i={partial:!0,tokenize:C},r=0,a=0,u;return o;function o(k){return c(k)}function c(k){let D=l.events[l.events.length-1];return r=D&&D[1].type==="linePrefix"?D[2].sliceSerialize(D[1],!0).length:0,u=k,t.enter("codeFenced"),t.enter("codeFencedFence"),t.enter("codeFencedFenceSequence"),f(k)}function f(k){return k===u?(a++,t.consume(k),f):a<3?n(k):(t.exit("codeFencedFenceSequence"),B(k)?U(t,s,"whitespace")(k):s(k))}function s(k){return k===null||R(k)?(t.exit("codeFencedFence"),l.interrupt?e(k):t.check(S1,v,E)(k)):(t.enter("codeFencedFenceInfo"),t.enter("chunkString",{contentType:"string"}),p(k))}function p(k){return k===null||R(k)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),s(k)):B(k)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),U(t,m,"whitespace")(k)):k===96&&k===u?n(k):(t.consume(k),p)}function m(k){return k===null||R(k)?s(k):(t.enter("codeFencedFenceMeta"),t.enter("chunkString",{contentType:"string"}),y(k))}function y(k){return k===null||R(k)?(t.exit("chunkString"),t.exit("codeFencedFenceMeta"),s(k)):k===96&&k===u?n(k):(t.consume(k),y)}function v(k){return t.attempt(i,E,T)(k)}function T(k){return t.enter("lineEnding"),t.consume(k),t.exit("lineEnding"),h}function h(k){return r>0&&B(k)?U(t,d,"linePrefix",r+1)(k):d(k)}function d(k){return k===null||R(k)?t.check(S1,v,E)(k):(t.enter("codeFlowValue"),g(k))}function g(k){return k===null||R(k)?(t.exit("codeFlowValue"),d(k)):(t.consume(k),g)}function E(k){return t.exit("codeFenced"),e(k)}function C(k,D,O){let L=0;return S;function S(j){return k.enter("lineEnding"),k.consume(j),k.exit("lineEnding"),P}function P(j){return k.enter("codeFencedFence"),B(j)?U(k,Q,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(j):Q(j)}function Q(j){return j===u?(k.enter("codeFencedFenceSequence"),N(j)):O(j)}function N(j){return j===u?(L++,k.consume(j),N):L>=a?(k.exit("codeFencedFenceSequence"),B(j)?U(k,q,"whitespace")(j):q(j)):O(j)}function q(j){return j===null||R(j)?(k.exit("codeFencedFence"),D(j)):O(j)}}}function Kk(t,e,n){let l=this;return i;function i(a){return a===null?n(a):(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),r)}function r(a){return l.parser.lazy[l.now().line]?n(a):e(a)}}var ia={name:"codeIndented",tokenize:Ik},Fk={partial:!0,tokenize:Jk};function Ik(t,e,n){let l=this;return i;function i(c){return t.enter("codeIndented"),U(t,r,"linePrefix",5)(c)}function r(c){let f=l.events[l.events.length-1];return f&&f[1].type==="linePrefix"&&f[2].sliceSerialize(f[1],!0).length>=4?a(c):n(c)}function a(c){return c===null?o(c):R(c)?t.attempt(Fk,a,o)(c):(t.enter("codeFlowValue"),u(c))}function u(c){return c===null||R(c)?(t.exit("codeFlowValue"),a(c)):(t.consume(c),u)}function o(c){return t.exit("codeIndented"),e(c)}}function Jk(t,e,n){let l=this;return i;function i(a){return l.parser.lazy[l.now().line]?n(a):R(a)?(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),i):U(t,r,"linePrefix",5)(a)}function r(a){let u=l.events[l.events.length-1];return u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?e(a):R(a)?i(a):n(a)}}var Ds={name:"codeText",previous:Pk,resolve:Wk,tokenize:$k};function Wk(t){let e=t.length-4,n=3,l,i;if((t[n][1].type==="lineEnding"||t[n][1].type==="space")&&(t[e][1].type==="lineEnding"||t[e][1].type==="space")){for(l=n;++l<e;)if(t[l][1].type==="codeTextData"){t[n][1].type="codeTextPadding",t[e][1].type="codeTextPadding",n+=2,e-=2;break}}for(l=n-1,e++;++l<=e;)i===void 0?l!==e&&t[l][1].type!=="lineEnding"&&(i=l):(l===e||t[l][1].type==="lineEnding")&&(t[i][1].type="codeTextData",l!==i+2&&(t[i][1].end=t[l-1][1].end,t.splice(i+2,l-i-2),e-=l-i-2,l=i+2),i=void 0);return t}function Pk(t){return t!==96||this.events[this.events.length-1][1].type==="characterEscape"}function $k(t,e,n){let l=this,i=0,r,a;return u;function u(p){return t.enter("codeText"),t.enter("codeTextSequence"),o(p)}function o(p){return p===96?(t.consume(p),i++,o):(t.exit("codeTextSequence"),c(p))}function c(p){return p===null?n(p):p===32?(t.enter("space"),t.consume(p),t.exit("space"),c):p===96?(a=t.enter("codeTextSequence"),r=0,s(p)):R(p)?(t.enter("lineEnding"),t.consume(p),t.exit("lineEnding"),c):(t.enter("codeTextData"),f(p))}function f(p){return p===null||p===32||p===96||R(p)?(t.exit("codeTextData"),c(p)):(t.consume(p),f)}function s(p){return p===96?(t.consume(p),r++,s):r===i?(t.exit("codeTextSequence"),t.exit("codeText"),e(p)):(a.type="codeTextData",f(p))}}var no=class{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,n){let l=n==null?Number.POSITIVE_INFINITY:n;return l<this.left.length?this.left.slice(e,l):e>this.left.length?this.right.slice(this.right.length-l+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-l+this.left.length).reverse())}splice(e,n,l){let i=n||0;this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return l&&ra(this.left,l),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ra(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ra(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&this.right.length===0||e<0&&this.left.length===0))if(e<this.left.length){let n=this.left.splice(e,Number.POSITIVE_INFINITY);ra(this.right,n.reverse())}else{let n=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ra(this.left,n.reverse())}}};function ra(t,e){let n=0;if(e.length<1e4)t.push(...e);else for(;n<e.length;)t.push(...e.slice(n,n+1e4)),n+=1e4}function lo(t){let e={},n=-1,l,i,r,a,u,o,c,f=new no(t);for(;++n<f.length;){for(;n in e;)n=e[n];if(l=f.get(n),n&&l[1].type==="chunkFlow"&&f.get(n-1)[1].type==="listItemPrefix"&&(o=l[1]._tokenizer.events,r=0,r<o.length&&o[r][1].type==="lineEndingBlank"&&(r+=2),r<o.length&&o[r][1].type==="content"))for(;++r<o.length&&o[r][1].type!=="content";)o[r][1].type==="chunkText"&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if(l[0]==="enter")l[1].contentType&&(Object.assign(e,tE(f,n)),n=e[n],c=!0);else if(l[1]._container){for(r=n,i=void 0;r--;)if(a=f.get(r),a[1].type==="lineEnding"||a[1].type==="lineEndingBlank")a[0]==="enter"&&(i&&(f.get(i)[1].type="lineEndingBlank"),a[1].type="lineEnding",i=r);else if(!(a[1].type==="linePrefix"||a[1].type==="listItemIndent"))break;i&&(l[1].end=z({},f.get(i)[1].start),u=f.slice(i,n),u.unshift(l),f.splice(i,n-i+1,u))}}return At(t,0,Number.POSITIVE_INFINITY,f.slice(0)),!c}function tE(t,e){let n=t.get(e)[1],l=t.get(e)[2],i=e-1,r=[],a=n._tokenizer;a||(a=l.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(a._contentTypeTextTrailing=!0));let u=a.events,o=[],c={},f,s,p=-1,m=n,y=0,v=0,T=[v];for(;m;){for(;t.get(++i)[1]!==m;);r.push(i),m._tokenizer||(f=l.sliceStream(m),m.next||f.push(null),s&&a.defineSkip(m.start),m._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(f),m._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),s=m,m=m.next}for(m=n;++p<u.length;)u[p][0]==="exit"&&u[p-1][0]==="enter"&&u[p][1].type===u[p-1][1].type&&u[p][1].start.line!==u[p][1].end.line&&(v=p+1,T.push(v),m._tokenizer=void 0,m.previous=void 0,m=m.next);for(a.events=[],m?(m._tokenizer=void 0,m.previous=void 0):T.pop(),p=T.length;p--;){let h=u.slice(T[p],T[p+1]),d=r.pop();o.push([d,d+h.length-1]),t.splice(d,2,h)}for(o.reverse(),p=-1;++p<o.length;)c[y+o[p][0]]=y+o[p][1],y+=o[p][1]-o[p][0]-1;return c}var Ms={resolve:nE,tokenize:lE},eE={partial:!0,tokenize:iE};function nE(t){return lo(t),t}function lE(t,e){let n;return l;function l(u){return t.enter("content"),n=t.enter("chunkContent",{contentType:"content"}),i(u)}function i(u){return u===null?r(u):R(u)?t.check(eE,a,r)(u):(t.consume(u),i)}function r(u){return t.exit("chunkContent"),t.exit("content"),e(u)}function a(u){return t.consume(u),t.exit("chunkContent"),n.next=t.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function iE(t,e,n){let l=this;return i;function i(a){return t.exit("chunkContent"),t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),U(t,r,"linePrefix")}function r(a){if(a===null||R(a))return n(a);let u=l.events[l.events.length-1];return!l.parser.constructs.disable.null.includes("codeIndented")&&u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?e(a):t.interrupt(l.parser.constructs.flow,n,e)(a)}}function io(t,e,n,l,i,r,a,u,o){let c=o||Number.POSITIVE_INFINITY,f=0;return s;function s(h){return h===60?(t.enter(l),t.enter(i),t.enter(r),t.consume(h),t.exit(r),p):h===null||h===32||h===41||wl(h)?n(h):(t.enter(l),t.enter(a),t.enter(u),t.enter("chunkString",{contentType:"string"}),v(h))}function p(h){return h===62?(t.enter(r),t.consume(h),t.exit(r),t.exit(i),t.exit(l),e):(t.enter(u),t.enter("chunkString",{contentType:"string"}),m(h))}function m(h){return h===62?(t.exit("chunkString"),t.exit(u),p(h)):h===null||h===60||R(h)?n(h):(t.consume(h),h===92?y:m)}function y(h){return h===60||h===62||h===92?(t.consume(h),m):m(h)}function v(h){return!f&&(h===null||h===41||Z(h))?(t.exit("chunkString"),t.exit(u),t.exit(a),t.exit(l),e(h)):f<c&&h===40?(t.consume(h),f++,v):h===41?(t.consume(h),f--,v):h===null||h===32||h===40||wl(h)?n(h):(t.consume(h),h===92?T:v)}function T(h){return h===40||h===41||h===92?(t.consume(h),v):v(h)}}function ro(t,e,n,l,i,r){let a=this,u=0,o;return c;function c(m){return t.enter(l),t.enter(i),t.consume(m),t.exit(i),t.enter(r),f}function f(m){return u>999||m===null||m===91||m===93&&!o||m===94&&!u&&"_hiddenFootnoteSupport"in a.parser.constructs?n(m):m===93?(t.exit(r),t.enter(i),t.consume(m),t.exit(i),t.exit(l),e):R(m)?(t.enter("lineEnding"),t.consume(m),t.exit("lineEnding"),f):(t.enter("chunkString",{contentType:"string"}),s(m))}function s(m){return m===null||m===91||m===93||R(m)||u++>999?(t.exit("chunkString"),f(m)):(t.consume(m),o||(o=!B(m)),m===92?p:s)}function p(m){return m===91||m===92||m===93?(t.consume(m),u++,s):s(m)}}function ao(t,e,n,l,i,r){let a;return u;function u(p){return p===34||p===39||p===40?(t.enter(l),t.enter(i),t.consume(p),t.exit(i),a=p===40?41:p,o):n(p)}function o(p){return p===a?(t.enter(i),t.consume(p),t.exit(i),t.exit(l),e):(t.enter(r),c(p))}function c(p){return p===a?(t.exit(r),o(a)):p===null?n(p):R(p)?(t.enter("lineEnding"),t.consume(p),t.exit("lineEnding"),U(t,c,"linePrefix")):(t.enter("chunkString",{contentType:"string"}),f(p))}function f(p){return p===a||p===null||R(p)?(t.exit("chunkString"),c(p)):(t.consume(p),p===92?s:f)}function s(p){return p===a||p===92?(t.consume(p),f):f(p)}}function Cl(t,e){let n;return l;function l(i){return R(i)?(t.enter("lineEnding"),t.consume(i),t.exit("lineEnding"),n=!0,l):B(i)?U(t,l,n?"linePrefix":"lineSuffix")(i):e(i)}}var Os={name:"definition",tokenize:aE},rE={partial:!0,tokenize:uE};function aE(t,e,n){let l=this,i;return r;function r(m){return t.enter("definition"),a(m)}function a(m){return ro.call(l,t,u,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(m)}function u(m){return i=Qt(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)),m===58?(t.enter("definitionMarker"),t.consume(m),t.exit("definitionMarker"),o):n(m)}function o(m){return Z(m)?Cl(t,c)(m):c(m)}function c(m){return io(t,f,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(m)}function f(m){return t.attempt(rE,s,s)(m)}function s(m){return B(m)?U(t,p,"whitespace")(m):p(m)}function p(m){return m===null||R(m)?(t.exit("definition"),l.parser.defined.push(i),e(m)):n(m)}}function uE(t,e,n){return l;function l(u){return Z(u)?Cl(t,i)(u):n(u)}function i(u){return ao(t,r,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(u)}function r(u){return B(u)?U(t,a,"whitespace")(u):a(u)}function a(u){return u===null||R(u)?e(u):n(u)}}var _s={name:"hardBreakEscape",tokenize:oE};function oE(t,e,n){return l;function l(r){return t.enter("hardBreakEscape"),t.consume(r),i}function i(r){return R(r)?(t.exit("hardBreakEscape"),e(r)):n(r)}}var Rs={name:"headingAtx",resolve:cE,tokenize:fE};function cE(t,e){let n=t.length-2,l=3,i,r;return t[l][1].type==="whitespace"&&(l+=2),n-2>l&&t[n][1].type==="whitespace"&&(n-=2),t[n][1].type==="atxHeadingSequence"&&(l===n-1||n-4>l&&t[n-2][1].type==="whitespace")&&(n-=l+1===n?2:4),n>l&&(i={type:"atxHeadingText",start:t[l][1].start,end:t[n][1].end},r={type:"chunkText",start:t[l][1].start,end:t[n][1].end,contentType:"text"},At(t,l,n-l+1,[["enter",i,e],["enter",r,e],["exit",r,e],["exit",i,e]])),t}function fE(t,e,n){let l=0;return i;function i(f){return t.enter("atxHeading"),r(f)}function r(f){return t.enter("atxHeadingSequence"),a(f)}function a(f){return f===35&&l++<6?(t.consume(f),a):f===null||Z(f)?(t.exit("atxHeadingSequence"),u(f)):n(f)}function u(f){return f===35?(t.enter("atxHeadingSequence"),o(f)):f===null||R(f)?(t.exit("atxHeading"),e(f)):B(f)?U(t,u,"whitespace")(f):(t.enter("atxHeadingText"),c(f))}function o(f){return f===35?(t.consume(f),o):(t.exit("atxHeadingSequence"),u(f))}function c(f){return f===null||f===35||Z(f)?(t.exit("atxHeadingText"),u(f)):(t.consume(f),c)}}var k1=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Ns=["pre","script","style","textarea"];var Ls={concrete:!0,name:"htmlFlow",resolveTo:pE,tokenize:hE},sE={partial:!0,tokenize:gE},mE={partial:!0,tokenize:dE};function pE(t){let e=t.length;for(;e--&&!(t[e][0]==="enter"&&t[e][1].type==="htmlFlow"););return e>1&&t[e-2][1].type==="linePrefix"&&(t[e][1].start=t[e-2][1].start,t[e+1][1].start=t[e-2][1].start,t.splice(e-2,2)),t}function hE(t,e,n){let l=this,i,r,a,u,o;return c;function c(b){return f(b)}function f(b){return t.enter("htmlFlow"),t.enter("htmlFlowData"),t.consume(b),s}function s(b){return b===33?(t.consume(b),p):b===47?(t.consume(b),r=!0,v):b===63?(t.consume(b),i=3,l.interrupt?e:x):Ot(b)?(t.consume(b),a=String.fromCharCode(b),T):n(b)}function p(b){return b===45?(t.consume(b),i=2,m):b===91?(t.consume(b),i=5,u=0,y):Ot(b)?(t.consume(b),i=4,l.interrupt?e:x):n(b)}function m(b){return b===45?(t.consume(b),l.interrupt?e:x):n(b)}function y(b){let oe="CDATA[";return b===oe.charCodeAt(u++)?(t.consume(b),u===oe.length?l.interrupt?e:Q:y):n(b)}function v(b){return Ot(b)?(t.consume(b),a=String.fromCharCode(b),T):n(b)}function T(b){if(b===null||b===47||b===62||Z(b)){let oe=b===47,tl=a.toLowerCase();return!oe&&!r&&Ns.includes(tl)?(i=1,l.interrupt?e(b):Q(b)):k1.includes(a.toLowerCase())?(i=6,oe?(t.consume(b),h):l.interrupt?e(b):Q(b)):(i=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(b):r?d(b):g(b))}return b===45||vt(b)?(t.consume(b),a+=String.fromCharCode(b),T):n(b)}function h(b){return b===62?(t.consume(b),l.interrupt?e:Q):n(b)}function d(b){return B(b)?(t.consume(b),d):S(b)}function g(b){return b===47?(t.consume(b),S):b===58||b===95||Ot(b)?(t.consume(b),E):B(b)?(t.consume(b),g):S(b)}function E(b){return b===45||b===46||b===58||b===95||vt(b)?(t.consume(b),E):C(b)}function C(b){return b===61?(t.consume(b),k):B(b)?(t.consume(b),C):g(b)}function k(b){return b===null||b===60||b===61||b===62||b===96?n(b):b===34||b===39?(t.consume(b),o=b,D):B(b)?(t.consume(b),k):O(b)}function D(b){return b===o?(t.consume(b),o=null,L):b===null||R(b)?n(b):(t.consume(b),D)}function O(b){return b===null||b===34||b===39||b===47||b===60||b===61||b===62||b===96||Z(b)?C(b):(t.consume(b),O)}function L(b){return b===47||b===62||B(b)?g(b):n(b)}function S(b){return b===62?(t.consume(b),P):n(b)}function P(b){return b===null||R(b)?Q(b):B(b)?(t.consume(b),P):n(b)}function Q(b){return b===45&&i===2?(t.consume(b),ht):b===60&&i===1?(t.consume(b),St):b===62&&i===4?(t.consume(b),$t):b===63&&i===3?(t.consume(b),x):b===93&&i===5?(t.consume(b),ae):R(b)&&(i===6||i===7)?(t.exit("htmlFlowData"),t.check(sE,ue,N)(b)):b===null||R(b)?(t.exit("htmlFlowData"),N(b)):(t.consume(b),Q)}function N(b){return t.check(mE,q,ue)(b)}function q(b){return t.enter("lineEnding"),t.consume(b),t.exit("lineEnding"),j}function j(b){return b===null||R(b)?N(b):(t.enter("htmlFlowData"),Q(b))}function ht(b){return b===45?(t.consume(b),x):Q(b)}function St(b){return b===47?(t.consume(b),a="",Pt):Q(b)}function Pt(b){if(b===62){let oe=a.toLowerCase();return Ns.includes(oe)?(t.consume(b),$t):Q(b)}return Ot(b)&&a.length<8?(t.consume(b),a+=String.fromCharCode(b),Pt):Q(b)}function ae(b){return b===93?(t.consume(b),x):Q(b)}function x(b){return b===62?(t.consume(b),$t):b===45&&i===2?(t.consume(b),x):Q(b)}function $t(b){return b===null||R(b)?(t.exit("htmlFlowData"),ue(b)):(t.consume(b),$t)}function ue(b){return t.exit("htmlFlow"),e(b)}}function dE(t,e,n){let l=this;return i;function i(a){return R(a)?(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),r):n(a)}function r(a){return l.parser.lazy[l.now().line]?n(a):e(a)}}function gE(t,e,n){return l;function l(i){return t.enter("lineEnding"),t.consume(i),t.exit("lineEnding"),t.attempt(Ke,e,n)}}var Us={name:"htmlText",tokenize:yE};function yE(t,e,n){let l=this,i,r,a;return u;function u(x){return t.enter("htmlText"),t.enter("htmlTextData"),t.consume(x),o}function o(x){return x===33?(t.consume(x),c):x===47?(t.consume(x),C):x===63?(t.consume(x),g):Ot(x)?(t.consume(x),O):n(x)}function c(x){return x===45?(t.consume(x),f):x===91?(t.consume(x),r=0,y):Ot(x)?(t.consume(x),d):n(x)}function f(x){return x===45?(t.consume(x),m):n(x)}function s(x){return x===null?n(x):x===45?(t.consume(x),p):R(x)?(a=s,St(x)):(t.consume(x),s)}function p(x){return x===45?(t.consume(x),m):s(x)}function m(x){return x===62?ht(x):x===45?p(x):s(x)}function y(x){let $t="CDATA[";return x===$t.charCodeAt(r++)?(t.consume(x),r===$t.length?v:y):n(x)}function v(x){return x===null?n(x):x===93?(t.consume(x),T):R(x)?(a=v,St(x)):(t.consume(x),v)}function T(x){return x===93?(t.consume(x),h):v(x)}function h(x){return x===62?ht(x):x===93?(t.consume(x),h):v(x)}function d(x){return x===null||x===62?ht(x):R(x)?(a=d,St(x)):(t.consume(x),d)}function g(x){return x===null?n(x):x===63?(t.consume(x),E):R(x)?(a=g,St(x)):(t.consume(x),g)}function E(x){return x===62?ht(x):g(x)}function C(x){return Ot(x)?(t.consume(x),k):n(x)}function k(x){return x===45||vt(x)?(t.consume(x),k):D(x)}function D(x){return R(x)?(a=D,St(x)):B(x)?(t.consume(x),D):ht(x)}function O(x){return x===45||vt(x)?(t.consume(x),O):x===47||x===62||Z(x)?L(x):n(x)}function L(x){return x===47?(t.consume(x),ht):x===58||x===95||Ot(x)?(t.consume(x),S):R(x)?(a=L,St(x)):B(x)?(t.consume(x),L):ht(x)}function S(x){return x===45||x===46||x===58||x===95||vt(x)?(t.consume(x),S):P(x)}function P(x){return x===61?(t.consume(x),Q):R(x)?(a=P,St(x)):B(x)?(t.consume(x),P):L(x)}function Q(x){return x===null||x===60||x===61||x===62||x===96?n(x):x===34||x===39?(t.consume(x),i=x,N):R(x)?(a=Q,St(x)):B(x)?(t.consume(x),Q):(t.consume(x),q)}function N(x){return x===i?(t.consume(x),i=void 0,j):x===null?n(x):R(x)?(a=N,St(x)):(t.consume(x),N)}function q(x){return x===null||x===34||x===39||x===60||x===61||x===96?n(x):x===47||x===62||Z(x)?L(x):(t.consume(x),q)}function j(x){return x===47||x===62||Z(x)?L(x):n(x)}function ht(x){return x===62?(t.consume(x),t.exit("htmlTextData"),t.exit("htmlText"),e):n(x)}function St(x){return t.exit("htmlTextData"),t.enter("lineEnding"),t.consume(x),t.exit("lineEnding"),Pt}function Pt(x){return B(x)?U(t,ae,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(x):ae(x)}function ae(x){return t.enter("htmlTextData"),a(x)}}var Dl={name:"labelEnd",resolveAll:SE,resolveTo:kE,tokenize:EE},xE={tokenize:TE},bE={tokenize:AE},vE={tokenize:wE};function SE(t){let e=-1,n=[];for(;++e<t.length;){let l=t[e][1];if(n.push(t[e]),l.type==="labelImage"||l.type==="labelLink"||l.type==="labelEnd"){let i=l.type==="labelImage"?4:2;l.type="data",e+=i}}return t.length!==n.length&&At(t,0,t.length,n),t}function kE(t,e){let n=t.length,l=0,i,r,a,u;for(;n--;)if(i=t[n][1],r){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;t[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(a){if(t[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(r=n,i.type!=="labelLink")){l=2;break}}else i.type==="labelEnd"&&(a=n);let o={type:t[r][1].type==="labelLink"?"link":"image",start:z({},t[r][1].start),end:z({},t[t.length-1][1].end)},c={type:"label",start:z({},t[r][1].start),end:z({},t[a][1].end)},f={type:"labelText",start:z({},t[r+l+2][1].end),end:z({},t[a-2][1].start)};return u=[["enter",o,e],["enter",c,e]],u=Wt(u,t.slice(r+1,r+l+3)),u=Wt(u,[["enter",f,e]]),u=Wt(u,Jn(e.parser.constructs.insideSpan.null,t.slice(r+l+4,a-3),e)),u=Wt(u,[["exit",f,e],t[a-2],t[a-1],["exit",c,e]]),u=Wt(u,t.slice(a+1)),u=Wt(u,[["exit",o,e]]),At(t,r,t.length,u),t}function EE(t,e,n){let l=this,i=l.events.length,r,a;for(;i--;)if((l.events[i][1].type==="labelImage"||l.events[i][1].type==="labelLink")&&!l.events[i][1]._balanced){r=l.events[i][1];break}return u;function u(p){return r?r._inactive?s(p):(a=l.parser.defined.includes(Qt(l.sliceSerialize({start:r.end,end:l.now()}))),t.enter("labelEnd"),t.enter("labelMarker"),t.consume(p),t.exit("labelMarker"),t.exit("labelEnd"),o):n(p)}function o(p){return p===40?t.attempt(xE,f,a?f:s)(p):p===91?t.attempt(bE,f,a?c:s)(p):a?f(p):s(p)}function c(p){return t.attempt(vE,f,s)(p)}function f(p){return e(p)}function s(p){return r._balanced=!0,n(p)}}function TE(t,e,n){return l;function l(s){return t.enter("resource"),t.enter("resourceMarker"),t.consume(s),t.exit("resourceMarker"),i}function i(s){return Z(s)?Cl(t,r)(s):r(s)}function r(s){return s===41?f(s):io(t,a,u,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(s)}function a(s){return Z(s)?Cl(t,o)(s):f(s)}function u(s){return n(s)}function o(s){return s===34||s===39||s===40?ao(t,c,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(s):f(s)}function c(s){return Z(s)?Cl(t,f)(s):f(s)}function f(s){return s===41?(t.enter("resourceMarker"),t.consume(s),t.exit("resourceMarker"),t.exit("resource"),e):n(s)}}function AE(t,e,n){let l=this;return i;function i(u){return ro.call(l,t,r,a,"reference","referenceMarker","referenceString")(u)}function r(u){return l.parser.defined.includes(Qt(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)))?e(u):n(u)}function a(u){return n(u)}}function wE(t,e,n){return l;function l(r){return t.enter("reference"),t.enter("referenceMarker"),t.consume(r),t.exit("referenceMarker"),i}function i(r){return r===93?(t.enter("referenceMarker"),t.consume(r),t.exit("referenceMarker"),t.exit("reference"),e):n(r)}}var Bs={name:"labelStartImage",resolveAll:Dl.resolveAll,tokenize:zE};function zE(t,e,n){let l=this;return i;function i(u){return t.enter("labelImage"),t.enter("labelImageMarker"),t.consume(u),t.exit("labelImageMarker"),r}function r(u){return u===91?(t.enter("labelMarker"),t.consume(u),t.exit("labelMarker"),t.exit("labelImage"),a):n(u)}function a(u){return u===94&&"_hiddenFootnoteSupport"in l.parser.constructs?n(u):e(u)}}var Hs={name:"labelStartLink",resolveAll:Dl.resolveAll,tokenize:CE};function CE(t,e,n){let l=this;return i;function i(a){return t.enter("labelLink"),t.enter("labelMarker"),t.consume(a),t.exit("labelMarker"),t.exit("labelLink"),r}function r(a){return a===94&&"_hiddenFootnoteSupport"in l.parser.constructs?n(a):e(a)}}var aa={name:"lineEnding",tokenize:DE};function DE(t,e){return n;function n(l){return t.enter("lineEnding"),t.consume(l),t.exit("lineEnding"),U(t,e,"linePrefix")}}var Ml={name:"thematicBreak",tokenize:ME};function ME(t,e,n){let l=0,i;return r;function r(c){return t.enter("thematicBreak"),a(c)}function a(c){return i=c,u(c)}function u(c){return c===i?(t.enter("thematicBreakSequence"),o(c)):l>=3&&(c===null||R(c))?(t.exit("thematicBreak"),e(c)):n(c)}function o(c){return c===i?(t.consume(c),l++,o):(t.exit("thematicBreakSequence"),B(c)?U(t,u,"whitespace")(c):u(c))}}var Zt={continuation:{tokenize:NE},exit:UE,name:"list",tokenize:RE},OE={partial:!0,tokenize:BE},_E={partial:!0,tokenize:LE};function RE(t,e,n){let l=this,i=l.events[l.events.length-1],r=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,a=0;return u;function u(m){let y=l.containerState.type||(m===42||m===43||m===45?"listUnordered":"listOrdered");if(y==="listUnordered"?!l.containerState.marker||m===l.containerState.marker:na(m)){if(l.containerState.type||(l.containerState.type=y,t.enter(y,{_container:!0})),y==="listUnordered")return t.enter("listItemPrefix"),m===42||m===45?t.check(Ml,n,c)(m):c(m);if(!l.interrupt||m===49)return t.enter("listItemPrefix"),t.enter("listItemValue"),o(m)}return n(m)}function o(m){return na(m)&&++a<10?(t.consume(m),o):(!l.interrupt||a<2)&&(l.containerState.marker?m===l.containerState.marker:m===41||m===46)?(t.exit("listItemValue"),c(m)):n(m)}function c(m){return t.enter("listItemMarker"),t.consume(m),t.exit("listItemMarker"),l.containerState.marker=l.containerState.marker||m,t.check(Ke,l.interrupt?n:f,t.attempt(OE,p,s))}function f(m){return l.containerState.initialBlankLine=!0,r++,p(m)}function s(m){return B(m)?(t.enter("listItemPrefixWhitespace"),t.consume(m),t.exit("listItemPrefixWhitespace"),p):n(m)}function p(m){return l.containerState.size=r+l.sliceSerialize(t.exit("listItemPrefix"),!0).length,e(m)}}function NE(t,e,n){let l=this;return l.containerState._closeFlow=void 0,t.check(Ke,i,r);function i(u){return l.containerState.furtherBlankLines=l.containerState.furtherBlankLines||l.containerState.initialBlankLine,U(t,e,"listItemIndent",l.containerState.size+1)(u)}function r(u){return l.containerState.furtherBlankLines||!B(u)?(l.containerState.furtherBlankLines=void 0,l.containerState.initialBlankLine=void 0,a(u)):(l.containerState.furtherBlankLines=void 0,l.containerState.initialBlankLine=void 0,t.attempt(_E,e,a)(u))}function a(u){return l.containerState._closeFlow=!0,l.interrupt=void 0,U(t,t.attempt(Zt,e,n),"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(u)}}function LE(t,e,n){let l=this;return U(t,i,"listItemIndent",l.containerState.size+1);function i(r){let a=l.events[l.events.length-1];return a&&a[1].type==="listItemIndent"&&a[2].sliceSerialize(a[1],!0).length===l.containerState.size?e(r):n(r)}}function UE(t){t.exit(this.containerState.type)}function BE(t,e,n){let l=this;return U(t,i,"listItemPrefixWhitespace",l.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(r){let a=l.events[l.events.length-1];return!B(r)&&a&&a[1].type==="listItemPrefixWhitespace"?e(r):n(r)}}var uo={name:"setextUnderline",resolveTo:HE,tokenize:qE};function HE(t,e){let n=t.length,l,i,r;for(;n--;)if(t[n][0]==="enter"){if(t[n][1].type==="content"){l=n;break}t[n][1].type==="paragraph"&&(i=n)}else t[n][1].type==="content"&&t.splice(n,1),!r&&t[n][1].type==="definition"&&(r=n);let a={type:"setextHeading",start:z({},t[l][1].start),end:z({},t[t.length-1][1].end)};return t[i][1].type="setextHeadingText",r?(t.splice(i,0,["enter",a,e]),t.splice(r+1,0,["exit",t[l][1],e]),t[l][1].end=z({},t[r][1].end)):t[l][1]=a,t.push(["exit",a,e]),t}function qE(t,e,n){let l=this,i;return r;function r(c){let f=l.events.length,s;for(;f--;)if(l.events[f][1].type!=="lineEnding"&&l.events[f][1].type!=="linePrefix"&&l.events[f][1].type!=="content"){s=l.events[f][1].type==="paragraph";break}return!l.parser.lazy[l.now().line]&&(l.interrupt||s)?(t.enter("setextHeadingLine"),i=c,a(c)):n(c)}function a(c){return t.enter("setextHeadingLineSequence"),u(c)}function u(c){return c===i?(t.consume(c),u):(t.exit("setextHeadingLineSequence"),B(c)?U(t,o,"lineSuffix")(c):o(c))}function o(c){return c===null||R(c)?(t.exit("setextHeadingLine"),e(c)):n(c)}}var E1={tokenize:jE};function jE(t){let e=this,n=t.attempt(Ke,l,t.attempt(this.parser.constructs.flowInitial,i,U(t,t.attempt(this.parser.constructs.flow,i,t.attempt(Ms,i)),"linePrefix")));return n;function l(r){if(r===null){t.consume(r);return}return t.enter("lineEndingBlank"),t.consume(r),t.exit("lineEndingBlank"),e.currentConstruct=void 0,n}function i(r){if(r===null){t.consume(r);return}return t.enter("lineEnding"),t.consume(r),t.exit("lineEnding"),e.currentConstruct=void 0,n}}var T1={resolveAll:C1()},A1=z1("string"),w1=z1("text");function z1(t){return{resolveAll:C1(t==="text"?YE:void 0),tokenize:e};function e(n){let l=this,i=this.parser.constructs[t],r=n.attempt(i,a,u);return a;function a(f){return c(f)?r(f):u(f)}function u(f){if(f===null){n.consume(f);return}return n.enter("data"),n.consume(f),o}function o(f){return c(f)?(n.exit("data"),r(f)):(n.consume(f),o)}function c(f){if(f===null)return!0;let s=i[f],p=-1;if(s)for(;++p<s.length;){let m=s[p];if(!m.previous||m.previous.call(l,l.previous))return!0}return!1}}}function C1(t){return e;function e(n,l){let i=-1,r;for(;++i<=n.length;)r===void 0?n[i]&&n[i][1].type==="data"&&(r=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==r+2&&(n[r][1].end=n[i-1][1].end,n.splice(r+2,i-r-2),i=r+2),r=void 0);return t?t(n,l):n}}function YE(t,e){let n=0;for(;++n<=t.length;)if((n===t.length||t[n][1].type==="lineEnding")&&t[n-1][1].type==="data"){let l=t[n-1][1],i=e.sliceStream(l),r=i.length,a=-1,u=0,o;for(;r--;){let c=i[r];if(typeof c=="string"){for(a=c.length;c.charCodeAt(a-1)===32;)u++,a--;if(a)break;a=-1}else if(c===-2)o=!0,u++;else if(c!==-1){r++;break}}if(e._contentTypeTextTrailing&&n===t.length&&(u=0),u){let c={type:n===t.length||o||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:r?a:l.start._bufferIndex+a,_index:l.start._index+r,line:l.end.line,column:l.end.column-u,offset:l.end.offset-u},end:z({},l.end)};l.end=z({},c.start),l.start.offset===l.end.offset?Object.assign(l,c):(t.splice(n,0,["enter",c,e],["exit",c,e]),n+=2)}n++}return t}var qs={};Pm(qs,{attentionMarkers:()=>IE,contentInitial:()=>XE,disable:()=>JE,document:()=>VE,flow:()=>QE,flowInitial:()=>GE,insideSpan:()=>FE,string:()=>ZE,text:()=>KE});var VE={42:Zt,43:Zt,45:Zt,48:Zt,49:Zt,50:Zt,51:Zt,52:Zt,53:Zt,54:Zt,55:Zt,56:Zt,57:Zt,62:Pu},XE={91:Os},GE={[-2]:ia,[-1]:ia,32:ia},QE={35:Rs,42:Ml,45:[uo,Ml],60:Ls,61:uo,95:Ml,96:eo,126:eo},ZE={38:to,92:$u},KE={[-5]:aa,[-4]:aa,[-3]:aa,33:Bs,38:to,42:la,60:[Cs,Us],91:Hs,92:[_s,$u],93:Dl,95:la,96:Ds},FE={null:[la,T1]},IE={null:[42,95]},JE={null:[]};function D1(t,e,n){let l={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},r=[],a=[],u=[],o=!0,c={attempt:L(D),check:L(O),consume:E,enter:C,exit:k,interrupt:L(O,{interrupt:!0})},f={code:null,containerState:{},defineSkip:h,events:[],now:T,parser:t,previous:null,sliceSerialize:y,sliceStream:v,write:m},s=e.tokenize.call(f,c),p;return e.resolveAll&&r.push(e),f;function m(N){return a=Wt(a,N),d(),a[a.length-1]!==null?[]:(S(e,0),f.events=Jn(r,f.events,f),f.events)}function y(N,q){return PE(v(N),q)}function v(N){return WE(a,N)}function T(){let{_bufferIndex:N,_index:q,line:j,column:ht,offset:St}=l;return{_bufferIndex:N,_index:q,line:j,column:ht,offset:St}}function h(N){i[N.line]=N.column,Q()}function d(){let N;for(;l._index<a.length;){let q=a[l._index];if(typeof q=="string")for(N=l._index,l._bufferIndex<0&&(l._bufferIndex=0);l._index===N&&l._bufferIndex<q.length;)g(q.charCodeAt(l._bufferIndex));else g(q)}}function g(N){o=void 0,p=N,s=s(N)}function E(N){R(N)?(l.line++,l.column=1,l.offset+=N===-3?2:1,Q()):N!==-1&&(l.column++,l.offset++),l._bufferIndex<0?l._index++:(l._bufferIndex++,l._bufferIndex===a[l._index].length&&(l._bufferIndex=-1,l._index++)),f.previous=N,o=!0}function C(N,q){let j=q||{};return j.type=N,j.start=T(),f.events.push(["enter",j,f]),u.push(j),j}function k(N){let q=u.pop();return q.end=T(),f.events.push(["exit",q,f]),q}function D(N,q){S(N,q.from)}function O(N,q){q.restore()}function L(N,q){return j;function j(ht,St,Pt){let ae,x,$t,ue;return Array.isArray(ht)?oe(ht):"tokenize"in ht?oe([ht]):b(ht);function b(Ht){return ji;function ji(Ie){let el=Ie!==null&&Ht[Ie],Ll=Ie!==null&&Ht.null,zo=[...Array.isArray(el)?el:el?[el]:[],...Array.isArray(Ll)?Ll:Ll?[Ll]:[]];return oe(zo)(Ie)}}function oe(Ht){return ae=Ht,x=0,Ht.length===0?Pt:tl(Ht[x])}function tl(Ht){return ji;function ji(Ie){return ue=P(),$t=Ht,Ht.partial||(f.currentConstruct=Ht),Ht.name&&f.parser.constructs.disable.null.includes(Ht.name)?ma(Ie):Ht.tokenize.call(q?Object.assign(Object.create(f),q):f,c,wo,ma)(Ie)}}function wo(Ht){return o=!0,N($t,ue),St}function ma(Ht){return o=!0,ue.restore(),++x<ae.length?tl(ae[x]):Pt}}}function S(N,q){N.resolveAll&&!r.includes(N)&&r.push(N),N.resolve&&At(f.events,q,f.events.length-q,N.resolve(f.events.slice(q),f)),N.resolveTo&&(f.events=N.resolveTo(f.events,f))}function P(){let N=T(),q=f.previous,j=f.currentConstruct,ht=f.events.length,St=Array.from(u);return{from:ht,restore:Pt};function Pt(){l=N,f.previous=q,f.currentConstruct=j,f.events.length=ht,u=St,Q()}}function Q(){l.line in i&&l.column<2&&(l.column=i[l.line],l.offset+=i[l.line]-1)}}function WE(t,e){let n=e.start._index,l=e.start._bufferIndex,i=e.end._index,r=e.end._bufferIndex,a;if(n===i)a=[t[n].slice(l,r)];else{if(a=t.slice(n,i),l>-1){let u=a[0];typeof u=="string"?a[0]=u.slice(l):a.shift()}r>0&&a.push(t[i].slice(0,r))}return a}function PE(t,e){let n=-1,l=[],i;for(;++n<t.length;){let r=t[n],a;if(typeof r=="string")a=r;else switch(r){case-5:{a="\r";break}case-4:{a=`
`;break}case-3:{a=`\r
`;break}case-2:{a=e?" ":"	";break}case-1:{if(!e&&i)continue;a=" ";break}default:a=String.fromCharCode(r)}i=r===-2,l.push(a)}return l.join("")}function js(t){let l={constructs:Ju([qs,...(t||{}).extensions||[]]),content:i(y1),defined:[],document:i(b1),flow:i(E1),lazy:{},string:i(A1),text:i(w1)};return l;function i(r){return a;function a(u){return D1(l,r,u)}}}function Ys(t){for(;!lo(t););return t}var M1=/[\0\t\n\r]/g;function Vs(){let t=1,e="",n=!0,l;return i;function i(r,a,u){let o=[],c,f,s,p,m;for(r=e+(typeof r=="string"?r.toString():new TextDecoder(a||void 0).decode(r)),s=0,e="",n&&(r.charCodeAt(0)===65279&&s++,n=void 0);s<r.length;){if(M1.lastIndex=s,c=M1.exec(r),p=c&&c.index!==void 0?c.index:r.length,m=r.charCodeAt(p),!c){e=r.slice(s);break}if(m===10&&s===p&&l)o.push(-3),l=void 0;else switch(l&&(o.push(-5),l=void 0),s<p&&(o.push(r.slice(s,p)),t+=p-s),m){case 0:{o.push(65533),t++;break}case 9:{for(f=Math.ceil(t/4)*4,o.push(-2);t++<f;)o.push(-1);break}case 10:{o.push(-4),t=1;break}default:l=!0,t=1}s=p+1}return u&&(l&&o.push(-5),e&&o.push(e),o.push(null)),o}}var $E=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function O1(t){return t.replace($E,t2)}function t2(t,e,n){if(e)return e;if(n.charCodeAt(0)===35){let i=n.charCodeAt(1),r=i===120||i===88;return Wu(n.slice(r?2:1),r?16:10)}return Oi(n)||t}var R1={}.hasOwnProperty;function Xs(t,e,n){return typeof e!="string"&&(n=e,e=void 0),e2(n)(Ys(js(n).document().write(Vs()(t,e,!0))))}function e2(t){let e={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(Zm),autolinkProtocol:L,autolinkEmail:L,atxHeading:r(Xm),blockQuote:r(Ie),characterEscape:L,characterReference:L,codeFenced:r(el),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:r(el,a),codeText:r(Ll,a),codeTextData:L,data:L,codeFlowValue:L,definition:r(zo),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:r(wx),hardBreakEscape:r(Gm),hardBreakTrailing:r(Gm),htmlFlow:r(Qm,a),htmlFlowData:L,htmlText:r(Qm,a),htmlTextData:L,image:r(zx),label:a,link:r(Zm),listItem:r(Cx),listItemValue:p,listOrdered:r(Km,s),listUnordered:r(Km),paragraph:r(Dx),reference:b,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:r(Xm),strong:r(Mx),thematicBreak:r(_x)},exit:{atxHeading:o(),atxHeadingSequence:C,autolink:o(),autolinkEmail:ji,autolinkProtocol:Ht,blockQuote:o(),characterEscapeValue:S,characterReferenceMarkerHexadecimal:tl,characterReferenceMarkerNumeric:tl,characterReferenceValue:wo,characterReference:ma,codeFenced:o(T),codeFencedFence:v,codeFencedFenceInfo:m,codeFencedFenceMeta:y,codeFlowValue:S,codeIndented:o(h),codeText:o(j),codeTextData:S,data:S,definition:o(),definitionDestinationString:E,definitionLabelString:d,definitionTitleString:g,emphasis:o(),hardBreakEscape:o(Q),hardBreakTrailing:o(Q),htmlFlow:o(N),htmlFlowData:S,htmlText:o(q),htmlTextData:S,image:o(St),label:ae,labelText:Pt,lineEnding:P,link:o(ht),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:oe,resourceDestinationString:x,resourceTitleString:$t,resource:ue,setextHeading:o(O),setextHeadingLineSequence:D,setextHeadingText:k,strong:o(),thematicBreak:o()}};N1(e,(t||{}).mdastExtensions||[]);let n={};return l;function l(A){let _={type:"root",children:[]},V={stack:[_],tokenStack:[],config:e,enter:u,exit:c,buffer:a,resume:f,data:n},J=[],at=-1;for(;++at<A.length;)if(A[at][1].type==="listOrdered"||A[at][1].type==="listUnordered")if(A[at][0]==="enter")J.push(at);else{let _e=J.pop();at=i(A,_e,at)}for(at=-1;++at<A.length;){let _e=e[A[at][0]];R1.call(_e,A[at][1].type)&&_e[A[at][1].type].call(Object.assign({sliceSerialize:A[at][2].sliceSerialize},V),A[at][1])}if(V.tokenStack.length>0){let _e=V.tokenStack[V.tokenStack.length-1];(_e[1]||_1).call(V,void 0,_e[0])}for(_.position={start:Wn(A.length>0?A[0][1].start:{line:1,column:1,offset:0}),end:Wn(A.length>0?A[A.length-2][1].end:{line:1,column:1,offset:0})},at=-1;++at<e.transforms.length;)_=e.transforms[at](_)||_;return _}function i(A,_,V){let J=_-1,at=-1,_e=!1,nl,Je,Yi,Vi;for(;++J<=V;){let ce=A[J];switch(ce[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{ce[0]==="enter"?at++:at--,Vi=void 0;break}case"lineEndingBlank":{ce[0]==="enter"&&(nl&&!Vi&&!at&&!Yi&&(Yi=J),Vi=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:Vi=void 0}if(!at&&ce[0]==="enter"&&ce[1].type==="listItemPrefix"||at===-1&&ce[0]==="exit"&&(ce[1].type==="listUnordered"||ce[1].type==="listOrdered")){if(nl){let Ul=J;for(Je=void 0;Ul--;){let We=A[Ul];if(We[1].type==="lineEnding"||We[1].type==="lineEndingBlank"){if(We[0]==="exit")continue;Je&&(A[Je][1].type="lineEndingBlank",_e=!0),We[1].type="lineEnding",Je=Ul}else if(!(We[1].type==="linePrefix"||We[1].type==="blockQuotePrefix"||We[1].type==="blockQuotePrefixWhitespace"||We[1].type==="blockQuoteMarker"||We[1].type==="listItemIndent"))break}Yi&&(!Je||Yi<Je)&&(nl._spread=!0),nl.end=Object.assign({},Je?A[Je][1].start:ce[1].end),A.splice(Je||J,0,["exit",nl,ce[2]]),J++,V++}if(ce[1].type==="listItemPrefix"){let Ul={type:"listItem",_spread:!1,start:Object.assign({},ce[1].start),end:void 0};nl=Ul,A.splice(J,0,["enter",Ul,ce[2]]),J++,V++,Yi=void 0,Vi=!0}}}return A[_][1]._spread=_e,V}function r(A,_){return V;function V(J){u.call(this,A(J),J),_&&_.call(this,J)}}function a(){this.stack.push({type:"fragment",children:[]})}function u(A,_,V){this.stack[this.stack.length-1].children.push(A),this.stack.push(A),this.tokenStack.push([_,V||void 0]),A.position={start:Wn(_.start),end:void 0}}function o(A){return _;function _(V){A&&A.call(this,V),c.call(this,V)}}function c(A,_){let V=this.stack.pop(),J=this.tokenStack.pop();if(J)J[0].type!==A.type&&(_?_.call(this,A,J[0]):(J[1]||_1).call(this,A,J[0]));else throw new Error("Cannot close `"+A.type+"` ("+Fn({start:A.start,end:A.end})+"): it\u2019s not open");V.position.end=Wn(A.end)}function f(){return Al(this.stack.pop())}function s(){this.data.expectingFirstListItemValue=!0}function p(A){if(this.data.expectingFirstListItemValue){let _=this.stack[this.stack.length-2];_.start=Number.parseInt(this.sliceSerialize(A),10),this.data.expectingFirstListItemValue=void 0}}function m(){let A=this.resume(),_=this.stack[this.stack.length-1];_.lang=A}function y(){let A=this.resume(),_=this.stack[this.stack.length-1];_.meta=A}function v(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function T(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function h(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A.replace(/(\r?\n|\r)$/g,"")}function d(A){let _=this.resume(),V=this.stack[this.stack.length-1];V.label=_,V.identifier=Qt(this.sliceSerialize(A)).toLowerCase()}function g(){let A=this.resume(),_=this.stack[this.stack.length-1];_.title=A}function E(){let A=this.resume(),_=this.stack[this.stack.length-1];_.url=A}function C(A){let _=this.stack[this.stack.length-1];if(!_.depth){let V=this.sliceSerialize(A).length;_.depth=V}}function k(){this.data.setextHeadingSlurpLineEnding=!0}function D(A){let _=this.stack[this.stack.length-1];_.depth=this.sliceSerialize(A).codePointAt(0)===61?1:2}function O(){this.data.setextHeadingSlurpLineEnding=void 0}function L(A){let V=this.stack[this.stack.length-1].children,J=V[V.length-1];(!J||J.type!=="text")&&(J=Ox(),J.position={start:Wn(A.start),end:void 0},V.push(J)),this.stack.push(J)}function S(A){let _=this.stack.pop();_.value+=this.sliceSerialize(A),_.position.end=Wn(A.end)}function P(A){let _=this.stack[this.stack.length-1];if(this.data.atHardBreak){let V=_.children[_.children.length-1];V.position.end=Wn(A.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&e.canContainEols.includes(_.type)&&(L.call(this,A),S.call(this,A))}function Q(){this.data.atHardBreak=!0}function N(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A}function q(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A}function j(){let A=this.resume(),_=this.stack[this.stack.length-1];_.value=A}function ht(){let A=this.stack[this.stack.length-1];if(this.data.inReference){let _=this.data.referenceType||"shortcut";A.type+="Reference",A.referenceType=_,delete A.url,delete A.title}else delete A.identifier,delete A.label;this.data.referenceType=void 0}function St(){let A=this.stack[this.stack.length-1];if(this.data.inReference){let _=this.data.referenceType||"shortcut";A.type+="Reference",A.referenceType=_,delete A.url,delete A.title}else delete A.identifier,delete A.label;this.data.referenceType=void 0}function Pt(A){let _=this.sliceSerialize(A),V=this.stack[this.stack.length-2];V.label=O1(_),V.identifier=Qt(_).toLowerCase()}function ae(){let A=this.stack[this.stack.length-1],_=this.resume(),V=this.stack[this.stack.length-1];if(this.data.inReference=!0,V.type==="link"){let J=A.children;V.children=J}else V.alt=_}function x(){let A=this.resume(),_=this.stack[this.stack.length-1];_.url=A}function $t(){let A=this.resume(),_=this.stack[this.stack.length-1];_.title=A}function ue(){this.data.inReference=void 0}function b(){this.data.referenceType="collapsed"}function oe(A){let _=this.resume(),V=this.stack[this.stack.length-1];V.label=_,V.identifier=Qt(this.sliceSerialize(A)).toLowerCase(),this.data.referenceType="full"}function tl(A){this.data.characterReferenceType=A.type}function wo(A){let _=this.sliceSerialize(A),V=this.data.characterReferenceType,J;V?(J=Wu(_,V==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):J=Oi(_);let at=this.stack[this.stack.length-1];at.value+=J}function ma(A){let _=this.stack.pop();_.position.end=Wn(A.end)}function Ht(A){S.call(this,A);let _=this.stack[this.stack.length-1];_.url=this.sliceSerialize(A)}function ji(A){S.call(this,A);let _=this.stack[this.stack.length-1];_.url="mailto:"+this.sliceSerialize(A)}function Ie(){return{type:"blockquote",children:[]}}function el(){return{type:"code",lang:null,meta:null,value:""}}function Ll(){return{type:"inlineCode",value:""}}function zo(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function wx(){return{type:"emphasis",children:[]}}function Xm(){return{type:"heading",depth:0,children:[]}}function Gm(){return{type:"break"}}function Qm(){return{type:"html",value:""}}function zx(){return{type:"image",title:null,url:"",alt:null}}function Zm(){return{type:"link",title:null,url:"",children:[]}}function Km(A){return{type:"list",ordered:A.type==="listOrdered",start:null,spread:A._spread,children:[]}}function Cx(A){return{type:"listItem",spread:A._spread,checked:null,children:[]}}function Dx(){return{type:"paragraph",children:[]}}function Mx(){return{type:"strong",children:[]}}function Ox(){return{type:"text",value:""}}function _x(){return{type:"thematicBreak"}}}function Wn(t){return{line:t.line,column:t.column,offset:t.offset}}function N1(t,e){let n=-1;for(;++n<e.length;){let l=e[n];Array.isArray(l)?N1(t,l):n2(t,l)}}function n2(t,e){let n;for(n in e)if(R1.call(e,n))switch(n){case"canContainEols":{let l=e[n];l&&t[n].push(...l);break}case"transforms":{let l=e[n];l&&t[n].push(...l);break}case"enter":case"exit":{let l=e[n];l&&Object.assign(t[n],l);break}}}function _1(t,e){throw t?new Error("Cannot close `"+t.type+"` ("+Fn({start:t.start,end:t.end})+"): a different token (`"+e.type+"`, "+Fn({start:e.start,end:e.end})+") is open"):new Error("Cannot close document, a token (`"+e.type+"`, "+Fn({start:e.start,end:e.end})+") is still open")}function oo(t){let e=this;e.parser=n;function n(l){return Xs(l,gt(z(z({},e.data("settings")),t),{extensions:e.data("micromarkExtensions")||[],mdastExtensions:e.data("fromMarkdownExtensions")||[]}))}}function L1(t,e){let n={type:"element",tagName:"blockquote",properties:{},children:t.wrap(t.all(e),!0)};return t.patch(e,n),t.applyData(e,n)}function U1(t,e){let n={type:"element",tagName:"br",properties:{},children:[]};return t.patch(e,n),[t.applyData(e,n),{type:"text",value:`
`}]}function B1(t,e){let n=e.value?e.value+`
`:"",l={};e.lang&&(l.className=["language-"+e.lang]);let i={type:"element",tagName:"code",properties:l,children:[{type:"text",value:n}]};return e.meta&&(i.data={meta:e.meta}),t.patch(e,i),i=t.applyData(e,i),i={type:"element",tagName:"pre",properties:{},children:[i]},t.patch(e,i),i}function H1(t,e){let n={type:"element",tagName:"del",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function q1(t,e){let n={type:"element",tagName:"em",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function j1(t,e){let n=typeof t.options.clobberPrefix=="string"?t.options.clobberPrefix:"user-content-",l=String(e.identifier).toUpperCase(),i=Oe(l.toLowerCase()),r=t.footnoteOrder.indexOf(l),a,u=t.footnoteCounts.get(l);u===void 0?(u=0,t.footnoteOrder.push(l),a=t.footnoteOrder.length):a=r+1,u+=1,t.footnoteCounts.set(l,u);let o={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+i,id:n+"fnref-"+i+(u>1?"-"+u:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(a)}]};t.patch(e,o);let c={type:"element",tagName:"sup",properties:{},children:[o]};return t.patch(e,c),t.applyData(e,c)}function Y1(t,e){let n={type:"element",tagName:"h"+e.depth,properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function V1(t,e){if(t.options.allowDangerousHtml){let n={type:"raw",value:e.value};return t.patch(e,n),t.applyData(e,n)}}function co(t,e){let n=e.referenceType,l="]";if(n==="collapsed"?l+="[]":n==="full"&&(l+="["+(e.label||e.identifier)+"]"),e.type==="imageReference")return[{type:"text",value:"!["+e.alt+l}];let i=t.all(e),r=i[0];r&&r.type==="text"?r.value="["+r.value:i.unshift({type:"text",value:"["});let a=i[i.length-1];return a&&a.type==="text"?a.value+=l:i.push({type:"text",value:l}),i}function X1(t,e){let n=String(e.identifier).toUpperCase(),l=t.definitionById.get(n);if(!l)return co(t,e);let i={src:Oe(l.url||""),alt:e.alt};l.title!==null&&l.title!==void 0&&(i.title=l.title);let r={type:"element",tagName:"img",properties:i,children:[]};return t.patch(e,r),t.applyData(e,r)}function G1(t,e){let n={src:Oe(e.url)};e.alt!==null&&e.alt!==void 0&&(n.alt=e.alt),e.title!==null&&e.title!==void 0&&(n.title=e.title);let l={type:"element",tagName:"img",properties:n,children:[]};return t.patch(e,l),t.applyData(e,l)}function Q1(t,e){let n={type:"text",value:e.value.replace(/\r?\n|\r/g," ")};t.patch(e,n);let l={type:"element",tagName:"code",properties:{},children:[n]};return t.patch(e,l),t.applyData(e,l)}function Z1(t,e){let n=String(e.identifier).toUpperCase(),l=t.definitionById.get(n);if(!l)return co(t,e);let i={href:Oe(l.url||"")};l.title!==null&&l.title!==void 0&&(i.title=l.title);let r={type:"element",tagName:"a",properties:i,children:t.all(e)};return t.patch(e,r),t.applyData(e,r)}function K1(t,e){let n={href:Oe(e.url)};e.title!==null&&e.title!==void 0&&(n.title=e.title);let l={type:"element",tagName:"a",properties:n,children:t.all(e)};return t.patch(e,l),t.applyData(e,l)}function F1(t,e,n){let l=t.all(e),i=n?l2(n):I1(e),r={},a=[];if(typeof e.checked=="boolean"){let f=l[0],s;f&&f.type==="element"&&f.tagName==="p"?s=f:(s={type:"element",tagName:"p",properties:{},children:[]},l.unshift(s)),s.children.length>0&&s.children.unshift({type:"text",value:" "}),s.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:e.checked,disabled:!0},children:[]}),r.className=["task-list-item"]}let u=-1;for(;++u<l.length;){let f=l[u];(i||u!==0||f.type!=="element"||f.tagName!=="p")&&a.push({type:"text",value:`
`}),f.type==="element"&&f.tagName==="p"&&!i?a.push(...f.children):a.push(f)}let o=l[l.length-1];o&&(i||o.type!=="element"||o.tagName!=="p")&&a.push({type:"text",value:`
`});let c={type:"element",tagName:"li",properties:r,children:a};return t.patch(e,c),t.applyData(e,c)}function l2(t){let e=!1;if(t.type==="list"){e=t.spread||!1;let n=t.children,l=-1;for(;!e&&++l<n.length;)e=I1(n[l])}return e}function I1(t){let e=t.spread;return e==null?t.children.length>1:e}function J1(t,e){let n={},l=t.all(e),i=-1;for(typeof e.start=="number"&&e.start!==1&&(n.start=e.start);++i<l.length;){let a=l[i];if(a.type==="element"&&a.tagName==="li"&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let r={type:"element",tagName:e.ordered?"ol":"ul",properties:n,children:t.wrap(l,!0)};return t.patch(e,r),t.applyData(e,r)}function W1(t,e){let n={type:"element",tagName:"p",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function P1(t,e){let n={type:"root",children:t.wrap(t.all(e))};return t.patch(e,n),t.applyData(e,n)}function $1(t,e){let n={type:"element",tagName:"strong",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function t0(t,e){let n=t.all(e),l=n.shift(),i=[];if(l){let a={type:"element",tagName:"thead",properties:{},children:t.wrap([l],!0)};t.patch(e.children[0],a),i.push(a)}if(n.length>0){let a={type:"element",tagName:"tbody",properties:{},children:t.wrap(n,!0)},u=Mi(e.children[1]),o=Ku(e.children[e.children.length-1]);u&&o&&(a.position={start:u,end:o}),i.push(a)}let r={type:"element",tagName:"table",properties:{},children:t.wrap(i,!0)};return t.patch(e,r),t.applyData(e,r)}function e0(t,e,n){let l=n?n.children:void 0,r=(l?l.indexOf(e):1)===0?"th":"td",a=n&&n.type==="table"?n.align:void 0,u=a?a.length:e.children.length,o=-1,c=[];for(;++o<u;){let s=e.children[o],p={},m=a?a[o]:void 0;m&&(p.align=m);let y={type:"element",tagName:r,properties:p,children:[]};s&&(y.children=t.all(s),t.patch(s,y),y=t.applyData(s,y)),c.push(y)}let f={type:"element",tagName:"tr",properties:{},children:t.wrap(c,!0)};return t.patch(e,f),t.applyData(e,f)}function n0(t,e){let n={type:"element",tagName:"td",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function i0(t){let e=String(t),n=/\r?\n|\r/g,l=n.exec(e),i=0,r=[];for(;l;)r.push(l0(e.slice(i,l.index),i>0,!0),l[0]),i=l.index+l[0].length,l=n.exec(e);return r.push(l0(e.slice(i),i>0,!1)),r.join("")}function l0(t,e,n){let l=0,i=t.length;if(e){let r=t.codePointAt(l);for(;r===9||r===32;)l++,r=t.codePointAt(l)}if(n){let r=t.codePointAt(i-1);for(;r===9||r===32;)i--,r=t.codePointAt(i-1)}return i>l?t.slice(l,i):""}function r0(t,e){let n={type:"text",value:i0(String(e.value))};return t.patch(e,n),t.applyData(e,n)}function a0(t,e){let n={type:"element",tagName:"hr",properties:{},children:[]};return t.patch(e,n),t.applyData(e,n)}var u0={blockquote:L1,break:U1,code:B1,delete:H1,emphasis:q1,footnoteReference:j1,heading:Y1,html:V1,imageReference:X1,image:G1,inlineCode:Q1,linkReference:Z1,link:K1,listItem:F1,list:J1,paragraph:W1,root:P1,strong:$1,table:t0,tableCell:n0,tableRow:e0,text:r0,thematicBreak:a0,toml:fo,yaml:fo,definition:fo,footnoteDefinition:fo};function fo(){}var o0=typeof self=="object"?self:globalThis,u2=(t,e)=>{let n=(i,r)=>(t.set(r,i),i),l=i=>{if(t.has(i))return t.get(i);let[r,a]=e[i];switch(r){case 0:case-1:return n(a,i);case 1:{let u=n([],i);for(let o of a)u.push(l(o));return u}case 2:{let u=n({},i);for(let[o,c]of a)u[l(o)]=l(c);return u}case 3:return n(new Date(a),i);case 4:{let{source:u,flags:o}=a;return n(new RegExp(u,o),i)}case 5:{let u=n(new Map,i);for(let[o,c]of a)u.set(l(o),l(c));return u}case 6:{let u=n(new Set,i);for(let o of a)u.add(l(o));return u}case 7:{let{name:u,message:o}=a;return n(new o0[u](o),i)}case 8:return n(BigInt(a),i);case"BigInt":return n(Object(BigInt(a)),i);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{let{buffer:u}=new Uint8Array(a);return n(new DataView(u),a)}}return n(new o0[r](a),i)};return l},Zs=t=>u2(new Map,t)(0);var _i="",{toString:o2}={},{keys:c2}=Object,ua=t=>{let e=typeof t;if(e!=="object"||!t)return[0,e];let n=o2.call(t).slice(8,-1);switch(n){case"Array":return[1,_i];case"Object":return[2,_i];case"Date":return[3,_i];case"RegExp":return[4,_i];case"Map":return[5,_i];case"Set":return[6,_i];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},mo=([t,e])=>t===0&&(e==="function"||e==="symbol"),f2=(t,e,n,l)=>{let i=(a,u)=>{let o=l.push(a)-1;return n.set(u,o),o},r=a=>{if(n.has(a))return n.get(a);let[u,o]=ua(a);switch(u){case 0:{let f=a;switch(o){case"bigint":u=8,f=a.toString();break;case"function":case"symbol":if(t)throw new TypeError("unable to serialize "+o);f=null;break;case"undefined":return i([-1],a)}return i([u,f],a)}case 1:{if(o){let p=a;return o==="DataView"?p=new Uint8Array(a.buffer):o==="ArrayBuffer"&&(p=new Uint8Array(a)),i([o,[...p]],a)}let f=[],s=i([u,f],a);for(let p of a)f.push(r(p));return s}case 2:{if(o)switch(o){case"BigInt":return i([o,a.toString()],a);case"Boolean":case"Number":case"String":return i([o,a.valueOf()],a)}if(e&&"toJSON"in a)return r(a.toJSON());let f=[],s=i([u,f],a);for(let p of c2(a))(t||!mo(ua(a[p])))&&f.push([r(p),r(a[p])]);return s}case 3:return i([u,a.toISOString()],a);case 4:{let{source:f,flags:s}=a;return i([u,{source:f,flags:s}],a)}case 5:{let f=[],s=i([u,f],a);for(let[p,m]of a)(t||!(mo(ua(p))||mo(ua(m))))&&f.push([r(p),r(m)]);return s}case 6:{let f=[],s=i([u,f],a);for(let p of a)(t||!mo(ua(p)))&&f.push(r(p));return s}}let{message:c}=a;return i([u,{name:o,message:c}],a)};return r},Ks=(t,{json:e,lossy:n}={})=>{let l=[];return f2(!(e||n),!!e,new Map,l)(t),l};var Ri=typeof structuredClone=="function"?(t,e)=>e&&("json"in e||"lossy"in e)?Zs(Ks(t,e)):structuredClone(t):(t,e)=>Zs(Ks(t,e));function s2(t,e){let n=[{type:"text",value:"\u21A9"}];return e>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(e)}]}),n}function m2(t,e){return"Back to reference "+(t+1)+(e>1?"-"+e:"")}function p0(t){let e=typeof t.options.clobberPrefix=="string"?t.options.clobberPrefix:"user-content-",n=t.options.footnoteBackContent||s2,l=t.options.footnoteBackLabel||m2,i=t.options.footnoteLabel||"Footnotes",r=t.options.footnoteLabelTagName||"h2",a=t.options.footnoteLabelProperties||{className:["sr-only"]},u=[],o=-1;for(;++o<t.footnoteOrder.length;){let c=t.footnoteById.get(t.footnoteOrder[o]);if(!c)continue;let f=t.all(c),s=String(c.identifier).toUpperCase(),p=Oe(s.toLowerCase()),m=0,y=[],v=t.footnoteCounts.get(s);for(;v!==void 0&&++m<=v;){y.length>0&&y.push({type:"text",value:" "});let d=typeof n=="string"?n:n(o,m);typeof d=="string"&&(d={type:"text",value:d}),y.push({type:"element",tagName:"a",properties:{href:"#"+e+"fnref-"+p+(m>1?"-"+m:""),dataFootnoteBackref:"",ariaLabel:typeof l=="string"?l:l(o,m),className:["data-footnote-backref"]},children:Array.isArray(d)?d:[d]})}let T=f[f.length-1];if(T&&T.type==="element"&&T.tagName==="p"){let d=T.children[T.children.length-1];d&&d.type==="text"?d.value+=" ":T.children.push({type:"text",value:" "}),T.children.push(...y)}else f.push(...y);let h={type:"element",tagName:"li",properties:{id:e+"fn-"+p},children:t.wrap(f,!0)};t.patch(c,h),u.push(h)}if(u.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:r,properties:gt(z({},Ri(a)),{id:"footnote-label"}),children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:t.wrap(u,!0)},{type:"text",value:`
`}]}}var Pn=function(t){if(t==null)return g2;if(typeof t=="function")return po(t);if(typeof t=="object")return Array.isArray(t)?p2(t):h2(t);if(typeof t=="string")return d2(t);throw new Error("Expected function, string, or object as test")};function p2(t){let e=[],n=-1;for(;++n<t.length;)e[n]=Pn(t[n]);return po(l);function l(...i){let r=-1;for(;++r<e.length;)if(e[r].apply(this,i))return!0;return!1}}function h2(t){let e=t;return po(n);function n(l){let i=l,r;for(r in t)if(i[r]!==e[r])return!1;return!0}}function d2(t){return po(e);function e(n){return n&&n.type===t}}function po(t){return e;function e(n,l,i){return!!(y2(n)&&t.call(this,n,typeof l=="number"?l:void 0,i||void 0))}}function g2(){return!0}function y2(t){return t!==null&&typeof t=="object"&&"type"in t}var h0=[],ho=!0,Ol=!1,go="skip";function oa(t,e,n,l){let i;typeof e=="function"&&typeof n!="function"?(l=n,n=e):i=e;let r=Pn(i),a=l?-1:1;u(t,void 0,[])();function u(o,c,f){let s=o&&typeof o=="object"?o:{};if(typeof s.type=="string"){let m=typeof s.tagName=="string"?s.tagName:typeof s.name=="string"?s.name:void 0;Object.defineProperty(p,"name",{value:"node ("+(o.type+(m?"<"+m+">":""))+")"})}return p;function p(){let m=h0,y,v,T;if((!e||r(o,c,f[f.length-1]||void 0))&&(m=x2(n(o,f)),m[0]===Ol))return m;if("children"in o&&o.children){let h=o;if(h.children&&m[0]!==go)for(v=(l?h.children.length:-1)+a,T=f.concat(h);v>-1&&v<h.children.length;){let d=h.children[v];if(y=u(d,v,T)(),y[0]===Ol)return y;v=typeof y[1]=="number"?y[1]:v+a}}return m}}}function x2(t){return Array.isArray(t)?t:typeof t=="number"?[ho,t]:t==null?h0:[t]}function _l(t,e,n,l){let i,r,a;typeof e=="function"&&typeof n!="function"?(r=void 0,a=e,i=n):(r=e,a=n,i=l),oa(t,r,u,i);function u(o,c){let f=c[c.length-1],s=f?f.children.indexOf(o):void 0;return a(o,s,f)}}var Fs={}.hasOwnProperty,b2={};function g0(t,e){let n=e||b2,l=new Map,i=new Map,r=new Map,a=z(z({},u0),n.handlers),u={all:c,applyData:S2,definitionById:l,footnoteById:i,footnoteCounts:r,footnoteOrder:[],handlers:a,one:o,options:n,patch:v2,wrap:E2};return _l(t,function(f){if(f.type==="definition"||f.type==="footnoteDefinition"){let s=f.type==="definition"?l:i,p=String(f.identifier).toUpperCase();s.has(p)||s.set(p,f)}}),u;function o(f,s){let p=f.type,m=u.handlers[p];if(Fs.call(u.handlers,p)&&m)return m(u,f,s);if(u.options.passThrough&&u.options.passThrough.includes(p)){if("children"in f){let v=f,{children:T}=v,h=Wm(v,["children"]),d=Ri(h);return d.children=u.all(f),d}return Ri(f)}return(u.options.unknownHandler||k2)(u,f,s)}function c(f){let s=[];if("children"in f){let p=f.children,m=-1;for(;++m<p.length;){let y=u.one(p[m],f);if(y){if(m&&p[m-1].type==="break"&&(!Array.isArray(y)&&y.type==="text"&&(y.value=d0(y.value)),!Array.isArray(y)&&y.type==="element")){let v=y.children[0];v&&v.type==="text"&&(v.value=d0(v.value))}Array.isArray(y)?s.push(...y):s.push(y)}}}return s}}function v2(t,e){t.position&&(e.position=ks(t))}function S2(t,e){let n=e;if(t&&t.data){let l=t.data.hName,i=t.data.hChildren,r=t.data.hProperties;if(typeof l=="string")if(n.type==="element")n.tagName=l;else{let a="children"in n?n.children:[n];n={type:"element",tagName:l,properties:{},children:a}}n.type==="element"&&r&&Object.assign(n.properties,Ri(r)),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function k2(t,e){let n=e.data||{},l="value"in e&&!(Fs.call(n,"hProperties")||Fs.call(n,"hChildren"))?{type:"text",value:e.value}:{type:"element",tagName:"div",properties:{},children:t.all(e)};return t.patch(e,l),t.applyData(e,l)}function E2(t,e){let n=[],l=-1;for(e&&n.push({type:"text",value:`
`});++l<t.length;)l&&n.push({type:"text",value:`
`}),n.push(t[l]);return e&&t.length>0&&n.push({type:"text",value:`
`}),n}function d0(t){let e=0,n=t.charCodeAt(e);for(;n===9||n===32;)e++,n=t.charCodeAt(e);return t.slice(e)}function yo(t,e){let n=g0(t,e),l=n.one(t,void 0),i=p0(n),r=Array.isArray(l)?{type:"root",children:l}:l||{type:"root",children:[]};return i&&("children"in r,r.children.push({type:"text",value:`
`},i)),r}function xo(t,e){return t&&"run"in t?function(n,l){return Xi(this,null,function*(){let i=yo(n,z({file:l},e));yield t.run(i,l)})}:function(n,l){return yo(n,z({file:l},t||e))}}function Is(t){if(t)throw t}var So=bn(A0(),1);function ca(t){if(typeof t!="object"||t===null)return!1;let e=Object.getPrototypeOf(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)}function Js(){let t=[],e={run:n,use:l};return e;function n(...i){let r=-1,a=i.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);u(null,...i);function u(o,...c){let f=t[++r],s=-1;if(o){a(o);return}for(;++s<i.length;)(c[s]===null||c[s]===void 0)&&(c[s]=i[s]);i=c,f?w0(f,u)(...c):a(null,...c)}}function l(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return t.push(i),e}}function w0(t,e){let n;return l;function l(...a){let u=t.length>a.length,o;u&&a.push(i);try{o=t.apply(this,a)}catch(c){let f=c;if(u&&n)throw f;return i(f)}u||(o&&o.then&&typeof o.then=="function"?o.then(r,i):o instanceof Error?i(o):r(o))}function i(a,...u){n||(n=!0,e(a,...u))}function r(a){i(null,a)}}var Ue={basename:T2,dirname:A2,extname:w2,join:z2,sep:"/"};function T2(t,e){if(e!==void 0&&typeof e!="string")throw new TypeError('"ext" argument must be a string');fa(t);let n=0,l=-1,i=t.length,r;if(e===void 0||e.length===0||e.length>t.length){for(;i--;)if(t.codePointAt(i)===47){if(r){n=i+1;break}}else l<0&&(r=!0,l=i+1);return l<0?"":t.slice(n,l)}if(e===t)return"";let a=-1,u=e.length-1;for(;i--;)if(t.codePointAt(i)===47){if(r){n=i+1;break}}else a<0&&(r=!0,a=i+1),u>-1&&(t.codePointAt(i)===e.codePointAt(u--)?u<0&&(l=i):(u=-1,l=a));return n===l?l=a:l<0&&(l=t.length),t.slice(n,l)}function A2(t){if(fa(t),t.length===0)return".";let e=-1,n=t.length,l;for(;--n;)if(t.codePointAt(n)===47){if(l){e=n;break}}else l||(l=!0);return e<0?t.codePointAt(0)===47?"/":".":e===1&&t.codePointAt(0)===47?"//":t.slice(0,e)}function w2(t){fa(t);let e=t.length,n=-1,l=0,i=-1,r=0,a;for(;e--;){let u=t.codePointAt(e);if(u===47){if(a){l=e+1;break}continue}n<0&&(a=!0,n=e+1),u===46?i<0?i=e:r!==1&&(r=1):i>-1&&(r=-1)}return i<0||n<0||r===0||r===1&&i===n-1&&i===l+1?"":t.slice(i,n)}function z2(...t){let e=-1,n;for(;++e<t.length;)fa(t[e]),t[e]&&(n=n===void 0?t[e]:n+"/"+t[e]);return n===void 0?".":C2(n)}function C2(t){fa(t);let e=t.codePointAt(0)===47,n=D2(t,!e);return n.length===0&&!e&&(n="."),n.length>0&&t.codePointAt(t.length-1)===47&&(n+="/"),e?"/"+n:n}function D2(t,e){let n="",l=0,i=-1,r=0,a=-1,u,o;for(;++a<=t.length;){if(a<t.length)u=t.codePointAt(a);else{if(u===47)break;u=47}if(u===47){if(!(i===a-1||r===1))if(i!==a-1&&r===2){if(n.length<2||l!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(o=n.lastIndexOf("/"),o!==n.length-1){o<0?(n="",l=0):(n=n.slice(0,o),l=n.length-1-n.lastIndexOf("/")),i=a,r=0;continue}}else if(n.length>0){n="",l=0,i=a,r=0;continue}}e&&(n=n.length>0?n+"/..":"..",l=2)}else n.length>0?n+="/"+t.slice(i+1,a):n=t.slice(i+1,a),l=a-i-1;i=a,r=0}else u===46&&r>-1?r++:r=-1}return n}function fa(t){if(typeof t!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}var z0={cwd:M2};function M2(){return"/"}function Ni(t){return!!(t!==null&&typeof t=="object"&&"href"in t&&t.href&&"protocol"in t&&t.protocol&&t.auth===void 0)}function C0(t){if(typeof t=="string")t=new URL(t);else if(!Ni(t)){let e=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+t+"`");throw e.code="ERR_INVALID_ARG_TYPE",e}if(t.protocol!=="file:"){let e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return O2(t)}function O2(t){if(t.hostname!==""){let l=new TypeError('File URL host must be "localhost" or empty on darwin');throw l.code="ERR_INVALID_FILE_URL_HOST",l}let e=t.pathname,n=-1;for(;++n<e.length;)if(e.codePointAt(n)===37&&e.codePointAt(n+1)===50){let l=e.codePointAt(n+2);if(l===70||l===102){let i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(e)}var Ws=["history","path","basename","stem","extname","dirname"],Rl=class{constructor(e){let n;e?Ni(e)?n={path:e}:typeof e=="string"||_2(e)?n={value:e}:n=e:n={},this.cwd="cwd"in n?"":z0.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let l=-1;for(;++l<Ws.length;){let r=Ws[l];r in n&&n[r]!==void 0&&n[r]!==null&&(this[r]=r==="history"?[...n[r]]:n[r])}let i;for(i in n)Ws.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?Ue.basename(this.path):void 0}set basename(e){$s(e,"basename"),Ps(e,"basename"),this.path=Ue.join(this.dirname||"",e)}get dirname(){return typeof this.path=="string"?Ue.dirname(this.path):void 0}set dirname(e){D0(this.basename,"dirname"),this.path=Ue.join(e||"",this.basename)}get extname(){return typeof this.path=="string"?Ue.extname(this.path):void 0}set extname(e){if(Ps(e,"extname"),D0(this.dirname,"extname"),e){if(e.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Ue.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){Ni(e)&&(e=C0(e)),$s(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return typeof this.path=="string"?Ue.basename(this.path,this.extname):void 0}set stem(e){$s(e,"stem"),Ps(e,"stem"),this.path=Ue.join(this.dirname||"",e+(this.extname||""))}fail(e,n,l){let i=this.message(e,n,l);throw i.fatal=!0,i}info(e,n,l){let i=this.message(e,n,l);return i.fatal=void 0,i}message(e,n,l){let i=new Tt(e,n,l);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(e){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(e||void 0).decode(this.value)}};function Ps(t,e){if(t&&t.includes(Ue.sep))throw new Error("`"+e+"` cannot be a path: did not expect `"+Ue.sep+"`")}function $s(t,e){if(!t)throw new Error("`"+e+"` cannot be empty")}function D0(t,e){if(!t)throw new Error("Setting `"+e+"` requires `path` to be set too")}function _2(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var M0=function(t){let l=this.constructor.prototype,i=l[t],r=function(){return i.apply(r,arguments)};return Object.setPrototypeOf(r,l),r};var R2={}.hasOwnProperty,lm=class t extends M0{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=Js()}copy(){let e=new t,n=-1;for(;++n<this.attachers.length;){let l=this.attachers[n];e.use(...l)}return e.data((0,So.default)(!0,{},this.namespace)),e}data(e,n){return typeof e=="string"?arguments.length===2?(nm("data",this.frozen),this.namespace[e]=n,this):R2.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nm("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;let e=this;for(;++this.freezeIndex<this.attachers.length;){let[n,...l]=this.attachers[this.freezeIndex];if(l[0]===!1)continue;l[0]===!0&&(l[0]=void 0);let i=n.call(e,...l);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let n=vo(e),l=this.parser||this.Parser;return tm("parse",l),l(String(n),n)}process(e,n){let l=this;return this.freeze(),tm("process",this.parser||this.Parser),em("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(r,a){let u=vo(e),o=l.parse(u);l.run(o,u,function(f,s,p){if(f||!s||!p)return c(f);let m=s,y=l.stringify(m,p);L2(y)?p.value=y:p.result=y,c(f,p)});function c(f,s){f||!s?a(f):r?r(s):n(void 0,s)}}}processSync(e){let n=!1,l;return this.freeze(),tm("processSync",this.parser||this.Parser),em("processSync",this.compiler||this.Compiler),this.process(e,i),_0("processSync","process",n),l;function i(r,a){n=!0,Is(r),l=a}}run(e,n,l){O0(e),this.freeze();let i=this.transformers;return!l&&typeof n=="function"&&(l=n,n=void 0),l?r(void 0,l):new Promise(r);function r(a,u){let o=vo(n);i.run(e,o,c);function c(f,s,p){let m=s||e;f?u(f):a?a(m):l(void 0,m,p)}}}runSync(e,n){let l=!1,i;return this.run(e,n,r),_0("runSync","run",l),i;function r(a,u){Is(a),i=u,l=!0}}stringify(e,n){this.freeze();let l=vo(n),i=this.compiler||this.Compiler;return em("stringify",i),O0(e),i(e,l)}use(e,...n){let l=this.attachers,i=this.namespace;if(nm("use",this.frozen),e!=null)if(typeof e=="function")o(e,n);else if(typeof e=="object")Array.isArray(e)?u(e):a(e);else throw new TypeError("Expected usable value, not `"+e+"`");return this;function r(c){if(typeof c=="function")o(c,[]);else if(typeof c=="object")if(Array.isArray(c)){let[f,...s]=c;o(f,s)}else a(c);else throw new TypeError("Expected usable value, not `"+c+"`")}function a(c){if(!("plugins"in c)&&!("settings"in c))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");u(c.plugins),c.settings&&(i.settings=(0,So.default)(!0,i.settings,c.settings))}function u(c){let f=-1;if(c!=null)if(Array.isArray(c))for(;++f<c.length;){let s=c[f];r(s)}else throw new TypeError("Expected a list of plugins, not `"+c+"`")}function o(c,f){let s=-1,p=-1;for(;++s<l.length;)if(l[s][0]===c){p=s;break}if(p===-1)l.push([c,...f]);else if(f.length>0){let[m,...y]=f,v=l[p][1];ca(v)&&ca(m)&&(m=(0,So.default)(!0,v,m)),l[p]=[c,m,...y]}}}},im=new lm().freeze();function tm(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `parser`")}function em(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `compiler`")}function nm(t,e){if(e)throw new Error("Cannot call `"+t+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function O0(t){if(!ca(t)||typeof t.type!="string")throw new TypeError("Expected node, got `"+t+"`")}function _0(t,e,n){if(!n)throw new Error("`"+t+"` finished async. Use `"+e+"` instead")}function vo(t){return N2(t)?t:new Rl(t)}function N2(t){return!!(t&&typeof t=="object"&&"message"in t&&"messages"in t)}function L2(t){return typeof t=="string"||U2(t)}function U2(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var B2="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",R0=[],N0={allowDangerousHtml:!0},H2=/^(https?|ircs?|mailto|xmpp)$/i,q2=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function rm(t){let e=j2(t),n=Y2(t);return V2(e.runSync(e.parse(n),n),t)}function j2(t){let e=t.rehypePlugins||R0,n=t.remarkPlugins||R0,l=t.remarkRehypeOptions?z(z({},t.remarkRehypeOptions),N0):N0;return im().use(oo).use(n).use(xo,l).use(e)}function Y2(t){let e=t.children||"",n=new Rl;return typeof e=="string"?n.value=e:(""+e,void 0),n}function V2(t,e){let n=e.allowedElements,l=e.allowElement,i=e.components,r=e.disallowedElements,a=e.skipHtml,u=e.unwrapDisallowed,o=e.urlTransform||U0;for(let f of q2)Object.hasOwn(e,f.from)&&(""+f.from+(f.to?"use `"+f.to+"` instead":"remove it")+B2+f.id,void 0);return n&&r&&void 0,_l(t,c),As(t,{Fragment:Li.Fragment,components:i,ignoreInvalidStyle:!0,jsx:Li.jsx,jsxs:Li.jsxs,passKeys:!0,passNode:!0});function c(f,s,p){if(f.type==="raw"&&p&&typeof s=="number")return a?p.children.splice(s,1):p.children[s]={type:"text",value:f.value},s;if(f.type==="element"){let m;for(m in ea)if(Object.hasOwn(ea,m)&&Object.hasOwn(f.properties,m)){let y=f.properties[m],v=ea[m];(v===null||v.includes(f.tagName))&&(f.properties[m]=o(String(y||""),m,f))}}if(f.type==="element"){let m=n?!n.includes(f.tagName):r?r.includes(f.tagName):!1;if(!m&&l&&typeof s=="number"&&(m=!l(f,s,p)),m&&p&&typeof s=="number")return u&&f.children?p.children.splice(s,1,...f.children):p.children.splice(s,1),s}}}function U0(t){let e=t.indexOf(":"),n=t.indexOf("?"),l=t.indexOf("#"),i=t.indexOf("/");return e===-1||i!==-1&&e>i||n!==-1&&e>n||l!==-1&&e>l||H2.test(t.slice(0,e))?t:""}function am(t,e){let n=String(t);if(typeof e!="string")throw new TypeError("Expected character");let l=0,i=n.indexOf(e);for(;i!==-1;)l++,i=n.indexOf(e,i+e.length);return l}function um(t){if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function om(t,e,n){let i=Pn((n||{}).ignore||[]),r=X2(e),a=-1;for(;++a<r.length;)oa(t,"text",u);function u(c,f){let s=-1,p;for(;++s<f.length;){let m=f[s],y=p?p.children:void 0;if(i(m,y?y.indexOf(m):void 0,p))return;p=m}if(p)return o(c,f)}function o(c,f){let s=f[f.length-1],p=r[a][0],m=r[a][1],y=0,T=s.children.indexOf(c),h=!1,d=[];p.lastIndex=0;let g=p.exec(c.value);for(;g;){let E=g.index,C={index:g.index,input:g.input,stack:[...f,c]},k=m(...g,C);if(typeof k=="string"&&(k=k.length>0?{type:"text",value:k}:void 0),k===!1?p.lastIndex=E+1:(y!==E&&d.push({type:"text",value:c.value.slice(y,E)}),Array.isArray(k)?d.push(...k):k&&d.push(k),y=E+g[0].length,h=!0),!p.global)break;g=p.exec(c.value)}return h?(y<c.value.length&&d.push({type:"text",value:c.value.slice(y)}),s.children.splice(T,1,...d)):d=[c],T+d.length}}function X2(t){let e=[];if(!Array.isArray(t))throw new TypeError("Expected find and replace tuple or list of tuples");let n=!t[0]||Array.isArray(t[0])?t:[t],l=-1;for(;++l<n.length;){let i=n[l];e.push([G2(i[0]),Q2(i[1])])}return e}function G2(t){return typeof t=="string"?new RegExp(um(t),"g"):t}function Q2(t){return typeof t=="function"?t:function(){return t}}var cm="phrasing",fm=["autolink","link","image","label"];function mm(){return{transforms:[W2],enter:{literalAutolink:Z2,literalAutolinkEmail:sm,literalAutolinkHttp:sm,literalAutolinkWww:sm},exit:{literalAutolink:J2,literalAutolinkEmail:I2,literalAutolinkHttp:K2,literalAutolinkWww:F2}}}function pm(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:cm,notInConstruct:fm},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:cm,notInConstruct:fm},{character:":",before:"[ps]",after:"\\/",inConstruct:cm,notInConstruct:fm}]}}function Z2(t){this.enter({type:"link",title:null,url:"",children:[]},t)}function sm(t){this.config.enter.autolinkProtocol.call(this,t)}function K2(t){this.config.exit.autolinkProtocol.call(this,t)}function F2(t){this.config.exit.data.call(this,t);let e=this.stack[this.stack.length-1];e.type,e.url="http://"+this.sliceSerialize(t)}function I2(t){this.config.exit.autolinkEmail.call(this,t)}function J2(t){this.exit(t)}function W2(t){om(t,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,P2],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),$2]],{ignore:["link","linkReference"]})}function P2(t,e,n,l,i){let r="";if(!B0(i)||(/^w/i.test(e)&&(n=e+n,e="",r="http://"),!tT(n)))return!1;let a=eT(n+l);if(!a[0])return!1;let u={type:"link",title:null,url:r+e+a[0],children:[{type:"text",value:e+a[0]}]};return a[1]?[u,{type:"text",value:a[1]}]:u}function $2(t,e,n,l){return!B0(l,!0)||/[-\d_]$/.test(n)?!1:{type:"link",title:null,url:"mailto:"+e+"@"+n,children:[{type:"text",value:e+"@"+n}]}}function tT(t){let e=t.split(".");return!(e.length<2||e[e.length-1]&&(/_/.test(e[e.length-1])||!/[a-zA-Z\d]/.test(e[e.length-1]))||e[e.length-2]&&(/_/.test(e[e.length-2])||!/[a-zA-Z\d]/.test(e[e.length-2])))}function eT(t){let e=/[!"&'),.:;<>?\]}]+$/.exec(t);if(!e)return[t,void 0];t=t.slice(0,e.index);let n=e[0],l=n.indexOf(")"),i=am(t,"("),r=am(t,")");for(;l!==-1&&i>r;)t+=n.slice(0,l+1),n=n.slice(l+1),l=n.indexOf(")"),r++;return[t,n]}function B0(t,e){let n=t.input.charCodeAt(t.index-1);return(t.index===0||Ze(n)||zl(n))&&(!e||n!==47)}H0.peek=fT;function nT(){this.buffer()}function lT(t){this.enter({type:"footnoteReference",identifier:"",label:""},t)}function iT(){this.buffer()}function rT(t){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},t)}function aT(t){let e=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=Qt(this.sliceSerialize(t)).toLowerCase(),n.label=e}function uT(t){this.exit(t)}function oT(t){let e=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=Qt(this.sliceSerialize(t)).toLowerCase(),n.label=e}function cT(t){this.exit(t)}function fT(){return"["}function H0(t,e,n,l){let i=n.createTracker(l),r=i.move("[^"),a=n.enter("footnoteReference"),u=n.enter("reference");return r+=i.move(n.safe(n.associationId(t),{after:"]",before:r})),u(),a(),r+=i.move("]"),r}function hm(){return{enter:{gfmFootnoteCallString:nT,gfmFootnoteCall:lT,gfmFootnoteDefinitionLabelString:iT,gfmFootnoteDefinition:rT},exit:{gfmFootnoteCallString:aT,gfmFootnoteCall:uT,gfmFootnoteDefinitionLabelString:oT,gfmFootnoteDefinition:cT}}}function dm(t){let e=!1;return t&&t.firstLineBlank&&(e=!0),{handlers:{footnoteDefinition:n,footnoteReference:H0},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function n(l,i,r,a){let u=r.createTracker(a),o=u.move("[^"),c=r.enter("footnoteDefinition"),f=r.enter("label");return o+=u.move(r.safe(r.associationId(l),{before:o,after:"]"})),f(),o+=u.move("]:"),l.children&&l.children.length>0&&(u.shift(4),o+=u.move((e?`
`:" ")+r.indentLines(r.containerFlow(l,u.current()),e?q0:sT))),c(),o}}function sT(t,e,n){return e===0?t:q0(t,e,n)}function q0(t,e,n){return(n?"":"    ")+t}var mT=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];j0.peek=dT;function gm(){return{canContainEols:["delete"],enter:{strikethrough:pT},exit:{strikethrough:hT}}}function ym(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:mT}],handlers:{delete:j0}}}function pT(t){this.enter({type:"delete",children:[]},t)}function hT(t){this.exit(t)}function j0(t,e,n,l){let i=n.createTracker(l),r=n.enter("strikethrough"),a=i.move("~~");return a+=n.containerPhrasing(t,gt(z({},i.current()),{before:a,after:"~"})),a+=i.move("~~"),r(),a}function dT(){return"~"}function gT(t){return t.length}function V0(t,e){let n=e||{},l=(n.align||[]).concat(),i=n.stringLength||gT,r=[],a=[],u=[],o=[],c=0,f=-1;for(;++f<t.length;){let v=[],T=[],h=-1;for(t[f].length>c&&(c=t[f].length);++h<t[f].length;){let d=yT(t[f][h]);if(n.alignDelimiters!==!1){let g=i(d);T[h]=g,(o[h]===void 0||g>o[h])&&(o[h]=g)}v.push(d)}a[f]=v,u[f]=T}let s=-1;if(typeof l=="object"&&"length"in l)for(;++s<c;)r[s]=Y0(l[s]);else{let v=Y0(l);for(;++s<c;)r[s]=v}s=-1;let p=[],m=[];for(;++s<c;){let v=r[s],T="",h="";v===99?(T=":",h=":"):v===108?T=":":v===114&&(h=":");let d=n.alignDelimiters===!1?1:Math.max(1,o[s]-T.length-h.length),g=T+"-".repeat(d)+h;n.alignDelimiters!==!1&&(d=T.length+d+h.length,d>o[s]&&(o[s]=d),m[s]=d),p[s]=g}a.splice(1,0,p),u.splice(1,0,m),f=-1;let y=[];for(;++f<a.length;){let v=a[f],T=u[f];s=-1;let h=[];for(;++s<c;){let d=v[s]||"",g="",E="";if(n.alignDelimiters!==!1){let C=o[s]-(T[s]||0),k=r[s];k===114?g=" ".repeat(C):k===99?C%2?(g=" ".repeat(C/2+.5),E=" ".repeat(C/2-.5)):(g=" ".repeat(C/2),E=g):E=" ".repeat(C)}n.delimiterStart!==!1&&!s&&h.push("|"),n.padding!==!1&&!(n.alignDelimiters===!1&&d==="")&&(n.delimiterStart!==!1||s)&&h.push(" "),n.alignDelimiters!==!1&&h.push(g),h.push(d),n.alignDelimiters!==!1&&h.push(E),n.padding!==!1&&h.push(" "),(n.delimiterEnd!==!1||s!==c-1)&&h.push("|")}y.push(n.delimiterEnd===!1?h.join("").replace(/ +$/,""):h.join(""))}return y.join(`
`)}function yT(t){return t==null?"":String(t)}function Y0(t){let e=typeof t=="string"?t.codePointAt(0):0;return e===67||e===99?99:e===76||e===108?108:e===82||e===114?114:0}function X0(t,e,n,l){let i=n.enter("blockquote"),r=n.createTracker(l);r.move("> "),r.shift(2);let a=n.indentLines(n.containerFlow(t,r.current()),xT);return i(),a}function xT(t,e,n){return">"+(n?"":" ")+t}function Q0(t,e){return G0(t,e.inConstruct,!0)&&!G0(t,e.notInConstruct,!1)}function G0(t,e,n){if(typeof e=="string"&&(e=[e]),!e||e.length===0)return n;let l=-1;for(;++l<e.length;)if(t.includes(e[l]))return!0;return!1}function xm(t,e,n,l){let i=-1;for(;++i<n.unsafe.length;)if(n.unsafe[i].character===`
`&&Q0(n.stack,n.unsafe[i]))return/[ \t]/.test(l.before)?"":" ";return`\\
`}function Z0(t,e){let n=String(t),l=n.indexOf(e),i=l,r=0,a=0;if(typeof e!="string")throw new TypeError("Expected substring");for(;l!==-1;)l===i?++r>a&&(a=r):r=1,i=l+e.length,l=n.indexOf(e,i);return a}function K0(t,e){return!!(e.options.fences===!1&&t.value&&!t.lang&&/[^ \r\n]/.test(t.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(t.value))}function F0(t){let e=t.options.fence||"`";if(e!=="`"&&e!=="~")throw new Error("Cannot serialize code with `"+e+"` for `options.fence`, expected `` ` `` or `~`");return e}function I0(t,e,n,l){let i=F0(n),r=t.value||"",a=i==="`"?"GraveAccent":"Tilde";if(K0(t,n)){let s=n.enter("codeIndented"),p=n.indentLines(r,bT);return s(),p}let u=n.createTracker(l),o=i.repeat(Math.max(Z0(r,i)+1,3)),c=n.enter("codeFenced"),f=u.move(o);if(t.lang){let s=n.enter(`codeFencedLang${a}`);f+=u.move(n.safe(t.lang,z({before:f,after:" ",encode:["`"]},u.current()))),s()}if(t.lang&&t.meta){let s=n.enter(`codeFencedMeta${a}`);f+=u.move(" "),f+=u.move(n.safe(t.meta,z({before:f,after:`
`,encode:["`"]},u.current()))),s()}return f+=u.move(`
`),r&&(f+=u.move(r+`
`)),f+=u.move(o),c(),f}function bT(t,e,n){return(n?"":"    ")+t}function Ui(t){let e=t.options.quote||'"';if(e!=='"'&&e!=="'")throw new Error("Cannot serialize title with `"+e+"` for `options.quote`, expected `\"`, or `'`");return e}function J0(t,e,n,l){let i=Ui(n),r=i==='"'?"Quote":"Apostrophe",a=n.enter("definition"),u=n.enter("label"),o=n.createTracker(l),c=o.move("[");return c+=o.move(n.safe(n.associationId(t),z({before:c,after:"]"},o.current()))),c+=o.move("]: "),u(),!t.url||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),c+=o.move("<"),c+=o.move(n.safe(t.url,z({before:c,after:">"},o.current()))),c+=o.move(">")):(u=n.enter("destinationRaw"),c+=o.move(n.safe(t.url,z({before:c,after:t.title?" ":`
`},o.current())))),u(),t.title&&(u=n.enter(`title${r}`),c+=o.move(" "+i),c+=o.move(n.safe(t.title,z({before:c,after:i},o.current()))),c+=o.move(i),u()),a(),c}function W0(t){let e=t.options.emphasis||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize emphasis with `"+e+"` for `options.emphasis`, expected `*`, or `_`");return e}function $n(t){return"&#x"+t.toString(16).toUpperCase()+";"}function Bi(t,e,n){let l=yn(t),i=yn(e);return l===void 0?i===void 0?n==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:l===1?i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}bm.peek=vT;function bm(t,e,n,l){let i=W0(n),r=n.enter("emphasis"),a=n.createTracker(l),u=a.move(i),o=a.move(n.containerPhrasing(t,z({after:i,before:u},a.current()))),c=o.charCodeAt(0),f=Bi(l.before.charCodeAt(l.before.length-1),c,i);f.inside&&(o=$n(c)+o.slice(1));let s=o.charCodeAt(o.length-1),p=Bi(l.after.charCodeAt(0),s,i);p.inside&&(o=o.slice(0,-1)+$n(s));let m=a.move(i);return r(),n.attentionEncodeSurroundingInfo={after:p.outside,before:f.outside},u+o+m}function vT(t,e,n){return n.options.emphasis||"*"}function P0(t,e){let n=!1;return _l(t,function(l){if("value"in l&&/\r?\n|\r/.test(l.value)||l.type==="break")return n=!0,Ol}),!!((!t.depth||t.depth<3)&&Al(t)&&(e.options.setext||n))}function $0(t,e,n,l){let i=Math.max(Math.min(6,t.depth||1),1),r=n.createTracker(l);if(P0(t,n)){let f=n.enter("headingSetext"),s=n.enter("phrasing"),p=n.containerPhrasing(t,gt(z({},r.current()),{before:`
`,after:`
`}));return s(),f(),p+`
`+(i===1?"=":"-").repeat(p.length-(Math.max(p.lastIndexOf("\r"),p.lastIndexOf(`
`))+1))}let a="#".repeat(i),u=n.enter("headingAtx"),o=n.enter("phrasing");r.move(a+" ");let c=n.containerPhrasing(t,z({before:"# ",after:`
`},r.current()));return/^[\t ]/.test(c)&&(c=$n(c.charCodeAt(0))+c.slice(1)),c=c?a+" "+c:a,n.options.closeAtx&&(c+=" "+a),o(),u(),c}vm.peek=ST;function vm(t){return t.value||""}function ST(){return"<"}Sm.peek=kT;function Sm(t,e,n,l){let i=Ui(n),r=i==='"'?"Quote":"Apostrophe",a=n.enter("image"),u=n.enter("label"),o=n.createTracker(l),c=o.move("![");return c+=o.move(n.safe(t.alt,z({before:c,after:"]"},o.current()))),c+=o.move("]("),u(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),c+=o.move("<"),c+=o.move(n.safe(t.url,z({before:c,after:">"},o.current()))),c+=o.move(">")):(u=n.enter("destinationRaw"),c+=o.move(n.safe(t.url,z({before:c,after:t.title?" ":")"},o.current())))),u(),t.title&&(u=n.enter(`title${r}`),c+=o.move(" "+i),c+=o.move(n.safe(t.title,z({before:c,after:i},o.current()))),c+=o.move(i),u()),c+=o.move(")"),a(),c}function kT(){return"!"}km.peek=ET;function km(t,e,n,l){let i=t.referenceType,r=n.enter("imageReference"),a=n.enter("label"),u=n.createTracker(l),o=u.move("!["),c=n.safe(t.alt,z({before:o,after:"]"},u.current()));o+=u.move(c+"]["),a();let f=n.stack;n.stack=[],a=n.enter("reference");let s=n.safe(n.associationId(t),z({before:o,after:"]"},u.current()));return a(),n.stack=f,r(),i==="full"||!c||c!==s?o+=u.move(s+"]"):i==="shortcut"?o=o.slice(0,-1):o+=u.move("]"),o}function ET(){return"!"}Em.peek=TT;function Em(t,e,n){let l=t.value||"",i="`",r=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(l);)i+="`";for(/[^ \r\n]/.test(l)&&(/^[ \r\n]/.test(l)&&/[ \r\n]$/.test(l)||/^`|`$/.test(l))&&(l=" "+l+" ");++r<n.unsafe.length;){let a=n.unsafe[r],u=n.compilePattern(a),o;if(a.atBreak)for(;o=u.exec(l);){let c=o.index;l.charCodeAt(c)===10&&l.charCodeAt(c-1)===13&&c--,l=l.slice(0,c)+" "+l.slice(o.index+1)}}return i+l+i}function TT(){return"`"}function Tm(t,e){let n=Al(t);return!!(!e.options.resourceLink&&t.url&&!t.title&&t.children&&t.children.length===1&&t.children[0].type==="text"&&(n===t.url||"mailto:"+n===t.url)&&/^[a-z][a-z+.-]+:/i.test(t.url)&&!/[\0- <>\u007F]/.test(t.url))}Am.peek=AT;function Am(t,e,n,l){let i=Ui(n),r=i==='"'?"Quote":"Apostrophe",a=n.createTracker(l),u,o;if(Tm(t,n)){let f=n.stack;n.stack=[],u=n.enter("autolink");let s=a.move("<");return s+=a.move(n.containerPhrasing(t,z({before:s,after:">"},a.current()))),s+=a.move(">"),u(),n.stack=f,s}u=n.enter("link"),o=n.enter("label");let c=a.move("[");return c+=a.move(n.containerPhrasing(t,z({before:c,after:"]("},a.current()))),c+=a.move("]("),o(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(o=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(t.url,z({before:c,after:">"},a.current()))),c+=a.move(">")):(o=n.enter("destinationRaw"),c+=a.move(n.safe(t.url,z({before:c,after:t.title?" ":")"},a.current())))),o(),t.title&&(o=n.enter(`title${r}`),c+=a.move(" "+i),c+=a.move(n.safe(t.title,z({before:c,after:i},a.current()))),c+=a.move(i),o()),c+=a.move(")"),u(),c}function AT(t,e,n){return Tm(t,n)?"<":"["}wm.peek=wT;function wm(t,e,n,l){let i=t.referenceType,r=n.enter("linkReference"),a=n.enter("label"),u=n.createTracker(l),o=u.move("["),c=n.containerPhrasing(t,z({before:o,after:"]"},u.current()));o+=u.move(c+"]["),a();let f=n.stack;n.stack=[],a=n.enter("reference");let s=n.safe(n.associationId(t),z({before:o,after:"]"},u.current()));return a(),n.stack=f,r(),i==="full"||!c||c!==s?o+=u.move(s+"]"):i==="shortcut"?o=o.slice(0,-1):o+=u.move("]"),o}function wT(){return"["}function Hi(t){let e=t.options.bullet||"*";if(e!=="*"&&e!=="+"&&e!=="-")throw new Error("Cannot serialize items with `"+e+"` for `options.bullet`, expected `*`, `+`, or `-`");return e}function tx(t){let e=Hi(t),n=t.options.bulletOther;if(!n)return e==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===e)throw new Error("Expected `bullet` (`"+e+"`) and `bulletOther` (`"+n+"`) to be different");return n}function ex(t){let e=t.options.bulletOrdered||".";if(e!=="."&&e!==")")throw new Error("Cannot serialize items with `"+e+"` for `options.bulletOrdered`, expected `.` or `)`");return e}function ko(t){let e=t.options.rule||"*";if(e!=="*"&&e!=="-"&&e!=="_")throw new Error("Cannot serialize rules with `"+e+"` for `options.rule`, expected `*`, `-`, or `_`");return e}function nx(t,e,n,l){let i=n.enter("list"),r=n.bulletCurrent,a=t.ordered?ex(n):Hi(n),u=t.ordered?a==="."?")":".":tx(n),o=e&&n.bulletLastUsed?a===n.bulletLastUsed:!1;if(!t.ordered){let f=t.children?t.children[0]:void 0;if((a==="*"||a==="-")&&f&&(!f.children||!f.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(o=!0),ko(n)===a&&f){let s=-1;for(;++s<t.children.length;){let p=t.children[s];if(p&&p.type==="listItem"&&p.children&&p.children[0]&&p.children[0].type==="thematicBreak"){o=!0;break}}}}o&&(a=u),n.bulletCurrent=a;let c=n.containerFlow(t,l);return n.bulletLastUsed=a,n.bulletCurrent=r,i(),c}function lx(t){let e=t.options.listItemIndent||"one";if(e!=="tab"&&e!=="one"&&e!=="mixed")throw new Error("Cannot serialize items with `"+e+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return e}function ix(t,e,n,l){let i=lx(n),r=n.bulletCurrent||Hi(n);e&&e.type==="list"&&e.ordered&&(r=(typeof e.start=="number"&&e.start>-1?e.start:1)+(n.options.incrementListMarker===!1?0:e.children.indexOf(t))+r);let a=r.length+1;(i==="tab"||i==="mixed"&&(e&&e.type==="list"&&e.spread||t.spread))&&(a=Math.ceil(a/4)*4);let u=n.createTracker(l);u.move(r+" ".repeat(a-r.length)),u.shift(a);let o=n.enter("listItem"),c=n.indentLines(n.containerFlow(t,u.current()),f);return o(),c;function f(s,p,m){return p?(m?"":" ".repeat(a))+s:(m?r:r+" ".repeat(a-r.length))+s}}function rx(t,e,n,l){let i=n.enter("paragraph"),r=n.enter("phrasing"),a=n.containerPhrasing(t,l);return r(),i(),a}var zm=Pn(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function ax(t,e,n,l){return(t.children.some(function(a){return zm(a)})?n.containerPhrasing:n.containerFlow).call(n,t,l)}function ux(t){let e=t.options.strong||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize strong with `"+e+"` for `options.strong`, expected `*`, or `_`");return e}Cm.peek=zT;function Cm(t,e,n,l){let i=ux(n),r=n.enter("strong"),a=n.createTracker(l),u=a.move(i+i),o=a.move(n.containerPhrasing(t,z({after:i,before:u},a.current()))),c=o.charCodeAt(0),f=Bi(l.before.charCodeAt(l.before.length-1),c,i);f.inside&&(o=$n(c)+o.slice(1));let s=o.charCodeAt(o.length-1),p=Bi(l.after.charCodeAt(0),s,i);p.inside&&(o=o.slice(0,-1)+$n(s));let m=a.move(i+i);return r(),n.attentionEncodeSurroundingInfo={after:p.outside,before:f.outside},u+o+m}function zT(t,e,n){return n.options.strong||"*"}function ox(t,e,n,l){return n.safe(t.value,l)}function cx(t){let e=t.options.ruleRepetition||3;if(e<3)throw new Error("Cannot serialize rules with repetition `"+e+"` for `options.ruleRepetition`, expected `3` or more");return e}function fx(t,e,n){let l=(ko(n)+(n.options.ruleSpaces?" ":"")).repeat(cx(n));return n.options.ruleSpaces?l.slice(0,-1):l}var sa={blockquote:X0,break:xm,code:I0,definition:J0,emphasis:bm,hardBreak:xm,heading:$0,html:vm,image:Sm,imageReference:km,inlineCode:Em,link:Am,linkReference:wm,list:nx,listItem:ix,paragraph:rx,root:ax,strong:Cm,text:ox,thematicBreak:fx};function Mm(){return{enter:{table:CT,tableData:sx,tableHeader:sx,tableRow:MT},exit:{codeText:OT,table:DT,tableData:Dm,tableHeader:Dm,tableRow:Dm}}}function CT(t){let e=t._align;this.enter({type:"table",align:e.map(function(n){return n==="none"?null:n}),children:[]},t),this.data.inTable=!0}function DT(t){this.exit(t),this.data.inTable=void 0}function MT(t){this.enter({type:"tableRow",children:[]},t)}function Dm(t){this.exit(t)}function sx(t){this.enter({type:"tableCell",children:[]},t)}function OT(t){let e=this.resume();this.data.inTable&&(e=e.replace(/\\([\\|])/g,_T));let n=this.stack[this.stack.length-1];n.type,n.value=e,this.exit(t)}function _T(t,e){return e==="|"?e:t}function Om(t){let e=t||{},n=e.tableCellPadding,l=e.tablePipeAlign,i=e.stringLength,r=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:p,table:a,tableCell:o,tableRow:u}};function a(m,y,v,T){return c(f(m,v,T),m.align)}function u(m,y,v,T){let h=s(m,v,T),d=c([h]);return d.slice(0,d.indexOf(`
`))}function o(m,y,v,T){let h=v.enter("tableCell"),d=v.enter("phrasing"),g=v.containerPhrasing(m,gt(z({},T),{before:r,after:r}));return d(),h(),g}function c(m,y){return V0(m,{align:y,alignDelimiters:l,padding:n,stringLength:i})}function f(m,y,v){let T=m.children,h=-1,d=[],g=y.enter("table");for(;++h<T.length;)d[h]=s(T[h],y,v);return g(),d}function s(m,y,v){let T=m.children,h=-1,d=[],g=y.enter("tableRow");for(;++h<T.length;)d[h]=o(T[h],m,y,v);return g(),d}function p(m,y,v){let T=sa.inlineCode(m,y,v);return v.stack.includes("tableCell")&&(T=T.replace(/\|/g,"\\$&")),T}}function _m(){return{exit:{taskListCheckValueChecked:mx,taskListCheckValueUnchecked:mx,paragraph:RT}}}function Rm(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:NT}}}function mx(t){let e=this.stack[this.stack.length-2];e.type,e.checked=t.type==="taskListCheckValueChecked"}function RT(t){let e=this.stack[this.stack.length-2];if(e&&e.type==="listItem"&&typeof e.checked=="boolean"){let n=this.stack[this.stack.length-1];n.type;let l=n.children[0];if(l&&l.type==="text"){let i=e.children,r=-1,a;for(;++r<i.length;){let u=i[r];if(u.type==="paragraph"){a=u;break}}a===n&&(l.value=l.value.slice(1),l.value.length===0?n.children.shift():n.position&&l.position&&typeof l.position.start.offset=="number"&&(l.position.start.column++,l.position.start.offset++,n.position.start=Object.assign({},l.position.start)))}}this.exit(t)}function NT(t,e,n,l){let i=t.children[0],r=typeof t.checked=="boolean"&&i&&i.type==="paragraph",a="["+(t.checked?"x":" ")+"] ",u=n.createTracker(l);r&&u.move(a);let o=sa.listItem(t,e,n,z(z({},l),u.current()));return r&&(o=o.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,c)),o;function c(f){return f+a}}function Nm(){return[mm(),hm(),gm(),Mm(),_m()]}function Lm(t){return{extensions:[pm(),dm(t),ym(),Om(t),Rm()]}}var LT={tokenize:jT,partial:!0},px={tokenize:YT,partial:!0},hx={tokenize:VT,partial:!0},dx={tokenize:XT,partial:!0},UT={tokenize:GT,partial:!0},gx={name:"wwwAutolink",tokenize:HT,previous:xx},yx={name:"protocolAutolink",tokenize:qT,previous:bx},xn={name:"emailAutolink",tokenize:BT,previous:vx},Fe={};function Bm(){return{text:Fe}}var Nl=48;for(;Nl<123;)Fe[Nl]=xn,Nl++,Nl===58?Nl=65:Nl===91&&(Nl=97);Fe[43]=xn;Fe[45]=xn;Fe[46]=xn;Fe[95]=xn;Fe[72]=[xn,yx];Fe[104]=[xn,yx];Fe[87]=[xn,gx];Fe[119]=[xn,gx];function BT(t,e,n){let l=this,i,r;return a;function a(s){return!Um(s)||!vx.call(l,l.previous)||Hm(l.events)?n(s):(t.enter("literalAutolink"),t.enter("literalAutolinkEmail"),u(s))}function u(s){return Um(s)?(t.consume(s),u):s===64?(t.consume(s),o):n(s)}function o(s){return s===46?t.check(UT,f,c)(s):s===45||s===95||vt(s)?(r=!0,t.consume(s),o):f(s)}function c(s){return t.consume(s),i=!0,o}function f(s){return r&&i&&Ot(l.previous)?(t.exit("literalAutolinkEmail"),t.exit("literalAutolink"),e(s)):n(s)}}function HT(t,e,n){let l=this;return i;function i(a){return a!==87&&a!==119||!xx.call(l,l.previous)||Hm(l.events)?n(a):(t.enter("literalAutolink"),t.enter("literalAutolinkWww"),t.check(LT,t.attempt(px,t.attempt(hx,r),n),n)(a))}function r(a){return t.exit("literalAutolinkWww"),t.exit("literalAutolink"),e(a)}}function qT(t,e,n){let l=this,i="",r=!1;return a;function a(s){return(s===72||s===104)&&bx.call(l,l.previous)&&!Hm(l.events)?(t.enter("literalAutolink"),t.enter("literalAutolinkHttp"),i+=String.fromCodePoint(s),t.consume(s),u):n(s)}function u(s){if(Ot(s)&&i.length<5)return i+=String.fromCodePoint(s),t.consume(s),u;if(s===58){let p=i.toLowerCase();if(p==="http"||p==="https")return t.consume(s),o}return n(s)}function o(s){return s===47?(t.consume(s),r?c:(r=!0,o)):n(s)}function c(s){return s===null||wl(s)||Z(s)||Ze(s)||zl(s)?n(s):t.attempt(px,t.attempt(hx,f),n)(s)}function f(s){return t.exit("literalAutolinkHttp"),t.exit("literalAutolink"),e(s)}}function jT(t,e,n){let l=0;return i;function i(a){return(a===87||a===119)&&l<3?(l++,t.consume(a),i):a===46&&l===3?(t.consume(a),r):n(a)}function r(a){return a===null?n(a):e(a)}}function YT(t,e,n){let l,i,r;return a;function a(c){return c===46||c===95?t.check(dx,o,u)(c):c===null||Z(c)||Ze(c)||c!==45&&zl(c)?o(c):(r=!0,t.consume(c),a)}function u(c){return c===95?l=!0:(i=l,l=void 0),t.consume(c),a}function o(c){return i||l||!r?n(c):e(c)}}function VT(t,e){let n=0,l=0;return i;function i(a){return a===40?(n++,t.consume(a),i):a===41&&l<n?r(a):a===33||a===34||a===38||a===39||a===41||a===42||a===44||a===46||a===58||a===59||a===60||a===63||a===93||a===95||a===126?t.check(dx,e,r)(a):a===null||Z(a)||Ze(a)?e(a):(t.consume(a),i)}function r(a){return a===41&&l++,t.consume(a),i}}function XT(t,e,n){return l;function l(u){return u===33||u===34||u===39||u===41||u===42||u===44||u===46||u===58||u===59||u===63||u===95||u===126?(t.consume(u),l):u===38?(t.consume(u),r):u===93?(t.consume(u),i):u===60||u===null||Z(u)||Ze(u)?e(u):n(u)}function i(u){return u===null||u===40||u===91||Z(u)||Ze(u)?e(u):l(u)}function r(u){return Ot(u)?a(u):n(u)}function a(u){return u===59?(t.consume(u),l):Ot(u)?(t.consume(u),a):n(u)}}function GT(t,e,n){return l;function l(r){return t.consume(r),i}function i(r){return vt(r)?n(r):e(r)}}function xx(t){return t===null||t===40||t===42||t===95||t===91||t===93||t===126||Z(t)}function bx(t){return!Ot(t)}function vx(t){return!(t===47||Um(t))}function Um(t){return t===43||t===45||t===46||t===95||vt(t)}function Hm(t){let e=t.length,n=!1;for(;e--;){let l=t[e][1];if((l.type==="labelLink"||l.type==="labelImage")&&!l._balanced){n=!0;break}if(l._gfmAutolinkLiteralWalkedInto){n=!1;break}}return t.length>0&&!n&&(t[t.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}var QT={tokenize:PT,partial:!0};function qm(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:IT,continuation:{tokenize:JT},exit:WT}},text:{91:{name:"gfmFootnoteCall",tokenize:FT},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:ZT,resolveTo:KT}}}}function ZT(t,e,n){let l=this,i=l.events.length,r=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),a;for(;i--;){let o=l.events[i][1];if(o.type==="labelImage"){a=o;break}if(o.type==="gfmFootnoteCall"||o.type==="labelLink"||o.type==="label"||o.type==="image"||o.type==="link")break}return u;function u(o){if(!a||!a._balanced)return n(o);let c=Qt(l.sliceSerialize({start:a.end,end:l.now()}));return c.codePointAt(0)!==94||!r.includes(c.slice(1))?n(o):(t.enter("gfmFootnoteCallLabelMarker"),t.consume(o),t.exit("gfmFootnoteCallLabelMarker"),e(o))}}function KT(t,e){let n=t.length,l;for(;n--;)if(t[n][1].type==="labelImage"&&t[n][0]==="enter"){l=t[n][1];break}t[n+1][1].type="data",t[n+3][1].type="gfmFootnoteCallLabelMarker";let i={type:"gfmFootnoteCall",start:Object.assign({},t[n+3][1].start),end:Object.assign({},t[t.length-1][1].end)},r={type:"gfmFootnoteCallMarker",start:Object.assign({},t[n+3][1].end),end:Object.assign({},t[n+3][1].end)};r.end.column++,r.end.offset++,r.end._bufferIndex++;let a={type:"gfmFootnoteCallString",start:Object.assign({},r.end),end:Object.assign({},t[t.length-1][1].start)},u={type:"chunkString",contentType:"string",start:Object.assign({},a.start),end:Object.assign({},a.end)},o=[t[n+1],t[n+2],["enter",i,e],t[n+3],t[n+4],["enter",r,e],["exit",r,e],["enter",a,e],["enter",u,e],["exit",u,e],["exit",a,e],t[t.length-2],t[t.length-1],["exit",i,e]];return t.splice(n,t.length-n+1,...o),t}function FT(t,e,n){let l=this,i=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),r=0,a;return u;function u(s){return t.enter("gfmFootnoteCall"),t.enter("gfmFootnoteCallLabelMarker"),t.consume(s),t.exit("gfmFootnoteCallLabelMarker"),o}function o(s){return s!==94?n(s):(t.enter("gfmFootnoteCallMarker"),t.consume(s),t.exit("gfmFootnoteCallMarker"),t.enter("gfmFootnoteCallString"),t.enter("chunkString").contentType="string",c)}function c(s){if(r>999||s===93&&!a||s===null||s===91||Z(s))return n(s);if(s===93){t.exit("chunkString");let p=t.exit("gfmFootnoteCallString");return i.includes(Qt(l.sliceSerialize(p)))?(t.enter("gfmFootnoteCallLabelMarker"),t.consume(s),t.exit("gfmFootnoteCallLabelMarker"),t.exit("gfmFootnoteCall"),e):n(s)}return Z(s)||(a=!0),r++,t.consume(s),s===92?f:c}function f(s){return s===91||s===92||s===93?(t.consume(s),r++,c):c(s)}}function IT(t,e,n){let l=this,i=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),r,a=0,u;return o;function o(y){return t.enter("gfmFootnoteDefinition")._container=!0,t.enter("gfmFootnoteDefinitionLabel"),t.enter("gfmFootnoteDefinitionLabelMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionLabelMarker"),c}function c(y){return y===94?(t.enter("gfmFootnoteDefinitionMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionMarker"),t.enter("gfmFootnoteDefinitionLabelString"),t.enter("chunkString").contentType="string",f):n(y)}function f(y){if(a>999||y===93&&!u||y===null||y===91||Z(y))return n(y);if(y===93){t.exit("chunkString");let v=t.exit("gfmFootnoteDefinitionLabelString");return r=Qt(l.sliceSerialize(v)),t.enter("gfmFootnoteDefinitionLabelMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionLabelMarker"),t.exit("gfmFootnoteDefinitionLabel"),p}return Z(y)||(u=!0),a++,t.consume(y),y===92?s:f}function s(y){return y===91||y===92||y===93?(t.consume(y),a++,f):f(y)}function p(y){return y===58?(t.enter("definitionMarker"),t.consume(y),t.exit("definitionMarker"),i.includes(r)||i.push(r),U(t,m,"gfmFootnoteDefinitionWhitespace")):n(y)}function m(y){return e(y)}}function JT(t,e,n){return t.check(Ke,e,t.attempt(QT,e,n))}function WT(t){t.exit("gfmFootnoteDefinition")}function PT(t,e,n){let l=this;return U(t,i,"gfmFootnoteDefinitionIndent",5);function i(r){let a=l.events[l.events.length-1];return a&&a[1].type==="gfmFootnoteDefinitionIndent"&&a[2].sliceSerialize(a[1],!0).length===4?e(r):n(r)}}function jm(t){let n=(t||{}).singleTilde,l={name:"strikethrough",tokenize:r,resolveAll:i};return n==null&&(n=!0),{text:{126:l},insideSpan:{null:[l]},attentionMarkers:{null:[126]}};function i(a,u){let o=-1;for(;++o<a.length;)if(a[o][0]==="enter"&&a[o][1].type==="strikethroughSequenceTemporary"&&a[o][1]._close){let c=o;for(;c--;)if(a[c][0]==="exit"&&a[c][1].type==="strikethroughSequenceTemporary"&&a[c][1]._open&&a[o][1].end.offset-a[o][1].start.offset===a[c][1].end.offset-a[c][1].start.offset){a[o][1].type="strikethroughSequence",a[c][1].type="strikethroughSequence";let f={type:"strikethrough",start:Object.assign({},a[c][1].start),end:Object.assign({},a[o][1].end)},s={type:"strikethroughText",start:Object.assign({},a[c][1].end),end:Object.assign({},a[o][1].start)},p=[["enter",f,u],["enter",a[c][1],u],["exit",a[c][1],u],["enter",s,u]],m=u.parser.constructs.insideSpan.null;m&&At(p,p.length,0,Jn(m,a.slice(c+1,o),u)),At(p,p.length,0,[["exit",s,u],["enter",a[o][1],u],["exit",a[o][1],u],["exit",f,u]]),At(a,c-1,o-c+3,p),o=c+p.length-2;break}}for(o=-1;++o<a.length;)a[o][1].type==="strikethroughSequenceTemporary"&&(a[o][1].type="data");return a}function r(a,u,o){let c=this.previous,f=this.events,s=0;return p;function p(y){return c===126&&f[f.length-1][1].type!=="characterEscape"?o(y):(a.enter("strikethroughSequenceTemporary"),m(y))}function m(y){let v=yn(c);if(y===126)return s>1?o(y):(a.consume(y),s++,m);if(s<2&&!n)return o(y);let T=a.exit("strikethroughSequenceTemporary"),h=yn(y);return T._open=!h||h===2&&!!v,T._close=!v||v===2&&!!h,u(y)}}}var Eo=class{constructor(){this.map=[]}add(e,n,l){$T(this,e,n,l)}consume(e){if(this.map.sort(function(r,a){return r[0]-a[0]}),this.map.length===0)return;let n=this.map.length,l=[];for(;n>0;)n-=1,l.push(e.slice(this.map[n][0]+this.map[n][1]),this.map[n][2]),e.length=this.map[n][0];l.push(e.slice()),e.length=0;let i=l.pop();for(;i;){for(let r of i)e.push(r);i=l.pop()}this.map.length=0}};function $T(t,e,n,l){let i=0;if(!(n===0&&l.length===0)){for(;i<t.map.length;){if(t.map[i][0]===e){t.map[i][1]+=n,t.map[i][2].push(...l);return}i+=1}t.map.push([e,n,l])}}function Sx(t,e){let n=!1,l=[];for(;e<t.length;){let i=t[e];if(n){if(i[0]==="enter")i[1].type==="tableContent"&&l.push(t[e+1][1].type==="tableDelimiterMarker"?"left":"none");else if(i[1].type==="tableContent"){if(t[e-1][1].type==="tableDelimiterMarker"){let r=l.length-1;l[r]=l[r]==="left"?"center":"right"}}else if(i[1].type==="tableDelimiterRow")break}else i[0]==="enter"&&i[1].type==="tableDelimiterRow"&&(n=!0);e+=1}return l}function Ym(){return{flow:{null:{name:"table",tokenize:tA,resolveAll:eA}}}}function tA(t,e,n){let l=this,i=0,r=0,a;return u;function u(S){let P=l.events.length-1;for(;P>-1;){let q=l.events[P][1].type;if(q==="lineEnding"||q==="linePrefix")P--;else break}let Q=P>-1?l.events[P][1].type:null,N=Q==="tableHead"||Q==="tableRow"?k:o;return N===k&&l.parser.lazy[l.now().line]?n(S):N(S)}function o(S){return t.enter("tableHead"),t.enter("tableRow"),c(S)}function c(S){return S===124||(a=!0,r+=1),f(S)}function f(S){return S===null?n(S):R(S)?r>1?(r=0,l.interrupt=!0,t.exit("tableRow"),t.enter("lineEnding"),t.consume(S),t.exit("lineEnding"),m):n(S):B(S)?U(t,f,"whitespace")(S):(r+=1,a&&(a=!1,i+=1),S===124?(t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),a=!0,f):(t.enter("data"),s(S)))}function s(S){return S===null||S===124||Z(S)?(t.exit("data"),f(S)):(t.consume(S),S===92?p:s)}function p(S){return S===92||S===124?(t.consume(S),s):s(S)}function m(S){return l.interrupt=!1,l.parser.lazy[l.now().line]?n(S):(t.enter("tableDelimiterRow"),a=!1,B(S)?U(t,y,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(S):y(S))}function y(S){return S===45||S===58?T(S):S===124?(a=!0,t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),v):C(S)}function v(S){return B(S)?U(t,T,"whitespace")(S):T(S)}function T(S){return S===58?(r+=1,a=!0,t.enter("tableDelimiterMarker"),t.consume(S),t.exit("tableDelimiterMarker"),h):S===45?(r+=1,h(S)):S===null||R(S)?E(S):C(S)}function h(S){return S===45?(t.enter("tableDelimiterFiller"),d(S)):C(S)}function d(S){return S===45?(t.consume(S),d):S===58?(a=!0,t.exit("tableDelimiterFiller"),t.enter("tableDelimiterMarker"),t.consume(S),t.exit("tableDelimiterMarker"),g):(t.exit("tableDelimiterFiller"),g(S))}function g(S){return B(S)?U(t,E,"whitespace")(S):E(S)}function E(S){return S===124?y(S):S===null||R(S)?!a||i!==r?C(S):(t.exit("tableDelimiterRow"),t.exit("tableHead"),e(S)):C(S)}function C(S){return n(S)}function k(S){return t.enter("tableRow"),D(S)}function D(S){return S===124?(t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),D):S===null||R(S)?(t.exit("tableRow"),e(S)):B(S)?U(t,D,"whitespace")(S):(t.enter("data"),O(S))}function O(S){return S===null||S===124||Z(S)?(t.exit("data"),D(S)):(t.consume(S),S===92?L:O)}function L(S){return S===92||S===124?(t.consume(S),O):O(S)}}function eA(t,e){let n=-1,l=!0,i=0,r=[0,0,0,0],a=[0,0,0,0],u=!1,o=0,c,f,s,p=new Eo;for(;++n<t.length;){let m=t[n],y=m[1];m[0]==="enter"?y.type==="tableHead"?(u=!1,o!==0&&(kx(p,e,o,c,f),f=void 0,o=0),c={type:"table",start:Object.assign({},y.start),end:Object.assign({},y.end)},p.add(n,0,[["enter",c,e]])):y.type==="tableRow"||y.type==="tableDelimiterRow"?(l=!0,s=void 0,r=[0,0,0,0],a=[0,n+1,0,0],u&&(u=!1,f={type:"tableBody",start:Object.assign({},y.start),end:Object.assign({},y.end)},p.add(n,0,[["enter",f,e]])),i=y.type==="tableDelimiterRow"?2:f?3:1):i&&(y.type==="data"||y.type==="tableDelimiterMarker"||y.type==="tableDelimiterFiller")?(l=!1,a[2]===0&&(r[1]!==0&&(a[0]=a[1],s=To(p,e,r,i,void 0,s),r=[0,0,0,0]),a[2]=n)):y.type==="tableCellDivider"&&(l?l=!1:(r[1]!==0&&(a[0]=a[1],s=To(p,e,r,i,void 0,s)),r=a,a=[r[1],n,0,0])):y.type==="tableHead"?(u=!0,o=n):y.type==="tableRow"||y.type==="tableDelimiterRow"?(o=n,r[1]!==0?(a[0]=a[1],s=To(p,e,r,i,n,s)):a[1]!==0&&(s=To(p,e,a,i,n,s)),i=0):i&&(y.type==="data"||y.type==="tableDelimiterMarker"||y.type==="tableDelimiterFiller")&&(a[3]=n)}for(o!==0&&kx(p,e,o,c,f),p.consume(e.events),n=-1;++n<e.events.length;){let m=e.events[n];m[0]==="enter"&&m[1].type==="table"&&(m[1]._align=Sx(e.events,n))}return t}function To(t,e,n,l,i,r){let a=l===1?"tableHeader":l===2?"tableDelimiter":"tableData",u="tableContent";n[0]!==0&&(r.end=Object.assign({},qi(e.events,n[0])),t.add(n[0],0,[["exit",r,e]]));let o=qi(e.events,n[1]);if(r={type:a,start:Object.assign({},o),end:Object.assign({},o)},t.add(n[1],0,[["enter",r,e]]),n[2]!==0){let c=qi(e.events,n[2]),f=qi(e.events,n[3]),s={type:u,start:Object.assign({},c),end:Object.assign({},f)};if(t.add(n[2],0,[["enter",s,e]]),l!==2){let p=e.events[n[2]],m=e.events[n[3]];if(p[1].end=Object.assign({},m[1].end),p[1].type="chunkText",p[1].contentType="text",n[3]>n[2]+1){let y=n[2]+1,v=n[3]-n[2]-1;t.add(y,v,[])}}t.add(n[3]+1,0,[["exit",s,e]])}return i!==void 0&&(r.end=Object.assign({},qi(e.events,i)),t.add(i,0,[["exit",r,e]]),r=void 0),r}function kx(t,e,n,l,i){let r=[],a=qi(e.events,n);i&&(i.end=Object.assign({},a),r.push(["exit",i,e])),l.end=Object.assign({},a),r.push(["exit",l,e]),t.add(n+1,0,r)}function qi(t,e){let n=t[e],l=n[0]==="enter"?"start":"end";return n[1][l]}var nA={name:"tasklistCheck",tokenize:lA};function Vm(){return{text:{91:nA}}}function lA(t,e,n){let l=this;return i;function i(o){return l.previous!==null||!l._gfmTasklistFirstContentOfListItem?n(o):(t.enter("taskListCheck"),t.enter("taskListCheckMarker"),t.consume(o),t.exit("taskListCheckMarker"),r)}function r(o){return Z(o)?(t.enter("taskListCheckValueUnchecked"),t.consume(o),t.exit("taskListCheckValueUnchecked"),a):o===88||o===120?(t.enter("taskListCheckValueChecked"),t.consume(o),t.exit("taskListCheckValueChecked"),a):n(o)}function a(o){return o===93?(t.enter("taskListCheckMarker"),t.consume(o),t.exit("taskListCheckMarker"),t.exit("taskListCheck"),u):n(o)}function u(o){return R(o)?e(o):B(o)?t.check({tokenize:iA},e,n)(o):n(o)}}function iA(t,e,n){return U(t,l,"whitespace");function l(i){return i===null?n(i):e(i)}}function Ex(t){return Ju([Bm(),qm(),jm(t),Ym(),Vm()])}var rA={};function Ao(t){let e=this,n=t||rA,l=e.data(),i=l.micromarkExtensions||(l.micromarkExtensions=[]),r=l.fromMarkdownExtensions||(l.fromMarkdownExtensions=[]),a=l.toMarkdownExtensions||(l.toMarkdownExtensions=[]);i.push(Ex(n)),r.push(Nm()),a.push(Lm(n))}var I=bn(Iu()),_t={container:{position:"fixed",bottom:"20px",right:"20px",zIndex:2147483647,fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:"14px",lineHeight:"1.5",color:"#333",pointerEvents:"auto"},containerLeft:{left:"20px",right:"auto"},bubble:{width:"60px",height:"60px",borderRadius:"50%",border:"none",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",transition:"all 0.3s ease",outline:"none",position:"relative"},bubbleHover:{transform:"scale(1.05)",boxShadow:"0 6px 16px rgba(0, 0, 0, 0.2)"},chatWindow:{position:"absolute",bottom:"70px",right:"0",width:"360px",height:"600px",maxHeight:"calc(100vh - 100px)",backgroundColor:"#ffffff",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.12)",border:"1px solid #e5e7eb",display:"flex",flexDirection:"column",overflow:"hidden",transform:"translateY(10px)",opacity:0,transition:"all 0.3s ease",pointerEvents:"auto"},chatWindowLeft:{left:"0",right:"auto"},chatWindowOpen:{transform:"translateY(0)",opacity:1},header:{padding:"16px 20px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between",backgroundColor:"#ffffff"},headerTitle:{margin:0,fontSize:"16px",fontWeight:"600",color:"#111827"},closeButton:{background:"none",border:"none",cursor:"pointer",padding:"4px",borderRadius:"4px",color:"#6b7280",fontSize:"18px",lineHeight:1,outline:"none"},messagesContainer:{flex:1,padding:"16px",overflowY:"auto",display:"flex",flexDirection:"column",gap:"12px"},message:{maxWidth:"80%",padding:"8px 12px",borderRadius:"12px",fontSize:"14px",lineHeight:"1.4"},userMessage:{alignSelf:"flex-end",backgroundColor:"#3b82f6",color:"#ffffff"},assistantMessage:{alignSelf:"flex-start",backgroundColor:"#f3f4f6",color:"#111827"},inputContainer:{padding:"16px",borderTop:"1px solid #e5e7eb",display:"flex",gap:"8px",backgroundColor:"#ffffff",color:"#111827 !important"},input:{flex:1,padding:"8px 12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",outline:"none",resize:"none",minHeight:"36px",maxHeight:"100px",backgroundColor:"#ffffff"},sendButton:{padding:"8px 16px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"500",outline:"none",transition:"all 0.2s ease"},sendButtonDisabled:{opacity:.5,cursor:"not-allowed"},markdownContent:{lineHeight:"1.6"},markdownH1:{fontSize:"18px",fontWeight:"700",margin:"16px 0 8px 0",color:"inherit"},markdownH2:{fontSize:"16px",fontWeight:"600",margin:"14px 0 6px 0",color:"inherit"},markdownH3:{fontSize:"15px",fontWeight:"600",margin:"12px 0 4px 0",color:"inherit"},markdownP:{margin:"8px 0",color:"inherit"},markdownUl:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},markdownOl:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},markdownLi:{margin:"4px 0",color:"inherit"},markdownCode:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"2px 4px",borderRadius:"3px",fontSize:"13px",fontFamily:'Monaco, Consolas, "Courier New", monospace',color:"inherit"},markdownPre:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:'Monaco, Consolas, "Courier New", monospace',color:"inherit"},markdownBlockquote:{borderLeft:"3px solid #ddd",paddingLeft:"12px",margin:"8px 0",fontStyle:"italic",color:"inherit"},markdownA:{color:"#3b82f6",textDecoration:"underline"},markdownStrong:{fontWeight:"600",color:"inherit"},markdownEm:{fontStyle:"italic",color:"inherit"}};function aA({content:t,primaryColor:e}){return(0,I.jsx)("div",{style:_t.markdownContent,children:(0,I.jsx)(rm,{remarkPlugins:[Ao],components:{h1:({children:n})=>(0,I.jsx)("h1",{style:{fontSize:"18px",fontWeight:700,margin:"16px 0 8px 0",color:"inherit"},children:n}),h2:({children:n})=>(0,I.jsx)("h2",{style:{fontSize:"16px",fontWeight:600,margin:"14px 0 6px 0",color:"inherit"},children:n}),h3:({children:n})=>(0,I.jsx)("h3",{style:{fontSize:"15px",fontWeight:600,margin:"12px 0 4px 0",color:"inherit"},children:n}),p:({children:n})=>(0,I.jsx)("p",{style:{margin:"8px 0",color:"inherit"},children:n}),ul:({children:n})=>(0,I.jsx)("ul",{style:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},children:n}),ol:({children:n})=>(0,I.jsx)("ol",{style:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},children:n}),li:({children:n})=>(0,I.jsx)("li",{style:{margin:"4px 0",color:"inherit"},children:n}),code:({children:n,className:l})=>l?(0,I.jsx)("code",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit",display:"block"},children:n}):(0,I.jsx)("code",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"2px 4px",borderRadius:"3px",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit"},children:n}),pre:({children:n})=>(0,I.jsx)("pre",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit"},children:n}),blockquote:({children:n})=>(0,I.jsx)("blockquote",{style:{borderLeft:"3px solid #ddd",paddingLeft:"12px",margin:"8px 0",fontStyle:"italic",color:"inherit"},children:n}),a:({children:n,href:l})=>(0,I.jsx)("a",{href:l,style:{color:e,textDecoration:"underline"},target:"_blank",rel:"noopener noreferrer",children:n}),strong:({children:n})=>(0,I.jsx)("strong",{style:{fontWeight:600,color:"inherit"},children:n}),em:({children:n})=>(0,I.jsx)("em",{style:{fontStyle:"italic",color:"inherit"},children:n})},children:t})})}function uA({config:t}){let[e,n]=(0,be.useState)(t.initiallyOpen||!1),[l,i]=(0,be.useState)([]),[r,a]=(0,be.useState)(""),[u,o]=(0,be.useState)(!1),[c,f]=(0,be.useState)(!1),s=(0,be.useRef)(null),p=t.primaryColor||"#3b82f6",m=t.secondaryColor||"#ffffff",y=t.position||"bottom-right",v=k=>{let D=k.replace("#",""),O=Number.parseInt(D.substring(0,2),16),L=Number.parseInt(D.substring(2,4),16),S=Number.parseInt(D.substring(4,6),16);return(O*299+L*587+S*114)/1e3>128?"#111827":"#ffffff"},T=v(m),h=v("#f3f4f6");(0,be.useEffect)(()=>{s.current&&s.current.scrollIntoView({behavior:"smooth"})},[l]),(0,be.useEffect)(()=>{e&&l.length===0&&t.welcomeMessage&&i([{id:"welcome",content:t.welcomeMessage,role:"assistant",timestamp:new Date}])},[e,l.length,t.welcomeMessage]);let d=(0,be.useCallback)(()=>Xi(this,null,function*(){var L;if(!r.trim()||u)return;let k={id:Date.now().toString(),content:r.trim(),role:"user",timestamp:new Date};i(S=>[...S,k]),a(""),o(!0);let D=(Date.now()+1).toString(),O={id:D,content:"",role:"assistant",timestamp:new Date};i(S=>[...S,O]);try{let S=yield fetch(`${t.baseUrl}/api/chat/${t.websiteId}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...l,k].map(q=>({role:q.role,content:q.content})),websiteId:t.websiteId,visitorId:t.visitorId})});if(!S.ok)throw new Error("Failed to send message");let P=(L=S.body)==null?void 0:L.getReader(),Q=new TextDecoder,N="";if(P)for(;;){let{done:q,value:j}=yield P.read();if(q)break;let St=Q.decode(j,{stream:!0}).split(`
`);for(let Pt of St)if(Pt.startsWith("0:")){let ae=Pt.slice(2);N+=ae,u&&ae.trim()&&o(!1);let x=N.replace(/([a-zA-Z])""/g,"$1").replace(/""([a-zA-Z])/g,"$1").replace(/""([!?.,;:])/g,"$1").replace(/([!?.,;:])""/g,"$1").replace(/""'/g,"'").replace(/'""/g,"'").replace(/"""/g,'"').replace(/""/g,"").replace(/\\"/g,'"').replace(/\\""/g,'"').replace(/"{2,}/g,'"').replace(/^"(.*)"$/g,"$1").replace(/^"(.*)"$/gm,"$1").replace(/"([^"]*)""/g,'"$1"').replace(/""([^"]*)""/g,'"$1"').replace(/\\\\"/g,'"').replace(/"\s*"/g,'"').replace(/"\n"/g,`"
`).replace(/"\r"/g,'"\r').replace(/([a-zA-Z])"([a-zA-Z])/g,"$1'$2").replace(/\s+"/g,' "').replace(/"\s+/g,'" ').trim();i($t=>$t.map(ue=>ue.id===D?gt(z({},ue),{content:x}):ue))}}N||i(q=>q.map(j=>j.id===D?gt(z({},j),{content:"Sorry, I encountered an error."}):j))}catch(S){console.error("Error sending message:",S),i(P=>P.map(Q=>Q.id===D?gt(z({},Q),{content:"Sorry, I encountered an error. Please try again."}):Q))}finally{o(!1)}}),[r,u,l,t]),g=k=>{k.key==="Enter"&&!k.shiftKey&&(k.preventDefault(),d())},E=()=>{n(!e)},C=()=>{n(!1)};return(0,I.jsxs)("div",{style:z(z({},_t.container),y==="bottom-left"?_t.containerLeft:{}),children:[e&&(0,I.jsxs)("div",{style:z(z(z({},_t.chatWindow),y==="bottom-left"?_t.chatWindowLeft:{}),_t.chatWindowOpen),children:[(0,I.jsxs)("div",{style:gt(z({},_t.header),{backgroundColor:m}),children:[(0,I.jsx)("h3",{style:gt(z({},_t.headerTitle),{color:T}),children:t.headerText||"Chat Assistant"}),(0,I.jsx)("button",{type:"button",style:gt(z({},_t.closeButton),{color:T}),onClick:C,"aria-label":"Close chat",children:"\xD7"})]}),(0,I.jsxs)("div",{style:_t.messagesContainer,children:[l.map(k=>(0,I.jsx)("div",{style:z(z(z({},_t.message),k.role==="user"?_t.userMessage:gt(z({},_t.assistantMessage),{color:h})),k.role==="user"?{backgroundColor:p}:{}),children:k.role==="assistant"?(0,I.jsx)(aA,{content:k.content,primaryColor:p}):k.content},k.id)),(0,I.jsx)("div",{ref:s})]}),(0,I.jsxs)("div",{style:gt(z({},_t.inputContainer),{backgroundColor:m,color:T}),children:[(0,I.jsx)("textarea",{style:gt(z({},_t.input),{backgroundColor:m,color:T}),value:r,onChange:k=>a(k.target.value),onKeyDown:g,placeholder:"Type your message...",rows:1}),(0,I.jsx)("button",{type:"button",style:z(gt(z({},_t.sendButton),{backgroundColor:p,color:"#ffffff"}),!r.trim()||u?_t.sendButtonDisabled:{}),onClick:d,disabled:!r.trim()||u,children:"Send"})]})]}),(0,I.jsx)("button",{type:"button",style:z(gt(z({},_t.bubble),{backgroundColor:p}),c?_t.bubbleHover:{}),onClick:E,onMouseEnter:()=>f(!0),onMouseLeave:()=>f(!1),"aria-label":e?"Close chat":"Open chat",children:(0,I.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",style:{color:"#ffffff"},children:[(0,I.jsx)("title",{children:e?"Close chat":"Open chat"}),e?(0,I.jsx)("path",{d:"M18 6L6 18M6 6l12 12"}):(0,I.jsx)("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})]})})]})}function Ax({shadowRoot:t,config:e}){let n=document.createElement("div");t.appendChild(n);let l=(0,Tx.createRoot)(n);return l.render((0,I.jsx)(uA,{config:e,shadowRoot:t})),{open(){},close(){},toggle(){},destroy(){l.unmount(),n.parentNode&&n.parentNode.removeChild(n)}}}window.BublWidgetV2={init:Ax};})();
/*! Bundled license information:

react/cjs/react.production.js:
  (**
   * @license React
   * react.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

scheduler/cjs/scheduler.production.js:
  (**
   * @license React
   * scheduler.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom.production.js:
  (**
   * @license React
   * react-dom.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom-client.production.js:
  (**
   * @license React
   * react-dom-client.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react/cjs/react-jsx-runtime.production.js:
  (**
   * @license React
   * react-jsx-runtime.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
