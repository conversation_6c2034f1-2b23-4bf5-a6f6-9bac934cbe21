
/**
 * Bubl Widget V2 Bundle
 * Self-contained React widget with Shadow DOM isolation
 * Generated: 2025-06-03T08:38:44.864Z
 */

"use strict";(()=>{var bx=Object.create;var za=Object.defineProperty,xx=Object.defineProperties,vx=Object.getOwnPropertyDescriptor,Sx=Object.getOwnPropertyDescriptors,kx=Object.getOwnPropertyNames,Aa=Object.getOwnPropertySymbols,wx=Object.getPrototypeOf,Go=Object.prototype.hasOwnProperty,dp=Object.prototype.propertyIsEnumerable;var gp=Math.pow,hp=(t,e,n)=>e in t?za(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,M=(t,e)=>{for(var n in e||(e={}))Go.call(e,n)&&hp(t,n,e[n]);if(Aa)for(var n of Aa(e))dp.call(e,n)&&hp(t,n,e[n]);return t},wt=(t,e)=>xx(t,Sx(e));var Ca=(t,e)=>{var n={};for(var l in t)Go.call(t,l)&&e.indexOf(l)<0&&(n[l]=t[l]);if(t!=null&&Aa)for(var l of Aa(t))e.indexOf(l)<0&&dp.call(t,l)&&(n[l]=t[l]);return n};var le=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),yp=(t,e)=>{for(var n in e)za(t,n,{get:e[n],enumerable:!0})},Ex=(t,e,n,l)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of kx(e))!Go.call(t,i)&&i!==n&&za(t,i,{get:()=>e[i],enumerable:!(l=vx(e,i))||l.enumerable});return t};var Fe=(t,e,n)=>(n=t!=null?bx(wx(t)):{},Ex(e||!t||!t.__esModule?za(n,"default",{value:t,enumerable:!0}):n,t));var nr=(t,e,n)=>new Promise((l,i)=>{var r=o=>{try{u(n.next(o))}catch(c){i(c)}},a=o=>{try{u(n.throw(o))}catch(c){i(c)}},u=o=>o.done?l(o.value):Promise.resolve(o.value).then(r,a);u((n=n.apply(t,e)).next())});var Mp=le(K=>{"use strict";var Xo=Symbol.for("react.transitional.element"),Tx=Symbol.for("react.portal"),Ax=Symbol.for("react.fragment"),zx=Symbol.for("react.strict_mode"),Cx=Symbol.for("react.profiler"),Mx=Symbol.for("react.consumer"),Dx=Symbol.for("react.context"),Ox=Symbol.for("react.forward_ref"),Rx=Symbol.for("react.suspense"),_x=Symbol.for("react.memo"),wp=Symbol.for("react.lazy"),bp=Symbol.iterator;function Nx(t){return t===null||typeof t!="object"?null:(t=bp&&t[bp]||t["@@iterator"],typeof t=="function"?t:null)}var Ep={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Tp=Object.assign,Ap={};function Kl(t,e,n){this.props=t,this.context=e,this.refs=Ap,this.updater=n||Ep}Kl.prototype.isReactComponent={};Kl.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Kl.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function zp(){}zp.prototype=Kl.prototype;function Qo(t,e,n){this.props=t,this.context=e,this.refs=Ap,this.updater=n||Ep}var Zo=Qo.prototype=new zp;Zo.constructor=Qo;Tp(Zo,Kl.prototype);Zo.isPureReactComponent=!0;var xp=Array.isArray,yt={H:null,A:null,T:null,S:null,V:null},Cp=Object.prototype.hasOwnProperty;function Fo(t,e,n,l,i,r){return n=r.ref,{$$typeof:Xo,type:t,key:e,ref:n!==void 0?n:null,props:r}}function Lx(t,e){return Fo(t.type,e,void 0,void 0,void 0,t.props)}function Ko(t){return typeof t=="object"&&t!==null&&t.$$typeof===Xo}function Ux(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var vp=/\/+/g;function Vo(t,e){return typeof t=="object"&&t!==null&&t.key!=null?Ux(""+t.key):e.toString(36)}function Sp(){}function Bx(t){switch(t.status){case"fulfilled":return t.value;case"rejected":throw t.reason;default:switch(typeof t.status=="string"?t.then(Sp,Sp):(t.status="pending",t.then(function(e){t.status==="pending"&&(t.status="fulfilled",t.value=e)},function(e){t.status==="pending"&&(t.status="rejected",t.reason=e)})),t.status){case"fulfilled":return t.value;case"rejected":throw t.reason}}throw t}function Fl(t,e,n,l,i){var r=typeof t;(r==="undefined"||r==="boolean")&&(t=null);var a=!1;if(t===null)a=!0;else switch(r){case"bigint":case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case Xo:case Tx:a=!0;break;case wp:return a=t._init,Fl(a(t._payload),e,n,l,i)}}if(a)return i=i(t),a=l===""?"."+Vo(t,0):l,xp(i)?(n="",a!=null&&(n=a.replace(vp,"$&/")+"/"),Fl(i,e,n,"",function(c){return c})):i!=null&&(Ko(i)&&(i=Lx(i,n+(i.key==null||t&&t.key===i.key?"":(""+i.key).replace(vp,"$&/")+"/")+a)),e.push(i)),1;a=0;var u=l===""?".":l+":";if(xp(t))for(var o=0;o<t.length;o++)l=t[o],r=u+Vo(l,o),a+=Fl(l,e,n,r,i);else if(o=Nx(t),typeof o=="function")for(t=o.call(t),o=0;!(l=t.next()).done;)l=l.value,r=u+Vo(l,o++),a+=Fl(l,e,n,r,i);else if(r==="object"){if(typeof t.then=="function")return Fl(Bx(t),e,n,l,i);throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.")}return a}function Ma(t,e,n){if(t==null)return t;var l=[],i=0;return Fl(t,l,"","",function(r){return e.call(n,r,i++)}),l}function Hx(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var kp=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function qx(){}K.Children={map:Ma,forEach:function(t,e,n){Ma(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return Ma(t,function(){e++}),e},toArray:function(t){return Ma(t,function(e){return e})||[]},only:function(t){if(!Ko(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};K.Component=Kl;K.Fragment=Ax;K.Profiler=Cx;K.PureComponent=Qo;K.StrictMode=zx;K.Suspense=Rx;K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=yt;K.__COMPILER_RUNTIME={__proto__:null,c:function(t){return yt.H.useMemoCache(t)}};K.cache=function(t){return function(){return t.apply(null,arguments)}};K.cloneElement=function(t,e,n){if(t==null)throw Error("The argument must be a React element, but you passed "+t+".");var l=Tp({},t.props),i=t.key,r=void 0;if(e!=null)for(a in e.ref!==void 0&&(r=void 0),e.key!==void 0&&(i=""+e.key),e)!Cp.call(e,a)||a==="key"||a==="__self"||a==="__source"||a==="ref"&&e.ref===void 0||(l[a]=e[a]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var u=Array(a),o=0;o<a;o++)u[o]=arguments[o+2];l.children=u}return Fo(t.type,i,void 0,void 0,r,l)};K.createContext=function(t){return t={$$typeof:Dx,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null},t.Provider=t,t.Consumer={$$typeof:Mx,_context:t},t};K.createElement=function(t,e,n){var l,i={},r=null;if(e!=null)for(l in e.key!==void 0&&(r=""+e.key),e)Cp.call(e,l)&&l!=="key"&&l!=="__self"&&l!=="__source"&&(i[l]=e[l]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var u=Array(a),o=0;o<a;o++)u[o]=arguments[o+2];i.children=u}if(t&&t.defaultProps)for(l in a=t.defaultProps,a)i[l]===void 0&&(i[l]=a[l]);return Fo(t,r,void 0,void 0,null,i)};K.createRef=function(){return{current:null}};K.forwardRef=function(t){return{$$typeof:Ox,render:t}};K.isValidElement=Ko;K.lazy=function(t){return{$$typeof:wp,_payload:{_status:-1,_result:t},_init:Hx}};K.memo=function(t,e){return{$$typeof:_x,type:t,compare:e===void 0?null:e}};K.startTransition=function(t){var e=yt.T,n={};yt.T=n;try{var l=t(),i=yt.S;i!==null&&i(n,l),typeof l=="object"&&l!==null&&typeof l.then=="function"&&l.then(qx,kp)}catch(r){kp(r)}finally{yt.T=e}};K.unstable_useCacheRefresh=function(){return yt.H.useCacheRefresh()};K.use=function(t){return yt.H.use(t)};K.useActionState=function(t,e,n){return yt.H.useActionState(t,e,n)};K.useCallback=function(t,e){return yt.H.useCallback(t,e)};K.useContext=function(t){return yt.H.useContext(t)};K.useDebugValue=function(){};K.useDeferredValue=function(t,e){return yt.H.useDeferredValue(t,e)};K.useEffect=function(t,e,n){var l=yt.H;if(typeof n=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return l.useEffect(t,e)};K.useId=function(){return yt.H.useId()};K.useImperativeHandle=function(t,e,n){return yt.H.useImperativeHandle(t,e,n)};K.useInsertionEffect=function(t,e){return yt.H.useInsertionEffect(t,e)};K.useLayoutEffect=function(t,e){return yt.H.useLayoutEffect(t,e)};K.useMemo=function(t,e){return yt.H.useMemo(t,e)};K.useOptimistic=function(t,e){return yt.H.useOptimistic(t,e)};K.useReducer=function(t,e,n){return yt.H.useReducer(t,e,n)};K.useRef=function(t){return yt.H.useRef(t)};K.useState=function(t){return yt.H.useState(t)};K.useSyncExternalStore=function(t,e,n){return yt.H.useSyncExternalStore(t,e,n)};K.useTransition=function(){return yt.H.useTransition()};K.version="19.1.0"});var lr=le((MA,Dp)=>{"use strict";Dp.exports=Mp()});var jp=le(bt=>{"use strict";function Wo(t,e){var n=t.length;t.push(e);t:for(;0<n;){var l=n-1>>>1,i=t[l];if(0<Da(i,e))t[l]=e,t[n]=i,n=l;else break t}}function Ke(t){return t.length===0?null:t[0]}function Ra(t){if(t.length===0)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;t:for(var l=0,i=t.length,r=i>>>1;l<r;){var a=2*(l+1)-1,u=t[a],o=a+1,c=t[o];if(0>Da(u,n))o<i&&0>Da(c,u)?(t[l]=c,t[o]=n,l=o):(t[l]=u,t[a]=n,l=a);else if(o<i&&0>Da(c,n))t[l]=c,t[o]=n,l=o;else break t}}return e}function Da(t,e){var n=t.sortIndex-e.sortIndex;return n!==0?n:t.id-e.id}bt.unstable_now=void 0;typeof performance=="object"&&typeof performance.now=="function"?(Op=performance,bt.unstable_now=function(){return Op.now()}):(Io=Date,Rp=Io.now(),bt.unstable_now=function(){return Io.now()-Rp});var Op,Io,Rp,cn=[],Dn=[],jx=1,ze=null,Pt=3,$o=!1,ir=!1,rr=!1,tc=!1,Lp=typeof setTimeout=="function"?setTimeout:null,Up=typeof clearTimeout=="function"?clearTimeout:null,_p=typeof setImmediate!="undefined"?setImmediate:null;function Oa(t){for(var e=Ke(Dn);e!==null;){if(e.callback===null)Ra(Dn);else if(e.startTime<=t)Ra(Dn),e.sortIndex=e.expirationTime,Wo(cn,e);else break;e=Ke(Dn)}}function ec(t){if(rr=!1,Oa(t),!ir)if(Ke(cn)!==null)ir=!0,Jl||(Jl=!0,Il());else{var e=Ke(Dn);e!==null&&nc(ec,e.startTime-t)}}var Jl=!1,ar=-1,Bp=5,Hp=-1;function qp(){return tc?!0:!(bt.unstable_now()-Hp<Bp)}function Jo(){if(tc=!1,Jl){var t=bt.unstable_now();Hp=t;var e=!0;try{t:{ir=!1,rr&&(rr=!1,Up(ar),ar=-1),$o=!0;var n=Pt;try{e:{for(Oa(t),ze=Ke(cn);ze!==null&&!(ze.expirationTime>t&&qp());){var l=ze.callback;if(typeof l=="function"){ze.callback=null,Pt=ze.priorityLevel;var i=l(ze.expirationTime<=t);if(t=bt.unstable_now(),typeof i=="function"){ze.callback=i,Oa(t),e=!0;break e}ze===Ke(cn)&&Ra(cn),Oa(t)}else Ra(cn);ze=Ke(cn)}if(ze!==null)e=!0;else{var r=Ke(Dn);r!==null&&nc(ec,r.startTime-t),e=!1}}break t}finally{ze=null,Pt=n,$o=!1}e=void 0}}finally{e?Il():Jl=!1}}}var Il;typeof _p=="function"?Il=function(){_p(Jo)}:typeof MessageChannel!="undefined"?(Po=new MessageChannel,Np=Po.port2,Po.port1.onmessage=Jo,Il=function(){Np.postMessage(null)}):Il=function(){Lp(Jo,0)};var Po,Np;function nc(t,e){ar=Lp(function(){t(bt.unstable_now())},e)}bt.unstable_IdlePriority=5;bt.unstable_ImmediatePriority=1;bt.unstable_LowPriority=4;bt.unstable_NormalPriority=3;bt.unstable_Profiling=null;bt.unstable_UserBlockingPriority=2;bt.unstable_cancelCallback=function(t){t.callback=null};bt.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Bp=0<t?Math.floor(1e3/t):5};bt.unstable_getCurrentPriorityLevel=function(){return Pt};bt.unstable_next=function(t){switch(Pt){case 1:case 2:case 3:var e=3;break;default:e=Pt}var n=Pt;Pt=e;try{return t()}finally{Pt=n}};bt.unstable_requestPaint=function(){tc=!0};bt.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=Pt;Pt=t;try{return e()}finally{Pt=n}};bt.unstable_scheduleCallback=function(t,e,n){var l=bt.unstable_now();switch(typeof n=="object"&&n!==null?(n=n.delay,n=typeof n=="number"&&0<n?l+n:l):n=l,t){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return i=n+i,t={id:jx++,callback:e,priorityLevel:t,startTime:n,expirationTime:i,sortIndex:-1},n>l?(t.sortIndex=n,Wo(Dn,t),Ke(cn)===null&&t===Ke(Dn)&&(rr?(Up(ar),ar=-1):rr=!0,nc(ec,n-l))):(t.sortIndex=i,Wo(cn,t),ir||$o||(ir=!0,Jl||(Jl=!0,Il()))),t};bt.unstable_shouldYield=qp;bt.unstable_wrapCallback=function(t){var e=Pt;return function(){var n=Pt;Pt=e;try{return t.apply(this,arguments)}finally{Pt=n}}}});var Gp=le((OA,Yp)=>{"use strict";Yp.exports=jp()});var Xp=le(re=>{"use strict";var Yx=lr();function Vp(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function On(){}var ie={d:{f:On,r:function(){throw Error(Vp(522))},D:On,C:On,L:On,m:On,X:On,S:On,M:On},p:0,findDOMNode:null},Gx=Symbol.for("react.portal");function Vx(t,e,n){var l=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Gx,key:l==null?null:""+l,children:t,containerInfo:e,implementation:n}}var ur=Yx.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function _a(t,e){if(t==="font")return"";if(typeof e=="string")return e==="use-credentials"?e:""}re.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ie;re.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)throw Error(Vp(299));return Vx(t,e,null,n)};re.flushSync=function(t){var e=ur.T,n=ie.p;try{if(ur.T=null,ie.p=2,t)return t()}finally{ur.T=e,ie.p=n,ie.d.f()}};re.preconnect=function(t,e){typeof t=="string"&&(e?(e=e.crossOrigin,e=typeof e=="string"?e==="use-credentials"?e:"":void 0):e=null,ie.d.C(t,e))};re.prefetchDNS=function(t){typeof t=="string"&&ie.d.D(t)};re.preinit=function(t,e){if(typeof t=="string"&&e&&typeof e.as=="string"){var n=e.as,l=_a(n,e.crossOrigin),i=typeof e.integrity=="string"?e.integrity:void 0,r=typeof e.fetchPriority=="string"?e.fetchPriority:void 0;n==="style"?ie.d.S(t,typeof e.precedence=="string"?e.precedence:void 0,{crossOrigin:l,integrity:i,fetchPriority:r}):n==="script"&&ie.d.X(t,{crossOrigin:l,integrity:i,fetchPriority:r,nonce:typeof e.nonce=="string"?e.nonce:void 0})}};re.preinitModule=function(t,e){if(typeof t=="string")if(typeof e=="object"&&e!==null){if(e.as==null||e.as==="script"){var n=_a(e.as,e.crossOrigin);ie.d.M(t,{crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0})}}else e==null&&ie.d.M(t)};re.preload=function(t,e){if(typeof t=="string"&&typeof e=="object"&&e!==null&&typeof e.as=="string"){var n=e.as,l=_a(n,e.crossOrigin);ie.d.L(t,n,{crossOrigin:l,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0,type:typeof e.type=="string"?e.type:void 0,fetchPriority:typeof e.fetchPriority=="string"?e.fetchPriority:void 0,referrerPolicy:typeof e.referrerPolicy=="string"?e.referrerPolicy:void 0,imageSrcSet:typeof e.imageSrcSet=="string"?e.imageSrcSet:void 0,imageSizes:typeof e.imageSizes=="string"?e.imageSizes:void 0,media:typeof e.media=="string"?e.media:void 0})}};re.preloadModule=function(t,e){if(typeof t=="string")if(e){var n=_a(e.as,e.crossOrigin);ie.d.m(t,{as:typeof e.as=="string"&&e.as!=="script"?e.as:void 0,crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0})}else ie.d.m(t)};re.requestFormReset=function(t){ie.d.r(t)};re.unstable_batchedUpdates=function(t,e){return t(e)};re.useFormState=function(t,e,n){return ur.H.useFormState(t,e,n)};re.useFormStatus=function(){return ur.H.useHostTransitionStatus()};re.version="19.1.0"});var Fp=le((_A,Zp)=>{"use strict";function Qp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Qp)}catch(t){console.error(t)}}Qp(),Zp.exports=Xp()});var Iy=le(eo=>{"use strict";var Bt=Gp(),dd=lr(),Xx=Fp();function D(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function gd(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Kr(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function yd(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function Kp(t){if(Kr(t)!==t)throw Error(D(188))}function Qx(t){var e=t.alternate;if(!e){if(e=Kr(t),e===null)throw Error(D(188));return e!==t?null:t}for(var n=t,l=e;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return Kp(i),t;if(r===l)return Kp(i),e;r=r.sibling}throw Error(D(188))}if(n.return!==l.return)n=i,l=r;else{for(var a=!1,u=i.child;u;){if(u===n){a=!0,n=i,l=r;break}if(u===l){a=!0,l=i,n=r;break}u=u.sibling}if(!a){for(u=r.child;u;){if(u===n){a=!0,n=r,l=i;break}if(u===l){a=!0,l=r,n=i;break}u=u.sibling}if(!a)throw Error(D(189))}}if(n.alternate!==l)throw Error(D(190))}if(n.tag!==3)throw Error(D(188));return n.stateNode.current===n?t:e}function bd(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=bd(t),e!==null)return e;t=t.sibling}return null}var gt=Object.assign,Zx=Symbol.for("react.element"),Na=Symbol.for("react.transitional.element"),gr=Symbol.for("react.portal"),li=Symbol.for("react.fragment"),xd=Symbol.for("react.strict_mode"),Nc=Symbol.for("react.profiler"),Fx=Symbol.for("react.provider"),vd=Symbol.for("react.consumer"),hn=Symbol.for("react.context"),Ms=Symbol.for("react.forward_ref"),Lc=Symbol.for("react.suspense"),Uc=Symbol.for("react.suspense_list"),Ds=Symbol.for("react.memo"),Nn=Symbol.for("react.lazy");Symbol.for("react.scope");var Bc=Symbol.for("react.activity");Symbol.for("react.legacy_hidden");Symbol.for("react.tracing_marker");var Kx=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var Ip=Symbol.iterator;function or(t){return t===null||typeof t!="object"?null:(t=Ip&&t[Ip]||t["@@iterator"],typeof t=="function"?t:null)}var Ix=Symbol.for("react.client.reference");function Hc(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Ix?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case li:return"Fragment";case Nc:return"Profiler";case xd:return"StrictMode";case Lc:return"Suspense";case Uc:return"SuspenseList";case Bc:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case gr:return"Portal";case hn:return(t.displayName||"Context")+".Provider";case vd:return(t._context.displayName||"Context")+".Consumer";case Ms:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Ds:return e=t.displayName||null,e!==null?e:Hc(t.type)||"Memo";case Nn:e=t._payload,t=t._init;try{return Hc(t(e))}catch(n){}}return null}var yr=Array.isArray,V=dd.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,rt=Xx.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,gl={pending:!1,data:null,method:null,action:null},qc=[],ii=-1;function en(t){return{current:t}}function Xt(t){0>ii||(t.current=qc[ii],qc[ii]=null,ii--)}function vt(t,e){ii++,qc[ii]=t.current,t.current=e}var We=en(null),Nr=en(null),Xn=en(null),su=en(null);function fu(t,e){switch(vt(Xn,e),vt(Nr,t),vt(We,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?ed(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=ed(e),t=By(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Xt(We),vt(We,t)}function wi(){Xt(We),Xt(Nr),Xt(Xn)}function jc(t){t.memoizedState!==null&&vt(su,t);var e=We.current,n=By(e,t.type);e!==n&&(vt(Nr,t),vt(We,n))}function mu(t){Nr.current===t&&(Xt(We),Xt(Nr)),su.current===t&&(Xt(su),Xr._currentValue=gl)}var Yc=Object.prototype.hasOwnProperty,Os=Bt.unstable_scheduleCallback,lc=Bt.unstable_cancelCallback,Jx=Bt.unstable_shouldYield,Px=Bt.unstable_requestPaint,$e=Bt.unstable_now,Wx=Bt.unstable_getCurrentPriorityLevel,Sd=Bt.unstable_ImmediatePriority,kd=Bt.unstable_UserBlockingPriority,pu=Bt.unstable_NormalPriority,$x=Bt.unstable_LowPriority,wd=Bt.unstable_IdlePriority,tv=Bt.log,ev=Bt.unstable_setDisableYieldValue,Ir=null,Se=null;function jn(t){if(typeof tv=="function"&&ev(t),Se&&typeof Se.setStrictMode=="function")try{Se.setStrictMode(Ir,t)}catch(e){}}var ke=Math.clz32?Math.clz32:iv,nv=Math.log,lv=Math.LN2;function iv(t){return t>>>=0,t===0?32:31-(nv(t)/lv|0)|0}var La=256,Ua=4194304;function pl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function ju(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var i=0,r=t.suspendedLanes,a=t.pingedLanes;t=t.warmLanes;var u=l&134217727;return u!==0?(l=u&~r,l!==0?i=pl(l):(a&=u,a!==0?i=pl(a):n||(n=u&~t,n!==0&&(i=pl(n))))):(u=l&~r,u!==0?i=pl(u):a!==0?i=pl(a):n||(n=l&~t,n!==0&&(i=pl(n)))),i===0?0:e!==0&&e!==i&&!(e&r)&&(r=i&-i,n=e&-e,r>=n||r===32&&(n&4194048)!==0)?e:i}function Jr(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function rv(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ed(){var t=La;return La<<=1,!(La&4194048)&&(La=256),t}function Td(){var t=Ua;return Ua<<=1,!(Ua&62914560)&&(Ua=4194304),t}function ic(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Pr(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function av(t,e,n,l,i,r){var a=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var u=t.entanglements,o=t.expirationTimes,c=t.hiddenUpdates;for(n=a&~n;0<n;){var s=31-ke(n),f=1<<s;u[s]=0,o[s]=-1;var p=c[s];if(p!==null)for(c[s]=null,s=0;s<p.length;s++){var m=p[s];m!==null&&(m.lane&=-536870913)}n&=~f}l!==0&&Ad(t,l,0),r!==0&&i===0&&t.tag!==0&&(t.suspendedLanes|=r&~(a&~e))}function Ad(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-ke(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function zd(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-ke(n),i=1<<l;i&e|t[l]&e&&(t[l]|=e),n&=~i}}function Rs(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function _s(t){return t&=-t,2<t?8<t?t&134217727?32:268435456:8:2}function Cd(){var t=rt.p;return t!==0?t:(t=window.event,t===void 0?32:Fy(t.type))}function uv(t,e){var n=rt.p;try{return rt.p=t,e()}finally{rt.p=n}}var el=Math.random().toString(36).slice(2),Wt="__reactFiber$"+el,me="__reactProps$"+el,Ni="__reactContainer$"+el,Gc="__reactEvents$"+el,ov="__reactListeners$"+el,cv="__reactHandles$"+el,Jp="__reactResources$"+el,Wr="__reactMarker$"+el;function Ns(t){delete t[Wt],delete t[me],delete t[Gc],delete t[ov],delete t[cv]}function ri(t){var e=t[Wt];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Ni]||n[Wt]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=id(t);t!==null;){if(n=t[Wt])return n;t=id(t)}return e}t=n,n=t.parentNode}return null}function Li(t){if(t=t[Wt]||t[Ni]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function br(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(D(33))}function di(t){var e=t[Jp];return e||(e=t[Jp]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Gt(t){t[Wr]=!0}var Md=new Set,Dd={};function zl(t,e){Ei(t,e),Ei(t+"Capture",e)}function Ei(t,e){for(Dd[t]=e,t=0;t<e.length;t++)Md.add(e[t])}var sv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Pp={},Wp={};function fv(t){return Yc.call(Wp,t)?!0:Yc.call(Pp,t)?!1:sv.test(t)?Wp[t]=!0:(Pp[t]=!0,!1)}function Pa(t,e,n){if(fv(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Ba(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function sn(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var rc,$p;function ti(t){if(rc===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);rc=e&&e[1]||"",$p=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+rc+t+$p}var ac=!1;function uc(t,e){if(!t||ac)return"";ac=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var f=function(){throw Error()};if(Object.defineProperty(f.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(f,[])}catch(m){var p=m}Reflect.construct(t,[],f)}else{try{f.call()}catch(m){p=m}t.call(f.prototype)}}else{try{throw Error()}catch(m){p=m}(f=t())&&typeof f.catch=="function"&&f.catch(function(){})}}catch(m){if(m&&p&&typeof m.stack=="string")return[m.stack,p.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),a=r[0],u=r[1];if(a&&u){var o=a.split(`
`),c=u.split(`
`);for(i=l=0;l<o.length&&!o[l].includes("DetermineComponentFrameRoot");)l++;for(;i<c.length&&!c[i].includes("DetermineComponentFrameRoot");)i++;if(l===o.length||i===c.length)for(l=o.length-1,i=c.length-1;1<=l&&0<=i&&o[l]!==c[i];)i--;for(;1<=l&&0<=i;l--,i--)if(o[l]!==c[i]){if(l!==1||i!==1)do if(l--,i--,0>i||o[l]!==c[i]){var s=`
`+o[l].replace(" at new "," at ");return t.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",t.displayName)),s}while(1<=l&&0<=i);break}}}finally{ac=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?ti(n):""}function mv(t){switch(t.tag){case 26:case 27:case 5:return ti(t.type);case 16:return ti("Lazy");case 13:return ti("Suspense");case 19:return ti("SuspenseList");case 0:case 15:return uc(t.type,!1);case 11:return uc(t.type.render,!1);case 1:return uc(t.type,!0);case 31:return ti("Activity");default:return""}}function th(t){try{var e="";do e+=mv(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Me(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Od(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function pv(t){var e=Od(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(a){l=""+a,r.call(this,a)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(a){l=""+a},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function hu(t){t._valueTracker||(t._valueTracker=pv(t))}function Rd(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Od(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function du(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}var hv=/[\n"\\]/g;function Re(t){return t.replace(hv,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Vc(t,e,n,l,i,r,a,u){t.name="",a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"?t.type=a:t.removeAttribute("type"),e!=null?a==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Me(e)):t.value!==""+Me(e)&&(t.value=""+Me(e)):a!=="submit"&&a!=="reset"||t.removeAttribute("value"),e!=null?Xc(t,a,Me(e)):n!=null?Xc(t,a,Me(n)):l!=null&&t.removeAttribute("value"),i==null&&r!=null&&(t.defaultChecked=!!r),i!=null&&(t.checked=i&&typeof i!="function"&&typeof i!="symbol"),u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"?t.name=""+Me(u):t.removeAttribute("name")}function _d(t,e,n,l,i,r,a,u){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.type=r),e!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||e!=null))return;n=n!=null?""+Me(n):"",e=e!=null?""+Me(e):n,u||e===t.value||(t.value=e),t.defaultValue=e}l=l!=null?l:i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=u?t.checked:!!l,t.defaultChecked=!!l,a!=null&&typeof a!="function"&&typeof a!="symbol"&&typeof a!="boolean"&&(t.name=a)}function Xc(t,e,n){e==="number"&&du(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function gi(t,e,n,l){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&l&&(t[n].defaultSelected=!0)}else{for(n=""+Me(n),e=null,i=0;i<t.length;i++){if(t[i].value===n){t[i].selected=!0,l&&(t[i].defaultSelected=!0);return}e!==null||t[i].disabled||(e=t[i])}e!==null&&(e.selected=!0)}}function Nd(t,e,n){if(e!=null&&(e=""+Me(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Me(n):""}function Ld(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(D(92));if(yr(l)){if(1<l.length)throw Error(D(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=Me(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function Ti(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var dv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function eh(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||dv.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function Ud(t,e,n){if(e!=null&&typeof e!="object")throw Error(D(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var i in e)l=e[i],e.hasOwnProperty(i)&&n[i]!==l&&eh(t,i,l)}else for(var r in e)e.hasOwnProperty(r)&&eh(t,r,e[r])}function Ls(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),yv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Wa(t){return yv.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Qc=null;function Us(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ai=null,yi=null;function nh(t){var e=Li(t);if(e&&(t=e.stateNode)){var n=t[me]||null;t:switch(t=e.stateNode,e.type){case"input":if(Vc(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Re(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var i=l[me]||null;if(!i)throw Error(D(90));Vc(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&Rd(l)}break t;case"textarea":Nd(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&gi(t,!!n.multiple,e,!1)}}}var oc=!1;function Bd(t,e,n){if(oc)return t(e,n);oc=!0;try{var l=t(e);return l}finally{if(oc=!1,(ai!==null||yi!==null)&&(Ju(),ai&&(e=ai,t=yi,yi=ai=null,nh(e),t)))for(e=0;e<t.length;e++)nh(t[e])}}function Lr(t,e){var n=t.stateNode;if(n===null)return null;var l=n[me]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(D(231,e,typeof n));return n}var Sn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),Zc=!1;if(Sn)try{Pl={},Object.defineProperty(Pl,"passive",{get:function(){Zc=!0}}),window.addEventListener("test",Pl,Pl),window.removeEventListener("test",Pl,Pl)}catch(t){Zc=!1}var Pl,Yn=null,Bs=null,$a=null;function Hd(){if($a)return $a;var t,e=Bs,n=e.length,l,i="value"in Yn?Yn.value:Yn.textContent,r=i.length;for(t=0;t<n&&e[t]===i[t];t++);var a=n-t;for(l=1;l<=a&&e[n-l]===i[r-l];l++);return $a=i.slice(t,1<l?1-l:void 0)}function tu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ha(){return!0}function lh(){return!1}function pe(t){function e(n,l,i,r,a){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=r,this.target=a,this.currentTarget=null;for(var u in t)t.hasOwnProperty(u)&&(n=t[u],this[u]=n?n(r):r[u]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Ha:lh,this.isPropagationStopped=lh,this}return gt(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ha)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ha)},persist:function(){},isPersistent:Ha}),e}var Cl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yu=pe(Cl),$r=gt({},Cl,{view:0,detail:0}),bv=pe($r),cc,sc,cr,Gu=gt({},$r,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hs,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==cr&&(cr&&t.type==="mousemove"?(cc=t.screenX-cr.screenX,sc=t.screenY-cr.screenY):sc=cc=0,cr=t),cc)},movementY:function(t){return"movementY"in t?t.movementY:sc}}),ih=pe(Gu),xv=gt({},Gu,{dataTransfer:0}),vv=pe(xv),Sv=gt({},$r,{relatedTarget:0}),fc=pe(Sv),kv=gt({},Cl,{animationName:0,elapsedTime:0,pseudoElement:0}),wv=pe(kv),Ev=gt({},Cl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Tv=pe(Ev),Av=gt({},Cl,{data:0}),rh=pe(Av),zv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Cv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Mv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Dv(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Mv[t])?!!e[t]:!1}function Hs(){return Dv}var Ov=gt({},$r,{key:function(t){if(t.key){var e=zv[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=tu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Cv[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hs,charCode:function(t){return t.type==="keypress"?tu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?tu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Rv=pe(Ov),_v=gt({},Gu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ah=pe(_v),Nv=gt({},$r,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hs}),Lv=pe(Nv),Uv=gt({},Cl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Bv=pe(Uv),Hv=gt({},Gu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),qv=pe(Hv),jv=gt({},Cl,{newState:0,oldState:0}),Yv=pe(jv),Gv=[9,13,27,32],qs=Sn&&"CompositionEvent"in window,vr=null;Sn&&"documentMode"in document&&(vr=document.documentMode);var Vv=Sn&&"TextEvent"in window&&!vr,qd=Sn&&(!qs||vr&&8<vr&&11>=vr),uh=" ",oh=!1;function jd(t,e){switch(t){case"keyup":return Gv.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Yd(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ui=!1;function Xv(t,e){switch(t){case"compositionend":return Yd(e);case"keypress":return e.which!==32?null:(oh=!0,uh);case"textInput":return t=e.data,t===uh&&oh?null:t;default:return null}}function Qv(t,e){if(ui)return t==="compositionend"||!qs&&jd(t,e)?(t=Hd(),$a=Bs=Yn=null,ui=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return qd&&e.locale!=="ko"?null:e.data;default:return null}}var Zv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ch(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Zv[t.type]:e==="textarea"}function Gd(t,e,n,l){ai?yi?yi.push(l):yi=[l]:ai=l,e=_u(e,"onChange"),0<e.length&&(n=new Yu("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Sr=null,Ur=null;function Fv(t){Ny(t,0)}function Vu(t){var e=br(t);if(Rd(e))return t}function sh(t,e){if(t==="change")return e}var Vd=!1;Sn&&(Sn?(ja="oninput"in document,ja||(mc=document.createElement("div"),mc.setAttribute("oninput","return;"),ja=typeof mc.oninput=="function"),qa=ja):qa=!1,Vd=qa&&(!document.documentMode||9<document.documentMode));var qa,ja,mc;function fh(){Sr&&(Sr.detachEvent("onpropertychange",Xd),Ur=Sr=null)}function Xd(t){if(t.propertyName==="value"&&Vu(Ur)){var e=[];Gd(e,Ur,t,Us(t)),Bd(Fv,e)}}function Kv(t,e,n){t==="focusin"?(fh(),Sr=e,Ur=n,Sr.attachEvent("onpropertychange",Xd)):t==="focusout"&&fh()}function Iv(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Vu(Ur)}function Jv(t,e){if(t==="click")return Vu(e)}function Pv(t,e){if(t==="input"||t==="change")return Vu(e)}function Wv(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Te=typeof Object.is=="function"?Object.is:Wv;function Br(t,e){if(Te(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!Yc.call(e,i)||!Te(t[i],e[i]))return!1}return!0}function mh(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ph(t,e){var n=mh(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=mh(n)}}function Qd(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Qd(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Zd(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=du(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch(l){n=!1}if(n)t=e.contentWindow;else break;e=du(t.document)}return e}function js(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var $v=Sn&&"documentMode"in document&&11>=document.documentMode,oi=null,Fc=null,kr=null,Kc=!1;function hh(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Kc||oi==null||oi!==du(l)||(l=oi,"selectionStart"in l&&js(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),kr&&Br(kr,l)||(kr=l,l=_u(Fc,"onSelect"),0<l.length&&(e=new Yu("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=oi)))}function ml(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var ci={animationend:ml("Animation","AnimationEnd"),animationiteration:ml("Animation","AnimationIteration"),animationstart:ml("Animation","AnimationStart"),transitionrun:ml("Transition","TransitionRun"),transitionstart:ml("Transition","TransitionStart"),transitioncancel:ml("Transition","TransitionCancel"),transitionend:ml("Transition","TransitionEnd")},pc={},Fd={};Sn&&(Fd=document.createElement("div").style,"AnimationEvent"in window||(delete ci.animationend.animation,delete ci.animationiteration.animation,delete ci.animationstart.animation),"TransitionEvent"in window||delete ci.transitionend.transition);function Ml(t){if(pc[t])return pc[t];if(!ci[t])return t;var e=ci[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Fd)return pc[t]=e[n];return t}var Kd=Ml("animationend"),Id=Ml("animationiteration"),Jd=Ml("animationstart"),tS=Ml("transitionrun"),eS=Ml("transitionstart"),nS=Ml("transitioncancel"),Pd=Ml("transitionend"),Wd=new Map,Ic="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ic.push("scrollEnd");function Xe(t,e){Wd.set(t,e),zl(e,[t])}var dh=new WeakMap;function _e(t,e){if(typeof t=="object"&&t!==null){var n=dh.get(t);return n!==void 0?n:(e={value:t,source:e,stack:th(e)},dh.set(t,e),e)}return{value:t,source:e,stack:th(e)}}var Ce=[],si=0,Ys=0;function Xu(){for(var t=si,e=Ys=si=0;e<t;){var n=Ce[e];Ce[e++]=null;var l=Ce[e];Ce[e++]=null;var i=Ce[e];Ce[e++]=null;var r=Ce[e];if(Ce[e++]=null,l!==null&&i!==null){var a=l.pending;a===null?i.next=i:(i.next=a.next,a.next=i),l.pending=i}r!==0&&$d(n,i,r)}}function Qu(t,e,n,l){Ce[si++]=t,Ce[si++]=e,Ce[si++]=n,Ce[si++]=l,Ys|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Gs(t,e,n,l){return Qu(t,e,n,l),gu(t)}function Ui(t,e){return Qu(t,null,null,e),gu(t)}function $d(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var i=!1,r=t.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(t=r.stateNode,t===null||t._visibility&1||(i=!0)),t=r,r=r.return;return t.tag===3?(r=t.stateNode,i&&e!==null&&(i=31-ke(n),t=r.hiddenUpdates,l=t[i],l===null?t[i]=[e]:l.push(e),e.lane=n|536870912),r):null}function gu(t){if(50<Rr)throw Rr=0,gs=null,Error(D(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var fi={};function lS(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ve(t,e,n,l){return new lS(t,e,n,l)}function Vs(t){return t=t.prototype,!(!t||!t.isReactComponent)}function xn(t,e){var n=t.alternate;return n===null?(n=ve(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function tg(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function eu(t,e,n,l,i,r){var a=0;if(l=t,typeof t=="function")Vs(t)&&(a=1);else if(typeof t=="string")a=lk(t,n,We.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Bc:return t=ve(31,n,e,i),t.elementType=Bc,t.lanes=r,t;case li:return yl(n.children,i,r,e);case xd:a=8,i|=24;break;case Nc:return t=ve(12,n,e,i|2),t.elementType=Nc,t.lanes=r,t;case Lc:return t=ve(13,n,e,i),t.elementType=Lc,t.lanes=r,t;case Uc:return t=ve(19,n,e,i),t.elementType=Uc,t.lanes=r,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Fx:case hn:a=10;break t;case vd:a=9;break t;case Ms:a=11;break t;case Ds:a=14;break t;case Nn:a=16,l=null;break t}a=29,n=Error(D(130,t===null?"null":typeof t,"")),l=null}return e=ve(a,n,e,i),e.elementType=t,e.type=l,e.lanes=r,e}function yl(t,e,n,l){return t=ve(7,t,l,e),t.lanes=n,t}function hc(t,e,n){return t=ve(6,t,null,e),t.lanes=n,t}function dc(t,e,n){return e=ve(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var mi=[],pi=0,yu=null,bu=0,De=[],Oe=0,bl=null,dn=1,gn="";function hl(t,e){mi[pi++]=bu,mi[pi++]=yu,yu=t,bu=e}function eg(t,e,n){De[Oe++]=dn,De[Oe++]=gn,De[Oe++]=bl,bl=t;var l=dn;t=gn;var i=32-ke(l)-1;l&=~(1<<i),n+=1;var r=32-ke(e)+i;if(30<r){var a=i-i%5;r=(l&(1<<a)-1).toString(32),l>>=a,i-=a,dn=1<<32-ke(e)+i|n<<i|l,gn=r+t}else dn=1<<r|n<<i|l,gn=t}function Xs(t){t.return!==null&&(hl(t,1),eg(t,1,0))}function Qs(t){for(;t===yu;)yu=mi[--pi],mi[pi]=null,bu=mi[--pi],mi[pi]=null;for(;t===bl;)bl=De[--Oe],De[Oe]=null,gn=De[--Oe],De[Oe]=null,dn=De[--Oe],De[Oe]=null}var ae=null,Et=null,it=!1,xl=null,Je=!1,Jc=Error(D(519));function wl(t){var e=Error(D(418,""));throw Hr(_e(e,t)),Jc}function gh(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[Wt]=t,e[me]=l,n){case"dialog":$("cancel",e),$("close",e);break;case"iframe":case"object":case"embed":$("load",e);break;case"video":case"audio":for(n=0;n<Yr.length;n++)$(Yr[n],e);break;case"source":$("error",e);break;case"img":case"image":case"link":$("error",e),$("load",e);break;case"details":$("toggle",e);break;case"input":$("invalid",e),_d(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),hu(e);break;case"select":$("invalid",e);break;case"textarea":$("invalid",e),Ld(e,l.value,l.defaultValue,l.children),hu(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||Uy(e.textContent,n)?(l.popover!=null&&($("beforetoggle",e),$("toggle",e)),l.onScroll!=null&&$("scroll",e),l.onScrollEnd!=null&&$("scrollend",e),l.onClick!=null&&(e.onclick=$u),e=!0):e=!1,e||wl(t)}function yh(t){for(ae=t.return;ae;)switch(ae.tag){case 5:case 13:Je=!1;return;case 27:case 3:Je=!0;return;default:ae=ae.return}}function sr(t){if(t!==ae)return!1;if(!it)return yh(t),it=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||ks(t.type,t.memoizedProps)),n=!n),n&&Et&&wl(t),yh(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(D(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Et=Ve(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Et=null}}else e===27?(e=Et,nl(t.type)?(t=Ts,Ts=null,Et=t):Et=e):Et=ae?Ve(t.stateNode.nextSibling):null;return!0}function ta(){Et=ae=null,it=!1}function bh(){var t=xl;return t!==null&&(fe===null?fe=t:fe.push.apply(fe,t),xl=null),t}function Hr(t){xl===null?xl=[t]:xl.push(t)}var Pc=en(null),Dl=null,yn=null;function Un(t,e,n){vt(Pc,e._currentValue),e._currentValue=n}function vn(t){t._currentValue=Pc.current,Xt(Pc)}function Wc(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function $c(t,e,n,l){var i=t.child;for(i!==null&&(i.return=t);i!==null;){var r=i.dependencies;if(r!==null){var a=i.child;r=r.firstContext;t:for(;r!==null;){var u=r;r=i;for(var o=0;o<e.length;o++)if(u.context===e[o]){r.lanes|=n,u=r.alternate,u!==null&&(u.lanes|=n),Wc(r.return,n,t),l||(a=null);break t}r=u.next}}else if(i.tag===18){if(a=i.return,a===null)throw Error(D(341));a.lanes|=n,r=a.alternate,r!==null&&(r.lanes|=n),Wc(a,n,t),a=null}else a=i.child;if(a!==null)a.return=i;else for(a=i;a!==null;){if(a===t){a=null;break}if(i=a.sibling,i!==null){i.return=a.return,a=i;break}a=a.return}i=a}}function ea(t,e,n,l){t=null;for(var i=e,r=!1;i!==null;){if(!r){if(i.flags&524288)r=!0;else if(i.flags&262144)break}if(i.tag===10){var a=i.alternate;if(a===null)throw Error(D(387));if(a=a.memoizedProps,a!==null){var u=i.type;Te(i.pendingProps.value,a.value)||(t!==null?t.push(u):t=[u])}}else if(i===su.current){if(a=i.alternate,a===null)throw Error(D(387));a.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(t!==null?t.push(Xr):t=[Xr])}i=i.return}t!==null&&$c(e,t,n,l),e.flags|=262144}function xu(t){for(t=t.firstContext;t!==null;){if(!Te(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function El(t){Dl=t,yn=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function $t(t){return ng(Dl,t)}function Ya(t,e){return Dl===null&&El(t),ng(t,e)}function ng(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},yn===null){if(t===null)throw Error(D(308));yn=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else yn=yn.next=e;return n}var iS=typeof AbortController!="undefined"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},rS=Bt.unstable_scheduleCallback,aS=Bt.unstable_NormalPriority,Lt={$$typeof:hn,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Zs(){return{controller:new iS,data:new Map,refCount:0}}function na(t){t.refCount--,t.refCount===0&&rS(aS,function(){t.controller.abort()})}var wr=null,ts=0,Ai=0,bi=null;function uS(t,e){if(wr===null){var n=wr=[];ts=0,Ai=gf(),bi={status:"pending",value:void 0,then:function(l){n.push(l)}}}return ts++,e.then(xh,xh),e}function xh(){if(--ts===0&&wr!==null){bi!==null&&(bi.status="fulfilled");var t=wr;wr=null,Ai=0,bi=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function oS(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var i=0;i<n.length;i++)(0,n[i])(e)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var vh=V.S;V.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&uS(t,e),vh!==null&&vh(t,e)};var vl=en(null);function Fs(){var t=vl.current;return t!==null?t:dt.pooledCache}function nu(t,e){e===null?vt(vl,vl.current):vt(vl,e.pool)}function lg(){var t=Fs();return t===null?null:{parent:Lt._currentValue,pool:t}}var la=Error(D(460)),ig=Error(D(474)),Zu=Error(D(542)),es={then:function(){}};function Sh(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ga(){}function rg(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Ga,Ga),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,wh(t),t;default:if(typeof e.status=="string")e.then(Ga,Ga);else{if(t=dt,t!==null&&100<t.shellSuspendCounter)throw Error(D(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var i=e;i.status="fulfilled",i.value=l}},function(l){if(e.status==="pending"){var i=e;i.status="rejected",i.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,wh(t),t}throw Er=e,la}}var Er=null;function kh(){if(Er===null)throw Error(D(459));var t=Er;return Er=null,t}function wh(t){if(t===la||t===Zu)throw Error(D(483))}var Ln=!1;function Ks(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ns(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Qn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Zn(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,ut&2){var i=l.pending;return i===null?e.next=e:(e.next=i.next,i.next=e),l.pending=e,e=gu(t),$d(t,null,n),e}return Qu(t,l,e,n),gu(t)}function Tr(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,zd(t,n)}}function gc(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var a={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?i=r=a:r=r.next=a,n=n.next}while(n!==null);r===null?i=r=e:r=r.next=e}else i=r=e;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var ls=!1;function Ar(){if(ls){var t=bi;if(t!==null)throw t}}function zr(t,e,n,l){ls=!1;var i=t.updateQueue;Ln=!1;var r=i.firstBaseUpdate,a=i.lastBaseUpdate,u=i.shared.pending;if(u!==null){i.shared.pending=null;var o=u,c=o.next;o.next=null,a===null?r=c:a.next=c,a=o;var s=t.alternate;s!==null&&(s=s.updateQueue,u=s.lastBaseUpdate,u!==a&&(u===null?s.firstBaseUpdate=c:u.next=c,s.lastBaseUpdate=o))}if(r!==null){var f=i.baseState;a=0,s=c=o=null,u=r;do{var p=u.lane&-536870913,m=p!==u.lane;if(m?(lt&p)===p:(l&p)===p){p!==0&&p===Ai&&(ls=!0),s!==null&&(s=s.next={lane:0,tag:u.tag,payload:u.payload,callback:null,next:null});t:{var y=t,v=u;p=e;var E=n;switch(v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(E,f,p);break t}f=y;break t;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,p=typeof y=="function"?y.call(E,f,p):y,p==null)break t;f=gt({},f,p);break t;case 2:Ln=!0}}p=u.callback,p!==null&&(t.flags|=64,m&&(t.flags|=8192),m=i.callbacks,m===null?i.callbacks=[p]:m.push(p))}else m={lane:p,tag:u.tag,payload:u.payload,callback:u.callback,next:null},s===null?(c=s=m,o=f):s=s.next=m,a|=p;if(u=u.next,u===null){if(u=i.shared.pending,u===null)break;m=u,u=m.next,m.next=null,i.lastBaseUpdate=m,i.shared.pending=null}}while(!0);s===null&&(o=f),i.baseState=o,i.firstBaseUpdate=c,i.lastBaseUpdate=s,r===null&&(i.shared.lanes=0),tl|=a,t.lanes=a,t.memoizedState=f}}function ag(t,e){if(typeof t!="function")throw Error(D(191,t));t.call(e)}function ug(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)ag(n[t],e)}var zi=en(null),vu=en(0);function Eh(t,e){t=En,vt(vu,t),vt(zi,e),En=t|e.baseLanes}function is(){vt(vu,En),vt(zi,zi.current)}function Is(){En=vu.current,Xt(zi),Xt(vu)}var Wn=0,I=null,ft=null,_t=null,Su=!1,xi=!1,Tl=!1,ku=0,qr=0,vi=null,cS=0;function Ct(){throw Error(D(321))}function Js(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Te(t[n],e[n]))return!1;return!0}function Ps(t,e,n,l,i,r){return Wn=r,I=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,V.H=t===null||t.memoizedState===null?Hg:qg,Tl=!1,r=n(l,i),Tl=!1,xi&&(r=cg(e,n,l,i)),og(t),r}function og(t){V.H=wu;var e=ft!==null&&ft.next!==null;if(Wn=0,_t=ft=I=null,Su=!1,qr=0,vi=null,e)throw Error(D(300));t===null||Vt||(t=t.dependencies,t!==null&&xu(t)&&(Vt=!0))}function cg(t,e,n,l){I=t;var i=0;do{if(xi&&(vi=null),qr=0,xi=!1,25<=i)throw Error(D(301));if(i+=1,_t=ft=null,t.updateQueue!=null){var r=t.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}V.H=gS,r=e(n,l)}while(xi);return r}function sS(){var t=V.H,e=t.useState()[0];return e=typeof e.then=="function"?ia(e):e,t=t.useState()[0],(ft!==null?ft.memoizedState:null)!==t&&(I.flags|=1024),e}function Ws(){var t=ku!==0;return ku=0,t}function $s(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function tf(t){if(Su){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Su=!1}Wn=0,_t=ft=I=null,xi=!1,qr=ku=0,vi=null}function ce(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return _t===null?I.memoizedState=_t=t:_t=_t.next=t,_t}function Nt(){if(ft===null){var t=I.alternate;t=t!==null?t.memoizedState:null}else t=ft.next;var e=_t===null?I.memoizedState:_t.next;if(e!==null)_t=e,ft=t;else{if(t===null)throw I.alternate===null?Error(D(467)):Error(D(310));ft=t,t={memoizedState:ft.memoizedState,baseState:ft.baseState,baseQueue:ft.baseQueue,queue:ft.queue,next:null},_t===null?I.memoizedState=_t=t:_t=_t.next=t}return _t}function ef(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ia(t){var e=qr;return qr+=1,vi===null&&(vi=[]),t=rg(vi,t,e),e=I,(_t===null?e.memoizedState:_t.next)===null&&(e=e.alternate,V.H=e===null||e.memoizedState===null?Hg:qg),t}function Fu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return ia(t);if(t.$$typeof===hn)return $t(t)}throw Error(D(438,String(t)))}function nf(t){var e=null,n=I.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=I.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(i){return i.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=ef(),I.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=Kx;return e.index++,n}function kn(t,e){return typeof e=="function"?e(t):e}function lu(t){var e=Nt();return lf(e,ft,t)}function lf(t,e,n){var l=t.queue;if(l===null)throw Error(D(311));l.lastRenderedReducer=n;var i=t.baseQueue,r=l.pending;if(r!==null){if(i!==null){var a=i.next;i.next=r.next,r.next=a}e.baseQueue=i=r,l.pending=null}if(r=t.baseState,i===null)t.memoizedState=r;else{e=i.next;var u=a=null,o=null,c=e,s=!1;do{var f=c.lane&-536870913;if(f!==c.lane?(lt&f)===f:(Wn&f)===f){var p=c.revertLane;if(p===0)o!==null&&(o=o.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Ai&&(s=!0);else if((Wn&p)===p){c=c.next,p===Ai&&(s=!0);continue}else f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},o===null?(u=o=f,a=r):o=o.next=f,I.lanes|=p,tl|=p;f=c.action,Tl&&n(r,f),r=c.hasEagerState?c.eagerState:n(r,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},o===null?(u=o=p,a=r):o=o.next=p,I.lanes|=f,tl|=f;c=c.next}while(c!==null&&c!==e);if(o===null?a=r:o.next=u,!Te(r,t.memoizedState)&&(Vt=!0,s&&(n=bi,n!==null)))throw n;t.memoizedState=r,t.baseState=a,t.baseQueue=o,l.lastRenderedState=r}return i===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function yc(t){var e=Nt(),n=e.queue;if(n===null)throw Error(D(311));n.lastRenderedReducer=t;var l=n.dispatch,i=n.pending,r=e.memoizedState;if(i!==null){n.pending=null;var a=i=i.next;do r=t(r,a.action),a=a.next;while(a!==i);Te(r,e.memoizedState)||(Vt=!0),e.memoizedState=r,e.baseQueue===null&&(e.baseState=r),n.lastRenderedState=r}return[r,l]}function sg(t,e,n){var l=I,i=Nt(),r=it;if(r){if(n===void 0)throw Error(D(407));n=n()}else n=e();var a=!Te((ft||i).memoizedState,n);a&&(i.memoizedState=n,Vt=!0),i=i.queue;var u=pg.bind(null,l,i,t);if(ra(2048,8,u,[t]),i.getSnapshot!==e||a||_t!==null&&_t.memoizedState.tag&1){if(l.flags|=2048,Ci(9,Ku(),mg.bind(null,l,i,n,e),null),dt===null)throw Error(D(349));r||Wn&124||fg(l,e,n)}return n}function fg(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=I.updateQueue,e===null?(e=ef(),I.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function mg(t,e,n,l){e.value=n,e.getSnapshot=l,hg(e)&&dg(t)}function pg(t,e,n){return n(function(){hg(e)&&dg(t)})}function hg(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Te(t,n)}catch(l){return!0}}function dg(t){var e=Ui(t,2);e!==null&&Ee(e,t,2)}function rs(t){var e=ce();if(typeof t=="function"){var n=t;if(t=n(),Tl){jn(!0);try{n()}finally{jn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:t},e}function gg(t,e,n,l){return t.baseState=n,lf(t,ft,typeof l=="function"?l:kn)}function fS(t,e,n,l,i){if(Iu(t))throw Error(D(485));if(t=e.action,t!==null){var r={payload:i,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(a){r.listeners.push(a)}};V.T!==null?n(!0):r.isTransition=!1,l(r),n=e.pending,n===null?(r.next=e.pending=r,yg(e,r)):(r.next=n.next,e.pending=n.next=r)}}function yg(t,e){var n=e.action,l=e.payload,i=t.state;if(e.isTransition){var r=V.T,a={};V.T=a;try{var u=n(i,l),o=V.S;o!==null&&o(a,u),Th(t,e,u)}catch(c){as(t,e,c)}finally{V.T=r}}else try{r=n(i,l),Th(t,e,r)}catch(c){as(t,e,c)}}function Th(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Ah(t,e,l)},function(l){return as(t,e,l)}):Ah(t,e,n)}function Ah(t,e,n){e.status="fulfilled",e.value=n,bg(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,yg(t,n)))}function as(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,bg(e),e=e.next;while(e!==l)}t.action=null}function bg(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function xg(t,e){return e}function zh(t,e){if(it){var n=dt.formState;if(n!==null){t:{var l=I;if(it){if(Et){e:{for(var i=Et,r=Je;i.nodeType!==8;){if(!r){i=null;break e}if(i=Ve(i.nextSibling),i===null){i=null;break e}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){Et=Ve(i.nextSibling),l=i.data==="F!";break t}}wl(l)}l=!1}l&&(e=n[0])}}return n=ce(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:xg,lastRenderedState:e},n.queue=l,n=Lg.bind(null,I,l),l.dispatch=n,l=rs(!1),r=of.bind(null,I,!1,l.queue),l=ce(),i={state:e,dispatch:null,action:t,pending:null},l.queue=i,n=fS.bind(null,I,i,r,n),i.dispatch=n,l.memoizedState=t,[e,n,!1]}function Ch(t){var e=Nt();return vg(e,ft,t)}function vg(t,e,n){if(e=lf(t,e,xg)[0],t=lu(kn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=ia(e)}catch(a){throw a===la?Zu:a}else l=e;e=Nt();var i=e.queue,r=i.dispatch;return n!==e.memoizedState&&(I.flags|=2048,Ci(9,Ku(),mS.bind(null,i,n),null)),[l,r,t]}function mS(t,e){t.action=e}function Mh(t){var e=Nt(),n=ft;if(n!==null)return vg(e,n,t);Nt(),e=e.memoizedState,n=Nt();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function Ci(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=I.updateQueue,e===null&&(e=ef(),I.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Ku(){return{destroy:void 0,resource:void 0}}function Sg(){return Nt().memoizedState}function iu(t,e,n,l){var i=ce();l=l===void 0?null:l,I.flags|=t,i.memoizedState=Ci(1|e,Ku(),n,l)}function ra(t,e,n,l){var i=Nt();l=l===void 0?null:l;var r=i.memoizedState.inst;ft!==null&&l!==null&&Js(l,ft.memoizedState.deps)?i.memoizedState=Ci(e,r,n,l):(I.flags|=t,i.memoizedState=Ci(1|e,r,n,l))}function Dh(t,e){iu(8390656,8,t,e)}function kg(t,e){ra(2048,8,t,e)}function wg(t,e){return ra(4,2,t,e)}function Eg(t,e){return ra(4,4,t,e)}function Tg(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Ag(t,e,n){n=n!=null?n.concat([t]):null,ra(4,4,Tg.bind(null,e,t),n)}function rf(){}function zg(t,e){var n=Nt();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Js(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function Cg(t,e){var n=Nt();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Js(e,l[1]))return l[0];if(l=t(),Tl){jn(!0);try{t()}finally{jn(!1)}}return n.memoizedState=[l,e],l}function af(t,e,n){return n===void 0||Wn&1073741824?t.memoizedState=e:(t.memoizedState=n,t=by(),I.lanes|=t,tl|=t,n)}function Mg(t,e,n,l){return Te(n,e)?n:zi.current!==null?(t=af(t,n,l),Te(t,e)||(Vt=!0),t):Wn&42?(t=by(),I.lanes|=t,tl|=t,e):(Vt=!0,t.memoizedState=n)}function Dg(t,e,n,l,i){var r=rt.p;rt.p=r!==0&&8>r?r:8;var a=V.T,u={};V.T=u,of(t,!1,e,n);try{var o=i(),c=V.S;if(c!==null&&c(u,o),o!==null&&typeof o=="object"&&typeof o.then=="function"){var s=oS(o,l);Cr(t,e,s,we(t))}else Cr(t,e,l,we(t))}catch(f){Cr(t,e,{then:function(){},status:"rejected",reason:f},we())}finally{rt.p=r,V.T=a}}function pS(){}function us(t,e,n,l){if(t.tag!==5)throw Error(D(476));var i=Og(t).queue;Dg(t,i,e,gl,n===null?pS:function(){return Rg(t),n(l)})}function Og(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:gl,baseState:gl,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:gl},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:kn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Rg(t){var e=Og(t).next.queue;Cr(t,e,{},we())}function uf(){return $t(Xr)}function _g(){return Nt().memoizedState}function Ng(){return Nt().memoizedState}function hS(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=we();t=Qn(n);var l=Zn(e,t,n);l!==null&&(Ee(l,e,n),Tr(l,e,n)),e={cache:Zs()},t.payload=e;return}e=e.return}}function dS(t,e,n){var l=we();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Iu(t)?Ug(e,n):(n=Gs(t,e,n,l),n!==null&&(Ee(n,t,l),Bg(n,e,l)))}function Lg(t,e,n){var l=we();Cr(t,e,n,l)}function Cr(t,e,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Iu(t))Ug(e,i);else{var r=t.alternate;if(t.lanes===0&&(r===null||r.lanes===0)&&(r=e.lastRenderedReducer,r!==null))try{var a=e.lastRenderedState,u=r(a,n);if(i.hasEagerState=!0,i.eagerState=u,Te(u,a))return Qu(t,e,i,0),dt===null&&Xu(),!1}catch(o){}finally{}if(n=Gs(t,e,i,l),n!==null)return Ee(n,t,l),Bg(n,e,l),!0}return!1}function of(t,e,n,l){if(l={lane:2,revertLane:gf(),action:l,hasEagerState:!1,eagerState:null,next:null},Iu(t)){if(e)throw Error(D(479))}else e=Gs(t,n,l,2),e!==null&&Ee(e,t,2)}function Iu(t){var e=t.alternate;return t===I||e!==null&&e===I}function Ug(t,e){xi=Su=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Bg(t,e,n){if(n&4194048){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,zd(t,n)}}var wu={readContext:$t,use:Fu,useCallback:Ct,useContext:Ct,useEffect:Ct,useImperativeHandle:Ct,useLayoutEffect:Ct,useInsertionEffect:Ct,useMemo:Ct,useReducer:Ct,useRef:Ct,useState:Ct,useDebugValue:Ct,useDeferredValue:Ct,useTransition:Ct,useSyncExternalStore:Ct,useId:Ct,useHostTransitionStatus:Ct,useFormState:Ct,useActionState:Ct,useOptimistic:Ct,useMemoCache:Ct,useCacheRefresh:Ct},Hg={readContext:$t,use:Fu,useCallback:function(t,e){return ce().memoizedState=[t,e===void 0?null:e],t},useContext:$t,useEffect:Dh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,iu(4194308,4,Tg.bind(null,e,t),n)},useLayoutEffect:function(t,e){return iu(4194308,4,t,e)},useInsertionEffect:function(t,e){iu(4,2,t,e)},useMemo:function(t,e){var n=ce();e=e===void 0?null:e;var l=t();if(Tl){jn(!0);try{t()}finally{jn(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=ce();if(n!==void 0){var i=n(e);if(Tl){jn(!0);try{n(e)}finally{jn(!1)}}}else i=e;return l.memoizedState=l.baseState=i,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:i},l.queue=t,t=t.dispatch=dS.bind(null,I,t),[l.memoizedState,t]},useRef:function(t){var e=ce();return t={current:t},e.memoizedState=t},useState:function(t){t=rs(t);var e=t.queue,n=Lg.bind(null,I,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:rf,useDeferredValue:function(t,e){var n=ce();return af(n,t,e)},useTransition:function(){var t=rs(!1);return t=Dg.bind(null,I,t.queue,!0,!1),ce().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=I,i=ce();if(it){if(n===void 0)throw Error(D(407));n=n()}else{if(n=e(),dt===null)throw Error(D(349));lt&124||fg(l,e,n)}i.memoizedState=n;var r={value:n,getSnapshot:e};return i.queue=r,Dh(pg.bind(null,l,r,t),[t]),l.flags|=2048,Ci(9,Ku(),mg.bind(null,l,r,n,e),null),n},useId:function(){var t=ce(),e=dt.identifierPrefix;if(it){var n=gn,l=dn;n=(l&~(1<<32-ke(l)-1)).toString(32)+n,e="\xAB"+e+"R"+n,n=ku++,0<n&&(e+="H"+n.toString(32)),e+="\xBB"}else n=cS++,e="\xAB"+e+"r"+n.toString(32)+"\xBB";return t.memoizedState=e},useHostTransitionStatus:uf,useFormState:zh,useActionState:zh,useOptimistic:function(t){var e=ce();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=of.bind(null,I,!0,n),n.dispatch=e,[t,e]},useMemoCache:nf,useCacheRefresh:function(){return ce().memoizedState=hS.bind(null,I)}},qg={readContext:$t,use:Fu,useCallback:zg,useContext:$t,useEffect:kg,useImperativeHandle:Ag,useInsertionEffect:wg,useLayoutEffect:Eg,useMemo:Cg,useReducer:lu,useRef:Sg,useState:function(){return lu(kn)},useDebugValue:rf,useDeferredValue:function(t,e){var n=Nt();return Mg(n,ft.memoizedState,t,e)},useTransition:function(){var t=lu(kn)[0],e=Nt().memoizedState;return[typeof t=="boolean"?t:ia(t),e]},useSyncExternalStore:sg,useId:_g,useHostTransitionStatus:uf,useFormState:Ch,useActionState:Ch,useOptimistic:function(t,e){var n=Nt();return gg(n,ft,t,e)},useMemoCache:nf,useCacheRefresh:Ng},gS={readContext:$t,use:Fu,useCallback:zg,useContext:$t,useEffect:kg,useImperativeHandle:Ag,useInsertionEffect:wg,useLayoutEffect:Eg,useMemo:Cg,useReducer:yc,useRef:Sg,useState:function(){return yc(kn)},useDebugValue:rf,useDeferredValue:function(t,e){var n=Nt();return ft===null?af(n,t,e):Mg(n,ft.memoizedState,t,e)},useTransition:function(){var t=yc(kn)[0],e=Nt().memoizedState;return[typeof t=="boolean"?t:ia(t),e]},useSyncExternalStore:sg,useId:_g,useHostTransitionStatus:uf,useFormState:Mh,useActionState:Mh,useOptimistic:function(t,e){var n=Nt();return ft!==null?gg(n,ft,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:nf,useCacheRefresh:Ng},Si=null,jr=0;function Va(t){var e=jr;return jr+=1,Si===null&&(Si=[]),rg(Si,t,e)}function fr(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Xa(t,e){throw e.$$typeof===Zx?Error(D(525)):(t=Object.prototype.toString.call(e),Error(D(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Oh(t){var e=t._init;return e(t._payload)}function jg(t){function e(h,d){if(t){var g=h.deletions;g===null?(h.deletions=[d],h.flags|=16):g.push(d)}}function n(h,d){if(!t)return null;for(;d!==null;)e(h,d),d=d.sibling;return null}function l(h){for(var d=new Map;h!==null;)h.key!==null?d.set(h.key,h):d.set(h.index,h),h=h.sibling;return d}function i(h,d){return h=xn(h,d),h.index=0,h.sibling=null,h}function r(h,d,g){return h.index=g,t?(g=h.alternate,g!==null?(g=g.index,g<d?(h.flags|=67108866,d):g):(h.flags|=67108866,d)):(h.flags|=1048576,d)}function a(h){return t&&h.alternate===null&&(h.flags|=67108866),h}function u(h,d,g,k){return d===null||d.tag!==6?(d=hc(g,h.mode,k),d.return=h,d):(d=i(d,g),d.return=h,d)}function o(h,d,g,k){var C=g.type;return C===li?s(h,d,g.props.children,k,g.key):d!==null&&(d.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Nn&&Oh(C)===d.type)?(d=i(d,g.props),fr(d,g),d.return=h,d):(d=eu(g.type,g.key,g.props,null,h.mode,k),fr(d,g),d.return=h,d)}function c(h,d,g,k){return d===null||d.tag!==4||d.stateNode.containerInfo!==g.containerInfo||d.stateNode.implementation!==g.implementation?(d=dc(g,h.mode,k),d.return=h,d):(d=i(d,g.children||[]),d.return=h,d)}function s(h,d,g,k,C){return d===null||d.tag!==7?(d=yl(g,h.mode,k,C),d.return=h,d):(d=i(d,g),d.return=h,d)}function f(h,d,g){if(typeof d=="string"&&d!==""||typeof d=="number"||typeof d=="bigint")return d=hc(""+d,h.mode,g),d.return=h,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Na:return g=eu(d.type,d.key,d.props,null,h.mode,g),fr(g,d),g.return=h,g;case gr:return d=dc(d,h.mode,g),d.return=h,d;case Nn:var k=d._init;return d=k(d._payload),f(h,d,g)}if(yr(d)||or(d))return d=yl(d,h.mode,g,null),d.return=h,d;if(typeof d.then=="function")return f(h,Va(d),g);if(d.$$typeof===hn)return f(h,Ya(h,d),g);Xa(h,d)}return null}function p(h,d,g,k){var C=d!==null?d.key:null;if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return C!==null?null:u(h,d,""+g,k);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Na:return g.key===C?o(h,d,g,k):null;case gr:return g.key===C?c(h,d,g,k):null;case Nn:return C=g._init,g=C(g._payload),p(h,d,g,k)}if(yr(g)||or(g))return C!==null?null:s(h,d,g,k,null);if(typeof g.then=="function")return p(h,d,Va(g),k);if(g.$$typeof===hn)return p(h,d,Ya(h,g),k);Xa(h,g)}return null}function m(h,d,g,k,C){if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return h=h.get(g)||null,u(d,h,""+k,C);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case Na:return h=h.get(k.key===null?g:k.key)||null,o(d,h,k,C);case gr:return h=h.get(k.key===null?g:k.key)||null,c(d,h,k,C);case Nn:var w=k._init;return k=w(k._payload),m(h,d,g,k,C)}if(yr(k)||or(k))return h=h.get(g)||null,s(d,h,k,C,null);if(typeof k.then=="function")return m(h,d,g,Va(k),C);if(k.$$typeof===hn)return m(h,d,g,Ya(d,k),C);Xa(d,k)}return null}function y(h,d,g,k){for(var C=null,w=null,A=d,T=d=0,N=null;A!==null&&T<g.length;T++){A.index>T?(N=A,A=null):N=A.sibling;var S=p(h,A,g[T],k);if(S===null){A===null&&(A=N);break}t&&A&&S.alternate===null&&e(h,A),d=r(S,d,T),w===null?C=S:w.sibling=S,w=S,A=N}if(T===g.length)return n(h,A),it&&hl(h,T),C;if(A===null){for(;T<g.length;T++)A=f(h,g[T],k),A!==null&&(d=r(A,d,T),w===null?C=A:w.sibling=A,w=A);return it&&hl(h,T),C}for(A=l(A);T<g.length;T++)N=m(A,h,T,g[T],k),N!==null&&(t&&N.alternate!==null&&A.delete(N.key===null?T:N.key),d=r(N,d,T),w===null?C=N:w.sibling=N,w=N);return t&&A.forEach(function(F){return e(h,F)}),it&&hl(h,T),C}function v(h,d,g,k){if(g==null)throw Error(D(151));for(var C=null,w=null,A=d,T=d=0,N=null,S=g.next();A!==null&&!S.done;T++,S=g.next()){A.index>T?(N=A,A=null):N=A.sibling;var F=p(h,A,S.value,k);if(F===null){A===null&&(A=N);break}t&&A&&F.alternate===null&&e(h,A),d=r(F,d,T),w===null?C=F:w.sibling=F,w=F,A=N}if(S.done)return n(h,A),it&&hl(h,T),C;if(A===null){for(;!S.done;T++,S=g.next())S=f(h,S.value,k),S!==null&&(d=r(S,d,T),w===null?C=S:w.sibling=S,w=S);return it&&hl(h,T),C}for(A=l(A);!S.done;T++,S=g.next())S=m(A,h,T,S.value,k),S!==null&&(t&&S.alternate!==null&&A.delete(S.key===null?T:S.key),d=r(S,d,T),w===null?C=S:w.sibling=S,w=S);return t&&A.forEach(function(Q){return e(h,Q)}),it&&hl(h,T),C}function E(h,d,g,k){if(typeof g=="object"&&g!==null&&g.type===li&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Na:t:{for(var C=g.key;d!==null;){if(d.key===C){if(C=g.type,C===li){if(d.tag===7){n(h,d.sibling),k=i(d,g.props.children),k.return=h,h=k;break t}}else if(d.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Nn&&Oh(C)===d.type){n(h,d.sibling),k=i(d,g.props),fr(k,g),k.return=h,h=k;break t}n(h,d);break}else e(h,d);d=d.sibling}g.type===li?(k=yl(g.props.children,h.mode,k,g.key),k.return=h,h=k):(k=eu(g.type,g.key,g.props,null,h.mode,k),fr(k,g),k.return=h,h=k)}return a(h);case gr:t:{for(C=g.key;d!==null;){if(d.key===C)if(d.tag===4&&d.stateNode.containerInfo===g.containerInfo&&d.stateNode.implementation===g.implementation){n(h,d.sibling),k=i(d,g.children||[]),k.return=h,h=k;break t}else{n(h,d);break}else e(h,d);d=d.sibling}k=dc(g,h.mode,k),k.return=h,h=k}return a(h);case Nn:return C=g._init,g=C(g._payload),E(h,d,g,k)}if(yr(g))return y(h,d,g,k);if(or(g)){if(C=or(g),typeof C!="function")throw Error(D(150));return g=C.call(g),v(h,d,g,k)}if(typeof g.then=="function")return E(h,d,Va(g),k);if(g.$$typeof===hn)return E(h,d,Ya(h,g),k);Xa(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint"?(g=""+g,d!==null&&d.tag===6?(n(h,d.sibling),k=i(d,g),k.return=h,h=k):(n(h,d),k=hc(g,h.mode,k),k.return=h,h=k),a(h)):n(h,d)}return function(h,d,g,k){try{jr=0;var C=E(h,d,g,k);return Si=null,C}catch(A){if(A===la||A===Zu)throw A;var w=ve(29,A,null,h.mode);return w.lanes=k,w.return=h,w}finally{}}}var Mi=jg(!0),Yg=jg(!1),Le=en(null),tn=null;function Bn(t){var e=t.alternate;vt(Ut,Ut.current&1),vt(Le,t),tn===null&&(e===null||zi.current!==null||e.memoizedState!==null)&&(tn=t)}function Gg(t){if(t.tag===22){if(vt(Ut,Ut.current),vt(Le,t),tn===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(tn=t)}}else Hn(t)}function Hn(){vt(Ut,Ut.current),vt(Le,Le.current)}function bn(t){Xt(Le),tn===t&&(tn=null),Xt(Ut)}var Ut=en(0);function Eu(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Es(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function bc(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:gt({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var os={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=we(),i=Qn(l);i.payload=e,n!=null&&(i.callback=n),e=Zn(t,i,l),e!==null&&(Ee(e,t,l),Tr(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=we(),i=Qn(l);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=Zn(t,i,l),e!==null&&(Ee(e,t,l),Tr(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=we(),l=Qn(n);l.tag=2,e!=null&&(l.callback=e),e=Zn(t,l,n),e!==null&&(Ee(e,t,n),Tr(e,t,n))}};function Rh(t,e,n,l,i,r,a){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,r,a):e.prototype&&e.prototype.isPureReactComponent?!Br(n,l)||!Br(i,r):!0}function _h(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&os.enqueueReplaceState(e,e.state,null)}function Al(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=gt({},n));for(var i in t)n[i]===void 0&&(n[i]=t[i])}return n}var Tu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Vg(t){Tu(t)}function Xg(t){console.error(t)}function Qg(t){Tu(t)}function Au(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Nh(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function cs(t,e,n){return n=Qn(n),n.tag=3,n.payload={element:null},n.callback=function(){Au(t,e)},n}function Zg(t){return t=Qn(t),t.tag=3,t}function Fg(t,e,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var r=l.value;t.payload=function(){return i(r)},t.callback=function(){Nh(e,n,l)}}var a=n.stateNode;a!==null&&typeof a.componentDidCatch=="function"&&(t.callback=function(){Nh(e,n,l),typeof i!="function"&&(Fn===null?Fn=new Set([this]):Fn.add(this));var u=l.stack;this.componentDidCatch(l.value,{componentStack:u!==null?u:""})})}function yS(t,e,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&ea(e,n,i,!0),n=Le.current,n!==null){switch(n.tag){case 13:return tn===null?ys():n.alternate===null&&Tt===0&&(Tt=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===es?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),Mc(t,l,i)),!1;case 22:return n.flags|=65536,l===es?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),Mc(t,l,i)),!1}throw Error(D(435,n.tag))}return Mc(t,l,i),ys(),!1}if(it)return e=Le.current,e!==null?(!(e.flags&65536)&&(e.flags|=256),e.flags|=65536,e.lanes=i,l!==Jc&&(t=Error(D(422),{cause:l}),Hr(_e(t,n)))):(l!==Jc&&(e=Error(D(423),{cause:l}),Hr(_e(e,n))),t=t.current.alternate,t.flags|=65536,i&=-i,t.lanes|=i,l=_e(l,n),i=cs(t.stateNode,l,i),gc(t,i),Tt!==4&&(Tt=2)),!1;var r=Error(D(520),{cause:l});if(r=_e(r,n),Or===null?Or=[r]:Or.push(r),Tt!==4&&(Tt=2),e===null)return!0;l=_e(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=i&-i,n.lanes|=t,t=cs(n.stateNode,l,t),gc(n,t),!1;case 1:if(e=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Fn===null||!Fn.has(r))))return n.flags|=65536,i&=-i,n.lanes|=i,i=Zg(i),Fg(i,t,n,l),gc(n,i),!1}n=n.return}while(n!==null);return!1}var Kg=Error(D(461)),Vt=!1;function Ft(t,e,n,l){e.child=t===null?Yg(e,null,n,l):Mi(e,t.child,n,l)}function Lh(t,e,n,l,i){n=n.render;var r=e.ref;if("ref"in l){var a={};for(var u in l)u!=="ref"&&(a[u]=l[u])}else a=l;return El(e),l=Ps(t,e,n,a,r,i),u=Ws(),t!==null&&!Vt?($s(t,e,i),wn(t,e,i)):(it&&u&&Xs(e),e.flags|=1,Ft(t,e,l,i),e.child)}function Uh(t,e,n,l,i){if(t===null){var r=n.type;return typeof r=="function"&&!Vs(r)&&r.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=r,Ig(t,e,r,l,i)):(t=eu(n.type,null,l,e,e.mode,i),t.ref=e.ref,t.return=e,e.child=t)}if(r=t.child,!cf(t,i)){var a=r.memoizedProps;if(n=n.compare,n=n!==null?n:Br,n(a,l)&&t.ref===e.ref)return wn(t,e,i)}return e.flags|=1,t=xn(r,l),t.ref=e.ref,t.return=e,e.child=t}function Ig(t,e,n,l,i){if(t!==null){var r=t.memoizedProps;if(Br(r,l)&&t.ref===e.ref)if(Vt=!1,e.pendingProps=l=r,cf(t,i))t.flags&131072&&(Vt=!0);else return e.lanes=t.lanes,wn(t,e,i)}return ss(t,e,n,l,i)}function Jg(t,e,n){var l=e.pendingProps,i=l.children,r=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if(e.flags&128){if(l=r!==null?r.baseLanes|n:n,t!==null){for(i=e.child=t.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;e.childLanes=r&~l}else e.childLanes=0,e.child=null;return Bh(t,e,l,n)}if(n&536870912)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&nu(e,r!==null?r.cachePool:null),r!==null?Eh(e,r):is(),Gg(e);else return e.lanes=e.childLanes=536870912,Bh(t,e,r!==null?r.baseLanes|n:n,n)}else r!==null?(nu(e,r.cachePool),Eh(e,r),Hn(e),e.memoizedState=null):(t!==null&&nu(e,null),is(),Hn(e));return Ft(t,e,i,n),e.child}function Bh(t,e,n,l){var i=Fs();return i=i===null?null:{parent:Lt._currentValue,pool:i},e.memoizedState={baseLanes:n,cachePool:i},t!==null&&nu(e,null),is(),Gg(e),t!==null&&ea(t,e,l,!0),null}function ru(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(D(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function ss(t,e,n,l,i){return El(e),n=Ps(t,e,n,l,void 0,i),l=Ws(),t!==null&&!Vt?($s(t,e,i),wn(t,e,i)):(it&&l&&Xs(e),e.flags|=1,Ft(t,e,n,i),e.child)}function Hh(t,e,n,l,i,r){return El(e),e.updateQueue=null,n=cg(e,l,n,i),og(t),l=Ws(),t!==null&&!Vt?($s(t,e,r),wn(t,e,r)):(it&&l&&Xs(e),e.flags|=1,Ft(t,e,n,r),e.child)}function qh(t,e,n,l,i){if(El(e),e.stateNode===null){var r=fi,a=n.contextType;typeof a=="object"&&a!==null&&(r=$t(a)),r=new n(l,r),e.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=os,e.stateNode=r,r._reactInternals=e,r=e.stateNode,r.props=l,r.state=e.memoizedState,r.refs={},Ks(e),a=n.contextType,r.context=typeof a=="object"&&a!==null?$t(a):fi,r.state=e.memoizedState,a=n.getDerivedStateFromProps,typeof a=="function"&&(bc(e,n,a,l),r.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(a=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),a!==r.state&&os.enqueueReplaceState(r,r.state,null),zr(e,l,r,i),Ar(),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){r=e.stateNode;var u=e.memoizedProps,o=Al(n,u);r.props=o;var c=r.context,s=n.contextType;a=fi,typeof s=="object"&&s!==null&&(a=$t(s));var f=n.getDerivedStateFromProps;s=typeof f=="function"||typeof r.getSnapshotBeforeUpdate=="function",u=e.pendingProps!==u,s||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(u||c!==a)&&_h(e,r,l,a),Ln=!1;var p=e.memoizedState;r.state=p,zr(e,l,r,i),Ar(),c=e.memoizedState,u||p!==c||Ln?(typeof f=="function"&&(bc(e,n,f,l),c=e.memoizedState),(o=Ln||Rh(e,n,o,l,p,c,a))?(s||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(e.flags|=4194308)):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=c),r.props=l,r.state=c,r.context=a,l=o):(typeof r.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{r=e.stateNode,ns(t,e),a=e.memoizedProps,s=Al(n,a),r.props=s,f=e.pendingProps,p=r.context,c=n.contextType,o=fi,typeof c=="object"&&c!==null&&(o=$t(c)),u=n.getDerivedStateFromProps,(c=typeof u=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(a!==f||p!==o)&&_h(e,r,l,o),Ln=!1,p=e.memoizedState,r.state=p,zr(e,l,r,i),Ar();var m=e.memoizedState;a!==f||p!==m||Ln||t!==null&&t.dependencies!==null&&xu(t.dependencies)?(typeof u=="function"&&(bc(e,n,u,l),m=e.memoizedState),(s=Ln||Rh(e,n,s,l,p,m,o)||t!==null&&t.dependencies!==null&&xu(t.dependencies))?(c||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,m,o),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,m,o)),typeof r.componentDidUpdate=="function"&&(e.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof r.componentDidUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=m),r.props=l,r.state=m,r.context=o,l=s):(typeof r.componentDidUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),l=!1)}return r=l,ru(t,e),l=(e.flags&128)!==0,r||l?(r=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),e.flags|=1,t!==null&&l?(e.child=Mi(e,t.child,null,i),e.child=Mi(e,null,n,i)):Ft(t,e,n,i),e.memoizedState=r.state,t=e.child):t=wn(t,e,i),t}function jh(t,e,n,l){return ta(),e.flags|=256,Ft(t,e,n,l),e.child}var xc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function vc(t){return{baseLanes:t,cachePool:lg()}}function Sc(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Ne),t}function Pg(t,e,n){var l=e.pendingProps,i=!1,r=(e.flags&128)!==0,a;if((a=r)||(a=t!==null&&t.memoizedState===null?!1:(Ut.current&2)!==0),a&&(i=!0,e.flags&=-129),a=(e.flags&32)!==0,e.flags&=-33,t===null){if(it){if(i?Bn(e):Hn(e),it){var u=Et,o;if(o=u){t:{for(o=u,u=Je;o.nodeType!==8;){if(!u){u=null;break t}if(o=Ve(o.nextSibling),o===null){u=null;break t}}u=o}u!==null?(e.memoizedState={dehydrated:u,treeContext:bl!==null?{id:dn,overflow:gn}:null,retryLane:536870912,hydrationErrors:null},o=ve(18,null,null,0),o.stateNode=u,o.return=e,e.child=o,ae=e,Et=null,o=!0):o=!1}o||wl(e)}if(u=e.memoizedState,u!==null&&(u=u.dehydrated,u!==null))return Es(u)?e.lanes=32:e.lanes=536870912,null;bn(e)}return u=l.children,l=l.fallback,i?(Hn(e),i=e.mode,u=zu({mode:"hidden",children:u},i),l=yl(l,i,n,null),u.return=e,l.return=e,u.sibling=l,e.child=u,i=e.child,i.memoizedState=vc(n),i.childLanes=Sc(t,a,n),e.memoizedState=xc,l):(Bn(e),fs(e,u))}if(o=t.memoizedState,o!==null&&(u=o.dehydrated,u!==null)){if(r)e.flags&256?(Bn(e),e.flags&=-257,e=kc(t,e,n)):e.memoizedState!==null?(Hn(e),e.child=t.child,e.flags|=128,e=null):(Hn(e),i=l.fallback,u=e.mode,l=zu({mode:"visible",children:l.children},u),i=yl(i,u,n,null),i.flags|=2,l.return=e,i.return=e,l.sibling=i,e.child=l,Mi(e,t.child,null,n),l=e.child,l.memoizedState=vc(n),l.childLanes=Sc(t,a,n),e.memoizedState=xc,e=i);else if(Bn(e),Es(u)){if(a=u.nextSibling&&u.nextSibling.dataset,a)var c=a.dgst;a=c,l=Error(D(419)),l.stack="",l.digest=a,Hr({value:l,source:null,stack:null}),e=kc(t,e,n)}else if(Vt||ea(t,e,n,!1),a=(n&t.childLanes)!==0,Vt||a){if(a=dt,a!==null&&(l=n&-n,l=l&42?1:Rs(l),l=l&(a.suspendedLanes|n)?0:l,l!==0&&l!==o.retryLane))throw o.retryLane=l,Ui(t,l),Ee(a,t,l),Kg;u.data==="$?"||ys(),e=kc(t,e,n)}else u.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=o.treeContext,Et=Ve(u.nextSibling),ae=e,it=!0,xl=null,Je=!1,t!==null&&(De[Oe++]=dn,De[Oe++]=gn,De[Oe++]=bl,dn=t.id,gn=t.overflow,bl=e),e=fs(e,l.children),e.flags|=4096);return e}return i?(Hn(e),i=l.fallback,u=e.mode,o=t.child,c=o.sibling,l=xn(o,{mode:"hidden",children:l.children}),l.subtreeFlags=o.subtreeFlags&65011712,c!==null?i=xn(c,i):(i=yl(i,u,n,null),i.flags|=2),i.return=e,l.return=e,l.sibling=i,e.child=l,l=i,i=e.child,u=t.child.memoizedState,u===null?u=vc(n):(o=u.cachePool,o!==null?(c=Lt._currentValue,o=o.parent!==c?{parent:c,pool:c}:o):o=lg(),u={baseLanes:u.baseLanes|n,cachePool:o}),i.memoizedState=u,i.childLanes=Sc(t,a,n),e.memoizedState=xc,l):(Bn(e),n=t.child,t=n.sibling,n=xn(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(a=e.deletions,a===null?(e.deletions=[t],e.flags|=16):a.push(t)),e.child=n,e.memoizedState=null,n)}function fs(t,e){return e=zu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function zu(t,e){return t=ve(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function kc(t,e,n){return Mi(e,t.child,null,n),t=fs(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Yh(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Wc(t.return,e,n)}function wc(t,e,n,l,i){var r=t.memoizedState;r===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(r.isBackwards=e,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=i)}function Wg(t,e,n){var l=e.pendingProps,i=l.revealOrder,r=l.tail;if(Ft(t,e,l.children,n),l=Ut.current,l&2)l=l&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Yh(t,n,e);else if(t.tag===19)Yh(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(vt(Ut,l),i){case"forwards":for(n=e.child,i=null;n!==null;)t=n.alternate,t!==null&&Eu(t)===null&&(i=n),n=n.sibling;n=i,n===null?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),wc(e,!1,i,n,r);break;case"backwards":for(n=null,i=e.child,e.child=null;i!==null;){if(t=i.alternate,t!==null&&Eu(t)===null){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}wc(e,!0,n,null,r);break;case"together":wc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function wn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),tl|=e.lanes,!(n&e.childLanes))if(t!==null){if(ea(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(D(153));if(e.child!==null){for(t=e.child,n=xn(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=xn(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function cf(t,e){return t.lanes&e?!0:(t=t.dependencies,!!(t!==null&&xu(t)))}function bS(t,e,n){switch(e.tag){case 3:fu(e,e.stateNode.containerInfo),Un(e,Lt,t.memoizedState.cache),ta();break;case 27:case 5:jc(e);break;case 4:fu(e,e.stateNode.containerInfo);break;case 10:Un(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Bn(e),e.flags|=128,null):n&e.child.childLanes?Pg(t,e,n):(Bn(e),t=wn(t,e,n),t!==null?t.sibling:null);Bn(e);break;case 19:var i=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(ea(t,e,n,!1),l=(n&e.childLanes)!==0),i){if(l)return Wg(t,e,n);e.flags|=128}if(i=e.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),vt(Ut,Ut.current),l)break;return null;case 22:case 23:return e.lanes=0,Jg(t,e,n);case 24:Un(e,Lt,t.memoizedState.cache)}return wn(t,e,n)}function $g(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Vt=!0;else{if(!cf(t,n)&&!(e.flags&128))return Vt=!1,bS(t,e,n);Vt=!!(t.flags&131072)}else Vt=!1,it&&e.flags&1048576&&eg(e,bu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,i=l._init;if(l=i(l._payload),e.type=l,typeof l=="function")Vs(l)?(t=Al(l,t),e.tag=1,e=qh(null,e,l,t,n)):(e.tag=0,e=ss(null,e,l,t,n));else{if(l!=null){if(i=l.$$typeof,i===Ms){e.tag=11,e=Lh(null,e,l,t,n);break t}else if(i===Ds){e.tag=14,e=Uh(null,e,l,t,n);break t}}throw e=Hc(l)||l,Error(D(306,e,""))}}return e;case 0:return ss(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,i=Al(l,e.pendingProps),qh(t,e,l,i,n);case 3:t:{if(fu(e,e.stateNode.containerInfo),t===null)throw Error(D(387));l=e.pendingProps;var r=e.memoizedState;i=r.element,ns(t,e),zr(e,l,null,n);var a=e.memoizedState;if(l=a.cache,Un(e,Lt,l),l!==r.cache&&$c(e,[Lt],n,!0),Ar(),l=a.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:a.cache},e.updateQueue.baseState=r,e.memoizedState=r,e.flags&256){e=jh(t,e,l,n);break t}else if(l!==i){i=_e(Error(D(424)),e),Hr(i),e=jh(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Et=Ve(t.firstChild),ae=e,it=!0,xl=null,Je=!0,n=Yg(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(ta(),l===i){e=wn(t,e,n);break t}Ft(t,e,l,n)}e=e.child}return e;case 26:return ru(t,e),t===null?(n=ad(e.type,null,e.pendingProps,null))?e.memoizedState=n:it||(n=e.type,t=e.pendingProps,l=Nu(Xn.current).createElement(n),l[Wt]=e,l[me]=t,It(l,n,t),Gt(l),e.stateNode=l):e.memoizedState=ad(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return jc(e),t===null&&it&&(l=e.stateNode=qy(e.type,e.pendingProps,Xn.current),ae=e,Je=!0,i=Et,nl(e.type)?(Ts=i,Et=Ve(l.firstChild)):Et=i),Ft(t,e,e.pendingProps.children,n),ru(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&it&&((i=l=Et)&&(l=XS(l,e.type,e.pendingProps,Je),l!==null?(e.stateNode=l,ae=e,Et=Ve(l.firstChild),Je=!1,i=!0):i=!1),i||wl(e)),jc(e),i=e.type,r=e.pendingProps,a=t!==null?t.memoizedProps:null,l=r.children,ks(i,r)?l=null:a!==null&&ks(i,a)&&(e.flags|=32),e.memoizedState!==null&&(i=Ps(t,e,sS,null,null,n),Xr._currentValue=i),ru(t,e),Ft(t,e,l,n),e.child;case 6:return t===null&&it&&((t=n=Et)&&(n=QS(n,e.pendingProps,Je),n!==null?(e.stateNode=n,ae=e,Et=null,t=!0):t=!1),t||wl(e)),null;case 13:return Pg(t,e,n);case 4:return fu(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Mi(e,null,l,n):Ft(t,e,l,n),e.child;case 11:return Lh(t,e,e.type,e.pendingProps,n);case 7:return Ft(t,e,e.pendingProps,n),e.child;case 8:return Ft(t,e,e.pendingProps.children,n),e.child;case 12:return Ft(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,Un(e,e.type,l.value),Ft(t,e,l.children,n),e.child;case 9:return i=e.type._context,l=e.pendingProps.children,El(e),i=$t(i),l=l(i),e.flags|=1,Ft(t,e,l,n),e.child;case 14:return Uh(t,e,e.type,e.pendingProps,n);case 15:return Ig(t,e,e.type,e.pendingProps,n);case 19:return Wg(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=zu(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=xn(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Jg(t,e,n);case 24:return El(e),l=$t(Lt),t===null?(i=Fs(),i===null&&(i=dt,r=Zs(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=n),i=r),e.memoizedState={parent:l,cache:i},Ks(e),Un(e,Lt,i)):(t.lanes&n&&(ns(t,e),zr(e,null,null,n),Ar()),i=t.memoizedState,r=e.memoizedState,i.parent!==l?(i={parent:l,cache:l},e.memoizedState=i,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=i),Un(e,Lt,l)):(l=r.cache,Un(e,Lt,l),l!==i.cache&&$c(e,[Lt],n,!0))),Ft(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(D(156,e.tag))}function fn(t){t.flags|=4}function Gh(t,e){if(e.type!=="stylesheet"||e.state.loading&4)t.flags&=-16777217;else if(t.flags|=16777216,!Gy(e)){if(e=Le.current,e!==null&&((lt&4194048)===lt?tn!==null:(lt&62914560)!==lt&&!(lt&536870912)||e!==tn))throw Er=es,ig;t.flags|=8192}}function Qa(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Td():536870912,t.lanes|=e,Di|=e)}function mr(t,e){if(!it)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function kt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=t,i=i.sibling;else for(i=t.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=t,i=i.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function xS(t,e,n){var l=e.pendingProps;switch(Qs(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return kt(e),null;case 1:return kt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),vn(Lt),wi(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(sr(e)?fn(e):t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,bh())),kt(e),null;case 26:return n=e.memoizedState,t===null?(fn(e),n!==null?(kt(e),Gh(e,n)):(kt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(fn(e),kt(e),Gh(e,n)):(kt(e),e.flags&=-16777217):(t.memoizedProps!==l&&fn(e),kt(e),e.flags&=-16777217),null;case 27:mu(e),n=Xn.current;var i=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(!l){if(e.stateNode===null)throw Error(D(166));return kt(e),null}t=We.current,sr(e)?gh(e,t):(t=qy(i,l,n),e.stateNode=t,fn(e))}return kt(e),null;case 5:if(mu(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(!l){if(e.stateNode===null)throw Error(D(166));return kt(e),null}if(t=We.current,sr(e))gh(e,t);else{switch(i=Nu(Xn.current),t){case 1:t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=i.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}t[Wt]=e,t[me]=l;t:for(i=e.child;i!==null;){if(i.tag===5||i.tag===6)t.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===e)break t;for(;i.sibling===null;){if(i.return===null||i.return===e)break t;i=i.return}i.sibling.return=i.return,i=i.sibling}e.stateNode=t;t:switch(It(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&fn(e)}}return kt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&fn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(D(166));if(t=Xn.current,sr(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,i=ae,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}t[Wt]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||Uy(t.nodeValue,n)),t||wl(e)}else t=Nu(t).createTextNode(l),t[Wt]=e,e.stateNode=t}return kt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(i=sr(e),l!==null&&l.dehydrated!==null){if(t===null){if(!i)throw Error(D(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(D(317));i[Wt]=e}else ta(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;kt(e),i=!1}else i=bh(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=i),i=!0;if(!i)return e.flags&256?(bn(e),e):(bn(e),null)}if(bn(e),e.flags&128)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==i&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Qa(e,e.updateQueue),kt(e),null;case 4:return wi(),t===null&&yf(e.stateNode.containerInfo),kt(e),null;case 10:return vn(e.type),kt(e),null;case 19:if(Xt(Ut),i=e.memoizedState,i===null)return kt(e),null;if(l=(e.flags&128)!==0,r=i.rendering,r===null)if(l)mr(i,!1);else{if(Tt!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(r=Eu(t),r!==null){for(e.flags|=128,mr(i,!1),t=r.updateQueue,e.updateQueue=t,Qa(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)tg(n,t),n=n.sibling;return vt(Ut,Ut.current&1|2),e.child}t=t.sibling}i.tail!==null&&$e()>Mu&&(e.flags|=128,l=!0,mr(i,!1),e.lanes=4194304)}else{if(!l)if(t=Eu(r),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Qa(e,t),mr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!it)return kt(e),null}else 2*$e()-i.renderingStartTime>Mu&&n!==536870912&&(e.flags|=128,l=!0,mr(i,!1),e.lanes=4194304);i.isBackwards?(r.sibling=e.child,e.child=r):(t=i.last,t!==null?t.sibling=r:e.child=r,i.last=r)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=$e(),e.sibling=null,t=Ut.current,vt(Ut,l?t&1|2:t&1),e):(kt(e),null);case 22:case 23:return bn(e),Is(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?n&536870912&&!(e.flags&128)&&(kt(e),e.subtreeFlags&6&&(e.flags|=8192)):kt(e),n=e.updateQueue,n!==null&&Qa(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&Xt(vl),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),vn(Lt),kt(e),null;case 25:return null;case 30:return null}throw Error(D(156,e.tag))}function vS(t,e){switch(Qs(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return vn(Lt),wi(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return mu(e),null;case 13:if(bn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(D(340));ta()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Xt(Ut),null;case 4:return wi(),null;case 10:return vn(e.type),null;case 22:case 23:return bn(e),Is(),t!==null&&Xt(vl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return vn(Lt),null;case 25:return null;default:return null}}function ty(t,e){switch(Qs(e),e.tag){case 3:vn(Lt),wi();break;case 26:case 27:case 5:mu(e);break;case 4:wi();break;case 13:bn(e);break;case 19:Xt(Ut);break;case 10:vn(e.type);break;case 22:case 23:bn(e),Is(),t!==null&&Xt(vl);break;case 24:vn(Lt)}}function aa(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&t)===t){l=void 0;var r=n.create,a=n.inst;l=r(),a.destroy=l}n=n.next}while(n!==i)}}catch(u){ht(e,e.return,u)}}function $n(t,e,n){try{var l=e.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var r=i.next;l=r;do{if((l.tag&t)===t){var a=l.inst,u=a.destroy;if(u!==void 0){a.destroy=void 0,i=e;var o=n,c=u;try{c()}catch(s){ht(i,o,s)}}}l=l.next}while(l!==r)}}catch(s){ht(e,e.return,s)}}function ey(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{ug(e,n)}catch(l){ht(t,t.return,l)}}}function ny(t,e,n){n.props=Al(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){ht(t,e,l)}}function Mr(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(i){ht(t,e,i)}}function Pe(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){ht(t,e,i)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){ht(t,e,i)}else n.current=null}function ly(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){ht(t,t.return,i)}}function Ec(t,e,n){try{var l=t.stateNode;qS(l,t.type,n,e),l[me]=e}catch(i){ht(t,t.return,i)}}function iy(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&nl(t.type)||t.tag===4}function Tc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||iy(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&nl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ms(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=$u));else if(l!==4&&(l===27&&nl(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(ms(t,e,n),t=t.sibling;t!==null;)ms(t,e,n),t=t.sibling}function Cu(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&nl(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Cu(t,e,n),t=t.sibling;t!==null;)Cu(t,e,n),t=t.sibling}function ry(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,i=e.attributes;i.length;)e.removeAttributeNode(i[0]);It(e,l,n),e[Wt]=t,e[me]=n}catch(r){ht(t,t.return,r)}}var pn=!1,Mt=!1,Ac=!1,Vh=typeof WeakSet=="function"?WeakSet:Set,Yt=null;function SS(t,e){if(t=t.containerInfo,vs=Hu,t=Zd(t),js(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch(v){n=null;break t}var a=0,u=-1,o=-1,c=0,s=0,f=t,p=null;e:for(;;){for(var m;f!==n||i!==0&&f.nodeType!==3||(u=a+i),f!==r||l!==0&&f.nodeType!==3||(o=a+l),f.nodeType===3&&(a+=f.nodeValue.length),(m=f.firstChild)!==null;)p=f,f=m;for(;;){if(f===t)break e;if(p===n&&++c===i&&(u=a),p===r&&++s===l&&(o=a),(m=f.nextSibling)!==null)break;f=p,p=f.parentNode}f=m}n=u===-1||o===-1?null:{start:u,end:o}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ss={focusedElem:t,selectionRange:n},Hu=!1,Yt=e;Yt!==null;)if(e=Yt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Yt=t;else for(;Yt!==null;){switch(e=Yt,r=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if(t&1024&&r!==null){t=void 0,n=e,i=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var y=Al(n.type,i,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(y,r),l.__reactInternalSnapshotBeforeUpdate=t}catch(v){ht(n,n.return,v)}}break;case 3:if(t&1024){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)ws(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":ws(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(t&1024)throw Error(D(163))}if(t=e.sibling,t!==null){t.return=e.return,Yt=t;break}Yt=e.return}}function ay(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:Rn(t,n),l&4&&aa(5,n);break;case 1:if(Rn(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(a){ht(n,n.return,a)}else{var i=Al(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(i,e,t.__reactInternalSnapshotBeforeUpdate)}catch(a){ht(n,n.return,a)}}l&64&&ey(n),l&512&&Mr(n,n.return);break;case 3:if(Rn(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{ug(t,e)}catch(a){ht(n,n.return,a)}}break;case 27:e===null&&l&4&&ry(n);case 26:case 5:Rn(t,n),e===null&&l&4&&ly(n),l&512&&Mr(n,n.return);break;case 12:Rn(t,n);break;case 13:Rn(t,n),l&4&&cy(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=DS.bind(null,n),ZS(t,n))));break;case 22:if(l=n.memoizedState!==null||pn,!l){e=e!==null&&e.memoizedState!==null||Mt,i=pn;var r=Mt;pn=l,(Mt=e)&&!r?_n(t,n,(n.subtreeFlags&8772)!==0):Rn(t,n),pn=i,Mt=r}break;case 30:break;default:Rn(t,n)}}function uy(t){var e=t.alternate;e!==null&&(t.alternate=null,uy(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Ns(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var xt=null,se=!1;function mn(t,e,n){for(n=n.child;n!==null;)oy(t,e,n),n=n.sibling}function oy(t,e,n){if(Se&&typeof Se.onCommitFiberUnmount=="function")try{Se.onCommitFiberUnmount(Ir,n)}catch(r){}switch(n.tag){case 26:Mt||Pe(n,e),mn(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Mt||Pe(n,e);var l=xt,i=se;nl(n.type)&&(xt=n.stateNode,se=!1),mn(t,e,n),_r(n.stateNode),xt=l,se=i;break;case 5:Mt||Pe(n,e);case 6:if(l=xt,i=se,xt=null,mn(t,e,n),xt=l,se=i,xt!==null)if(se)try{(xt.nodeType===9?xt.body:xt.nodeName==="HTML"?xt.ownerDocument.body:xt).removeChild(n.stateNode)}catch(r){ht(n,e,r)}else try{xt.removeChild(n.stateNode)}catch(r){ht(n,e,r)}break;case 18:xt!==null&&(se?(t=xt,ld(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Fr(t)):ld(xt,n.stateNode));break;case 4:l=xt,i=se,xt=n.stateNode.containerInfo,se=!0,mn(t,e,n),xt=l,se=i;break;case 0:case 11:case 14:case 15:Mt||$n(2,n,e),Mt||$n(4,n,e),mn(t,e,n);break;case 1:Mt||(Pe(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&ny(n,e,l)),mn(t,e,n);break;case 21:mn(t,e,n);break;case 22:Mt=(l=Mt)||n.memoizedState!==null,mn(t,e,n),Mt=l;break;default:mn(t,e,n)}}function cy(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Fr(t)}catch(n){ht(e,e.return,n)}}function kS(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Vh),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Vh),e;default:throw Error(D(435,t.tag))}}function zc(t,e){var n=kS(t);e.forEach(function(l){var i=OS.bind(null,t,l);n.has(l)||(n.add(l),l.then(i,i))})}function ye(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],r=t,a=e,u=a;t:for(;u!==null;){switch(u.tag){case 27:if(nl(u.type)){xt=u.stateNode,se=!1;break t}break;case 5:xt=u.stateNode,se=!1;break t;case 3:case 4:xt=u.stateNode.containerInfo,se=!0;break t}u=u.return}if(xt===null)throw Error(D(160));oy(r,a,i),xt=null,se=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)sy(e,t),e=e.sibling}var Ge=null;function sy(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ye(e,t),be(t),l&4&&($n(3,t,t.return),aa(3,t),$n(5,t,t.return));break;case 1:ye(e,t),be(t),l&512&&(Mt||n===null||Pe(n,n.return)),l&64&&pn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Ge;if(ye(e,t),be(t),l&512&&(Mt||n===null||Pe(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,i=i.ownerDocument||i;e:switch(l){case"title":r=i.getElementsByTagName("title")[0],(!r||r[Wr]||r[Wt]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(l),i.head.insertBefore(r,i.querySelector("head > title"))),It(r,l,n),r[Wt]=t,Gt(r),l=r;break t;case"link":var a=od("link","href",i).get(l+(n.href||""));if(a){for(var u=0;u<a.length;u++)if(r=a[u],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){a.splice(u,1);break e}}r=i.createElement(l),It(r,l,n),i.head.appendChild(r);break;case"meta":if(a=od("meta","content",i).get(l+(n.content||""))){for(u=0;u<a.length;u++)if(r=a[u],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){a.splice(u,1);break e}}r=i.createElement(l),It(r,l,n),i.head.appendChild(r);break;default:throw Error(D(468,l))}r[Wt]=t,Gt(r),l=r}t.stateNode=l}else cd(i,t.type,t.stateNode);else t.stateNode=ud(i,l,t.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?cd(i,t.type,t.stateNode):ud(i,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Ec(t,t.memoizedProps,n.memoizedProps)}break;case 27:ye(e,t),be(t),l&512&&(Mt||n===null||Pe(n,n.return)),n!==null&&l&4&&Ec(t,t.memoizedProps,n.memoizedProps);break;case 5:if(ye(e,t),be(t),l&512&&(Mt||n===null||Pe(n,n.return)),t.flags&32){i=t.stateNode;try{Ti(i,"")}catch(m){ht(t,t.return,m)}}l&4&&t.stateNode!=null&&(i=t.memoizedProps,Ec(t,i,n!==null?n.memoizedProps:i)),l&1024&&(Ac=!0);break;case 6:if(ye(e,t),be(t),l&4){if(t.stateNode===null)throw Error(D(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(m){ht(t,t.return,m)}}break;case 3:if(ou=null,i=Ge,Ge=Lu(e.containerInfo),ye(e,t),Ge=i,be(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Fr(e.containerInfo)}catch(m){ht(t,t.return,m)}Ac&&(Ac=!1,fy(t));break;case 4:l=Ge,Ge=Lu(t.stateNode.containerInfo),ye(e,t),be(t),Ge=l;break;case 12:ye(e,t),be(t);break;case 13:ye(e,t),be(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(hf=$e()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,zc(t,l)));break;case 22:i=t.memoizedState!==null;var o=n!==null&&n.memoizedState!==null,c=pn,s=Mt;if(pn=c||i,Mt=s||o,ye(e,t),Mt=s,pn=c,be(t),l&8192)t:for(e=t.stateNode,e._visibility=i?e._visibility&-2:e._visibility|1,i&&(n===null||o||pn||Mt||dl(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){o=n=e;try{if(r=o.stateNode,i)a=r.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none";else{u=o.stateNode;var f=o.memoizedProps.style,p=f!=null&&f.hasOwnProperty("display")?f.display:null;u.style.display=p==null||typeof p=="boolean"?"":(""+p).trim()}}catch(m){ht(o,o.return,m)}}}else if(e.tag===6){if(n===null){o=e;try{o.stateNode.nodeValue=i?"":o.memoizedProps}catch(m){ht(o,o.return,m)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,zc(t,n))));break;case 19:ye(e,t),be(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,zc(t,l)));break;case 30:break;case 21:break;default:ye(e,t),be(t)}}function be(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(iy(l)){n=l;break}l=l.return}if(n==null)throw Error(D(160));switch(n.tag){case 27:var i=n.stateNode,r=Tc(t);Cu(t,r,i);break;case 5:var a=n.stateNode;n.flags&32&&(Ti(a,""),n.flags&=-33);var u=Tc(t);Cu(t,u,a);break;case 3:case 4:var o=n.stateNode.containerInfo,c=Tc(t);ms(t,c,o);break;default:throw Error(D(161))}}catch(s){ht(t,t.return,s)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function fy(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;fy(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Rn(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)ay(t,e.alternate,e),e=e.sibling}function dl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:$n(4,e,e.return),dl(e);break;case 1:Pe(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&ny(e,e.return,n),dl(e);break;case 27:_r(e.stateNode);case 26:case 5:Pe(e,e.return),dl(e);break;case 22:e.memoizedState===null&&dl(e);break;case 30:dl(e);break;default:dl(e)}t=t.sibling}}function _n(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,i=t,r=e,a=r.flags;switch(r.tag){case 0:case 11:case 15:_n(i,r,n),aa(4,r);break;case 1:if(_n(i,r,n),l=r,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(c){ht(l,l.return,c)}if(l=r,i=l.updateQueue,i!==null){var u=l.stateNode;try{var o=i.shared.hiddenCallbacks;if(o!==null)for(i.shared.hiddenCallbacks=null,i=0;i<o.length;i++)ag(o[i],u)}catch(c){ht(l,l.return,c)}}n&&a&64&&ey(r),Mr(r,r.return);break;case 27:ry(r);case 26:case 5:_n(i,r,n),n&&l===null&&a&4&&ly(r),Mr(r,r.return);break;case 12:_n(i,r,n);break;case 13:_n(i,r,n),n&&a&4&&cy(i,r);break;case 22:r.memoizedState===null&&_n(i,r,n),Mr(r,r.return);break;case 30:break;default:_n(i,r,n)}e=e.sibling}}function sf(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&na(n))}function ff(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&na(t))}function Ie(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)my(t,e,n,l),e=e.sibling}function my(t,e,n,l){var i=e.flags;switch(e.tag){case 0:case 11:case 15:Ie(t,e,n,l),i&2048&&aa(9,e);break;case 1:Ie(t,e,n,l);break;case 3:Ie(t,e,n,l),i&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&na(t)));break;case 12:if(i&2048){Ie(t,e,n,l),t=e.stateNode;try{var r=e.memoizedProps,a=r.id,u=r.onPostCommit;typeof u=="function"&&u(a,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(o){ht(e,e.return,o)}}else Ie(t,e,n,l);break;case 13:Ie(t,e,n,l);break;case 23:break;case 22:r=e.stateNode,a=e.alternate,e.memoizedState!==null?r._visibility&2?Ie(t,e,n,l):Dr(t,e):r._visibility&2?Ie(t,e,n,l):(r._visibility|=2,ei(t,e,n,l,(e.subtreeFlags&10256)!==0)),i&2048&&sf(a,e);break;case 24:Ie(t,e,n,l),i&2048&&ff(e.alternate,e);break;default:Ie(t,e,n,l)}}function ei(t,e,n,l,i){for(i=i&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var r=t,a=e,u=n,o=l,c=a.flags;switch(a.tag){case 0:case 11:case 15:ei(r,a,u,o,i),aa(8,a);break;case 23:break;case 22:var s=a.stateNode;a.memoizedState!==null?s._visibility&2?ei(r,a,u,o,i):Dr(r,a):(s._visibility|=2,ei(r,a,u,o,i)),i&&c&2048&&sf(a.alternate,a);break;case 24:ei(r,a,u,o,i),i&&c&2048&&ff(a.alternate,a);break;default:ei(r,a,u,o,i)}e=e.sibling}}function Dr(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,i=l.flags;switch(l.tag){case 22:Dr(n,l),i&2048&&sf(l.alternate,l);break;case 24:Dr(n,l),i&2048&&ff(l.alternate,l);break;default:Dr(n,l)}e=e.sibling}}var xr=8192;function Wl(t){if(t.subtreeFlags&xr)for(t=t.child;t!==null;)py(t),t=t.sibling}function py(t){switch(t.tag){case 26:Wl(t),t.flags&xr&&t.memoizedState!==null&&rk(Ge,t.memoizedState,t.memoizedProps);break;case 5:Wl(t);break;case 3:case 4:var e=Ge;Ge=Lu(t.stateNode.containerInfo),Wl(t),Ge=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=xr,xr=16777216,Wl(t),xr=e):Wl(t));break;default:Wl(t)}}function hy(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function pr(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Yt=l,gy(l,t)}hy(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)dy(t),t=t.sibling}function dy(t){switch(t.tag){case 0:case 11:case 15:pr(t),t.flags&2048&&$n(9,t,t.return);break;case 3:pr(t);break;case 12:pr(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,au(t)):pr(t);break;default:pr(t)}}function au(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];Yt=l,gy(l,t)}hy(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:$n(8,e,e.return),au(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,au(e));break;default:au(e)}t=t.sibling}}function gy(t,e){for(;Yt!==null;){var n=Yt;switch(n.tag){case 0:case 11:case 15:$n(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:na(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,Yt=l;else t:for(n=t;Yt!==null;){l=Yt;var i=l.sibling,r=l.return;if(uy(l),l===n){Yt=null;break t}if(i!==null){i.return=r,Yt=i;break t}Yt=r}}}var wS={getCacheForType:function(t){var e=$t(Lt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},ES=typeof WeakMap=="function"?WeakMap:Map,ut=0,dt=null,tt=null,lt=0,at=0,xe=null,Gn=!1,Bi=!1,mf=!1,En=0,Tt=0,tl=0,Sl=0,pf=0,Ne=0,Di=0,Or=null,fe=null,ps=!1,hf=0,Mu=1/0,Du=null,Fn=null,Kt=0,Kn=null,Oi=null,ki=0,hs=0,ds=null,yy=null,Rr=0,gs=null;function we(){if(ut&2&&lt!==0)return lt&-lt;if(V.T!==null){var t=Ai;return t!==0?t:gf()}return Cd()}function by(){Ne===0&&(Ne=!(lt&536870912)||it?Ed():536870912);var t=Le.current;return t!==null&&(t.flags|=32),Ne}function Ee(t,e,n){(t===dt&&(at===2||at===9)||t.cancelPendingCommit!==null)&&(Ri(t,0),Vn(t,lt,Ne,!1)),Pr(t,n),(!(ut&2)||t!==dt)&&(t===dt&&(!(ut&2)&&(Sl|=n),Tt===4&&Vn(t,lt,Ne,!1)),nn(t))}function xy(t,e,n){if(ut&6)throw Error(D(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||Jr(t,e),i=l?zS(t,e):Cc(t,e,!0),r=l;do{if(i===0){Bi&&!l&&Vn(t,e,0,!1);break}else{if(n=t.current.alternate,r&&!TS(n)){i=Cc(t,e,!1),r=!1;continue}if(i===2){if(r=e,t.errorRecoveryDisabledLanes&r)var a=0;else a=t.pendingLanes&-536870913,a=a!==0?a:a&536870912?536870912:0;if(a!==0){e=a;t:{var u=t;i=Or;var o=u.current.memoizedState.isDehydrated;if(o&&(Ri(u,a).flags|=256),a=Cc(u,a,!1),a!==2){if(mf&&!o){u.errorRecoveryDisabledLanes|=r,Sl|=r,i=4;break t}r=fe,fe=i,r!==null&&(fe===null?fe=r:fe.push.apply(fe,r))}i=a}if(r=!1,i!==2)continue}}if(i===1){Ri(t,0),Vn(t,e,0,!0);break}t:{switch(l=t,r=i,r){case 0:case 1:throw Error(D(345));case 4:if((e&4194048)!==e)break;case 6:Vn(l,e,Ne,!Gn);break t;case 2:fe=null;break;case 3:case 5:break;default:throw Error(D(329))}if((e&62914560)===e&&(i=hf+300-$e(),10<i)){if(Vn(l,e,Ne,!Gn),ju(l,0,!0)!==0)break t;l.timeoutHandle=Hy(Xh.bind(null,l,n,fe,Du,ps,e,Ne,Sl,Di,Gn,r,2,-0,0),i);break t}Xh(l,n,fe,Du,ps,e,Ne,Sl,Di,Gn,r,0,-0,0)}}break}while(!0);nn(t)}function Xh(t,e,n,l,i,r,a,u,o,c,s,f,p,m){if(t.timeoutHandle=-1,f=e.subtreeFlags,(f&8192||(f&16785408)===16785408)&&(Vr={stylesheets:null,count:0,unsuspend:ik},py(e),f=ak(),f!==null)){t.cancelPendingCommit=f(Zh.bind(null,t,e,r,n,l,i,a,u,o,s,1,p,m)),Vn(t,r,a,!c);return}Zh(t,e,r,n,l,i,a,u,o)}function TS(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],r=i.getSnapshot;i=i.value;try{if(!Te(r(),i))return!1}catch(a){return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Vn(t,e,n,l){e&=~pf,e&=~Sl,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var i=e;0<i;){var r=31-ke(i),a=1<<r;l[r]=-1,i&=~a}n!==0&&Ad(t,n,e)}function Ju(){return ut&6?!0:(ua(0,!1),!1)}function df(){if(tt!==null){if(at===0)var t=tt.return;else t=tt,yn=Dl=null,tf(t),Si=null,jr=0,t=tt;for(;t!==null;)ty(t.alternate,t),t=t.return;tt=null}}function Ri(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,YS(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),df(),dt=t,tt=n=xn(t.current,null),lt=e,at=0,xe=null,Gn=!1,Bi=Jr(t,e),mf=!1,Di=Ne=pf=Sl=tl=Tt=0,fe=Or=null,ps=!1,e&8&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var i=31-ke(l),r=1<<i;e|=t[i],l&=~r}return En=e,Xu(),n}function vy(t,e){I=null,V.H=wu,e===la||e===Zu?(e=kh(),at=3):e===ig?(e=kh(),at=4):at=e===Kg?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,xe=e,tt===null&&(Tt=1,Au(t,_e(e,t.current)))}function Sy(){var t=V.H;return V.H=wu,t===null?wu:t}function ky(){var t=V.A;return V.A=wS,t}function ys(){Tt=4,Gn||(lt&4194048)!==lt&&Le.current!==null||(Bi=!0),!(tl&134217727)&&!(Sl&134217727)||dt===null||Vn(dt,lt,Ne,!1)}function Cc(t,e,n){var l=ut;ut|=2;var i=Sy(),r=ky();(dt!==t||lt!==e)&&(Du=null,Ri(t,e)),e=!1;var a=Tt;t:do try{if(at!==0&&tt!==null){var u=tt,o=xe;switch(at){case 8:df(),a=6;break t;case 3:case 2:case 9:case 6:Le.current===null&&(e=!0);var c=at;if(at=0,xe=null,hi(t,u,o,c),n&&Bi){a=0;break t}break;default:c=at,at=0,xe=null,hi(t,u,o,c)}}AS(),a=Tt;break}catch(s){vy(t,s)}while(!0);return e&&t.shellSuspendCounter++,yn=Dl=null,ut=l,V.H=i,V.A=r,tt===null&&(dt=null,lt=0,Xu()),a}function AS(){for(;tt!==null;)wy(tt)}function zS(t,e){var n=ut;ut|=2;var l=Sy(),i=ky();dt!==t||lt!==e?(Du=null,Mu=$e()+500,Ri(t,e)):Bi=Jr(t,e);t:do try{if(at!==0&&tt!==null){e=tt;var r=xe;e:switch(at){case 1:at=0,xe=null,hi(t,e,r,1);break;case 2:case 9:if(Sh(r)){at=0,xe=null,Qh(e);break}e=function(){at!==2&&at!==9||dt!==t||(at=7),nn(t)},r.then(e,e);break t;case 3:at=7;break t;case 4:at=5;break t;case 7:Sh(r)?(at=0,xe=null,Qh(e)):(at=0,xe=null,hi(t,e,r,7));break;case 5:var a=null;switch(tt.tag){case 26:a=tt.memoizedState;case 5:case 27:var u=tt;if(!a||Gy(a)){at=0,xe=null;var o=u.sibling;if(o!==null)tt=o;else{var c=u.return;c!==null?(tt=c,Pu(c)):tt=null}break e}}at=0,xe=null,hi(t,e,r,5);break;case 6:at=0,xe=null,hi(t,e,r,6);break;case 8:df(),Tt=6;break t;default:throw Error(D(462))}}CS();break}catch(s){vy(t,s)}while(!0);return yn=Dl=null,V.H=l,V.A=i,ut=n,tt!==null?0:(dt=null,lt=0,Xu(),Tt)}function CS(){for(;tt!==null&&!Jx();)wy(tt)}function wy(t){var e=$g(t.alternate,t,En);t.memoizedProps=t.pendingProps,e===null?Pu(t):tt=e}function Qh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=Hh(n,e,e.pendingProps,e.type,void 0,lt);break;case 11:e=Hh(n,e,e.pendingProps,e.type.render,e.ref,lt);break;case 5:tf(e);default:ty(n,e),e=tt=tg(e,En),e=$g(n,e,En)}t.memoizedProps=t.pendingProps,e===null?Pu(t):tt=e}function hi(t,e,n,l){yn=Dl=null,tf(e),Si=null,jr=0;var i=e.return;try{if(yS(t,i,e,n,lt)){Tt=1,Au(t,_e(n,t.current)),tt=null;return}}catch(r){if(i!==null)throw tt=i,r;Tt=1,Au(t,_e(n,t.current)),tt=null;return}e.flags&32768?(it||l===1?t=!0:Bi||lt&536870912?t=!1:(Gn=t=!0,(l===2||l===9||l===3||l===6)&&(l=Le.current,l!==null&&l.tag===13&&(l.flags|=16384))),Ey(e,t)):Pu(e)}function Pu(t){var e=t;do{if(e.flags&32768){Ey(e,Gn);return}t=e.return;var n=xS(e.alternate,e,En);if(n!==null){tt=n;return}if(e=e.sibling,e!==null){tt=e;return}tt=e=t}while(e!==null);Tt===0&&(Tt=5)}function Ey(t,e){do{var n=vS(t.alternate,t);if(n!==null){n.flags&=32767,tt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){tt=t;return}tt=t=n}while(t!==null);Tt=6,tt=null}function Zh(t,e,n,l,i,r,a,u,o){t.cancelPendingCommit=null;do Wu();while(Kt!==0);if(ut&6)throw Error(D(327));if(e!==null){if(e===t.current)throw Error(D(177));if(r=e.lanes|e.childLanes,r|=Ys,av(t,n,r,a,u,o),t===dt&&(tt=dt=null,lt=0),Oi=e,Kn=t,ki=n,hs=r,ds=i,yy=l,e.subtreeFlags&10256||e.flags&10256?(t.callbackNode=null,t.callbackPriority=0,RS(pu,function(){return My(!0),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,e.subtreeFlags&13878||l){l=V.T,V.T=null,i=rt.p,rt.p=2,a=ut,ut|=4;try{SS(t,e,n)}finally{ut=a,rt.p=i,V.T=l}}Kt=1,Ty(),Ay(),zy()}}function Ty(){if(Kt===1){Kt=0;var t=Kn,e=Oi,n=(e.flags&13878)!==0;if(e.subtreeFlags&13878||n){n=V.T,V.T=null;var l=rt.p;rt.p=2;var i=ut;ut|=4;try{sy(e,t);var r=Ss,a=Zd(t.containerInfo),u=r.focusedElem,o=r.selectionRange;if(a!==u&&u&&u.ownerDocument&&Qd(u.ownerDocument.documentElement,u)){if(o!==null&&js(u)){var c=o.start,s=o.end;if(s===void 0&&(s=c),"selectionStart"in u)u.selectionStart=c,u.selectionEnd=Math.min(s,u.value.length);else{var f=u.ownerDocument||document,p=f&&f.defaultView||window;if(p.getSelection){var m=p.getSelection(),y=u.textContent.length,v=Math.min(o.start,y),E=o.end===void 0?v:Math.min(o.end,y);!m.extend&&v>E&&(a=E,E=v,v=a);var h=ph(u,v),d=ph(u,E);if(h&&d&&(m.rangeCount!==1||m.anchorNode!==h.node||m.anchorOffset!==h.offset||m.focusNode!==d.node||m.focusOffset!==d.offset)){var g=f.createRange();g.setStart(h.node,h.offset),m.removeAllRanges(),v>E?(m.addRange(g),m.extend(d.node,d.offset)):(g.setEnd(d.node,d.offset),m.addRange(g))}}}}for(f=[],m=u;m=m.parentNode;)m.nodeType===1&&f.push({element:m,left:m.scrollLeft,top:m.scrollTop});for(typeof u.focus=="function"&&u.focus(),u=0;u<f.length;u++){var k=f[u];k.element.scrollLeft=k.left,k.element.scrollTop=k.top}}Hu=!!vs,Ss=vs=null}finally{ut=i,rt.p=l,V.T=n}}t.current=e,Kt=2}}function Ay(){if(Kt===2){Kt=0;var t=Kn,e=Oi,n=(e.flags&8772)!==0;if(e.subtreeFlags&8772||n){n=V.T,V.T=null;var l=rt.p;rt.p=2;var i=ut;ut|=4;try{ay(t,e.alternate,e)}finally{ut=i,rt.p=l,V.T=n}}Kt=3}}function zy(){if(Kt===4||Kt===3){Kt=0,Px();var t=Kn,e=Oi,n=ki,l=yy;e.subtreeFlags&10256||e.flags&10256?Kt=5:(Kt=0,Oi=Kn=null,Cy(t,t.pendingLanes));var i=t.pendingLanes;if(i===0&&(Fn=null),_s(n),e=e.stateNode,Se&&typeof Se.onCommitFiberRoot=="function")try{Se.onCommitFiberRoot(Ir,e,void 0,(e.current.flags&128)===128)}catch(o){}if(l!==null){e=V.T,i=rt.p,rt.p=2,V.T=null;try{for(var r=t.onRecoverableError,a=0;a<l.length;a++){var u=l[a];r(u.value,{componentStack:u.stack})}}finally{V.T=e,rt.p=i}}ki&3&&Wu(),nn(t),i=t.pendingLanes,n&4194090&&i&42?t===gs?Rr++:(Rr=0,gs=t):Rr=0,ua(0,!1)}}function Cy(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,na(e)))}function Wu(t){return Ty(),Ay(),zy(),My(t)}function My(){if(Kt!==5)return!1;var t=Kn,e=hs;hs=0;var n=_s(ki),l=V.T,i=rt.p;try{rt.p=32>n?32:n,V.T=null,n=ds,ds=null;var r=Kn,a=ki;if(Kt=0,Oi=Kn=null,ki=0,ut&6)throw Error(D(331));var u=ut;if(ut|=4,dy(r.current),my(r,r.current,a,n),ut=u,ua(0,!1),Se&&typeof Se.onPostCommitFiberRoot=="function")try{Se.onPostCommitFiberRoot(Ir,r)}catch(o){}return!0}finally{rt.p=i,V.T=l,Cy(t,e)}}function Fh(t,e,n){e=_e(n,e),e=cs(t.stateNode,e,2),t=Zn(t,e,2),t!==null&&(Pr(t,2),nn(t))}function ht(t,e,n){if(t.tag===3)Fh(t,t,n);else for(;e!==null;){if(e.tag===3){Fh(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Fn===null||!Fn.has(l))){t=_e(n,t),n=Zg(2),l=Zn(e,n,2),l!==null&&(Fg(n,l,e,t),Pr(l,2),nn(l));break}}e=e.return}}function Mc(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new ES;var i=new Set;l.set(e,i)}else i=l.get(e),i===void 0&&(i=new Set,l.set(e,i));i.has(n)||(mf=!0,i.add(n),t=MS.bind(null,t,e,n),e.then(t,t))}function MS(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,dt===t&&(lt&n)===n&&(Tt===4||Tt===3&&(lt&62914560)===lt&&300>$e()-hf?!(ut&2)&&Ri(t,0):pf|=n,Di===lt&&(Di=0)),nn(t)}function Dy(t,e){e===0&&(e=Td()),t=Ui(t,e),t!==null&&(Pr(t,e),nn(t))}function DS(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Dy(t,n)}function OS(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,i=t.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(D(314))}l!==null&&l.delete(e),Dy(t,n)}function RS(t,e){return Os(t,e)}var Ou=null,ni=null,bs=!1,Ru=!1,Dc=!1,kl=0;function nn(t){t!==ni&&t.next===null&&(ni===null?Ou=ni=t:ni=ni.next=t),Ru=!0,bs||(bs=!0,NS())}function ua(t,e){if(!Dc&&Ru){Dc=!0;do for(var n=!1,l=Ou;l!==null;){if(!e)if(t!==0){var i=l.pendingLanes;if(i===0)var r=0;else{var a=l.suspendedLanes,u=l.pingedLanes;r=(1<<31-ke(42|t)+1)-1,r&=i&~(a&~u),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,Kh(l,r))}else r=lt,r=ju(l,l===dt?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),!(r&3)||Jr(l,r)||(n=!0,Kh(l,r));l=l.next}while(n);Dc=!1}}function _S(){Oy()}function Oy(){Ru=bs=!1;var t=0;kl!==0&&(jS()&&(t=kl),kl=0);for(var e=$e(),n=null,l=Ou;l!==null;){var i=l.next,r=Ry(l,e);r===0?(l.next=null,n===null?Ou=i:n.next=i,i===null&&(ni=n)):(n=l,(t!==0||r&3)&&(Ru=!0)),l=i}ua(t,!1)}function Ry(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,i=t.expirationTimes,r=t.pendingLanes&-62914561;0<r;){var a=31-ke(r),u=1<<a,o=i[a];o===-1?(!(u&n)||u&l)&&(i[a]=rv(u,e)):o<=e&&(t.expiredLanes|=u),r&=~u}if(e=dt,n=lt,n=ju(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(at===2||at===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&lc(l),t.callbackNode=null,t.callbackPriority=0;if(!(n&3)||Jr(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&lc(l),_s(n)){case 2:case 8:n=kd;break;case 32:n=pu;break;case 268435456:n=wd;break;default:n=pu}return l=_y.bind(null,t),n=Os(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&lc(l),t.callbackPriority=2,t.callbackNode=null,2}function _y(t,e){if(Kt!==0&&Kt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(Wu(!0)&&t.callbackNode!==n)return null;var l=lt;return l=ju(t,t===dt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(xy(t,l,e),Ry(t,$e()),t.callbackNode!=null&&t.callbackNode===n?_y.bind(null,t):null)}function Kh(t,e){if(Wu())return null;xy(t,e,!0)}function NS(){GS(function(){ut&6?Os(Sd,_S):Oy()})}function gf(){return kl===0&&(kl=Ed()),kl}function Ih(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Wa(""+t)}function Jh(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function LS(t,e,n,l,i){if(e==="submit"&&n&&n.stateNode===i){var r=Ih((i[me]||null).action),a=l.submitter;a&&(e=(e=a[me]||null)?Ih(e.formAction):a.getAttribute("formAction"),e!==null&&(r=e,a=null));var u=new Yu("action","action",null,l,i);t.push({event:u,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(kl!==0){var o=a?Jh(i,a):new FormData(i);us(n,{pending:!0,data:o,method:i.method,action:r},null,o)}}else typeof r=="function"&&(u.preventDefault(),o=a?Jh(i,a):new FormData(i),us(n,{pending:!0,data:o,method:i.method,action:r},r,o))},currentTarget:i}]})}}for(Za=0;Za<Ic.length;Za++)Fa=Ic[Za],Ph=Fa.toLowerCase(),Wh=Fa[0].toUpperCase()+Fa.slice(1),Xe(Ph,"on"+Wh);var Fa,Ph,Wh,Za;Xe(Kd,"onAnimationEnd");Xe(Id,"onAnimationIteration");Xe(Jd,"onAnimationStart");Xe("dblclick","onDoubleClick");Xe("focusin","onFocus");Xe("focusout","onBlur");Xe(tS,"onTransitionRun");Xe(eS,"onTransitionStart");Xe(nS,"onTransitionCancel");Xe(Pd,"onTransitionEnd");Ei("onMouseEnter",["mouseout","mouseover"]);Ei("onMouseLeave",["mouseout","mouseover"]);Ei("onPointerEnter",["pointerout","pointerover"]);Ei("onPointerLeave",["pointerout","pointerover"]);zl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));zl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));zl("onBeforeInput",["compositionend","keypress","textInput","paste"]);zl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));zl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));zl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Yr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),US=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Yr));function Ny(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],i=l.event;l=l.listeners;t:{var r=void 0;if(e)for(var a=l.length-1;0<=a;a--){var u=l[a],o=u.instance,c=u.currentTarget;if(u=u.listener,o!==r&&i.isPropagationStopped())break t;r=u,i.currentTarget=c;try{r(i)}catch(s){Tu(s)}i.currentTarget=null,r=o}else for(a=0;a<l.length;a++){if(u=l[a],o=u.instance,c=u.currentTarget,u=u.listener,o!==r&&i.isPropagationStopped())break t;r=u,i.currentTarget=c;try{r(i)}catch(s){Tu(s)}i.currentTarget=null,r=o}}}}function $(t,e){var n=e[Gc];n===void 0&&(n=e[Gc]=new Set);var l=t+"__bubble";n.has(l)||(Ly(e,t,2,!1),n.add(l))}function Oc(t,e,n){var l=0;e&&(l|=4),Ly(n,t,l,e)}var Ka="_reactListening"+Math.random().toString(36).slice(2);function yf(t){if(!t[Ka]){t[Ka]=!0,Md.forEach(function(n){n!=="selectionchange"&&(US.has(n)||Oc(n,!1,t),Oc(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ka]||(e[Ka]=!0,Oc("selectionchange",!1,e))}}function Ly(t,e,n,l){switch(Fy(e)){case 2:var i=ck;break;case 8:i=sk;break;default:i=Sf}n=i.bind(null,e,n,t),i=void 0,!Zc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(i=!0),l?i!==void 0?t.addEventListener(e,n,{capture:!0,passive:i}):t.addEventListener(e,n,!0):i!==void 0?t.addEventListener(e,n,{passive:i}):t.addEventListener(e,n,!1)}function Rc(t,e,n,l,i){var r=l;if(!(e&1)&&!(e&2)&&l!==null)t:for(;;){if(l===null)return;var a=l.tag;if(a===3||a===4){var u=l.stateNode.containerInfo;if(u===i)break;if(a===4)for(a=l.return;a!==null;){var o=a.tag;if((o===3||o===4)&&a.stateNode.containerInfo===i)return;a=a.return}for(;u!==null;){if(a=ri(u),a===null)return;if(o=a.tag,o===5||o===6||o===26||o===27){l=r=a;continue t}u=u.parentNode}}l=l.return}Bd(function(){var c=r,s=Us(n),f=[];t:{var p=Wd.get(t);if(p!==void 0){var m=Yu,y=t;switch(t){case"keypress":if(tu(n)===0)break t;case"keydown":case"keyup":m=Rv;break;case"focusin":y="focus",m=fc;break;case"focusout":y="blur",m=fc;break;case"beforeblur":case"afterblur":m=fc;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=ih;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=vv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=Lv;break;case Kd:case Id:case Jd:m=wv;break;case Pd:m=Bv;break;case"scroll":case"scrollend":m=bv;break;case"wheel":m=qv;break;case"copy":case"cut":case"paste":m=Tv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=ah;break;case"toggle":case"beforetoggle":m=Yv}var v=(e&4)!==0,E=!v&&(t==="scroll"||t==="scrollend"),h=v?p!==null?p+"Capture":null:p;v=[];for(var d=c,g;d!==null;){var k=d;if(g=k.stateNode,k=k.tag,k!==5&&k!==26&&k!==27||g===null||h===null||(k=Lr(d,h),k!=null&&v.push(Gr(d,k,g))),E)break;d=d.return}0<v.length&&(p=new m(p,y,null,n,s),f.push({event:p,listeners:v}))}}if(!(e&7)){t:{if(p=t==="mouseover"||t==="pointerover",m=t==="mouseout"||t==="pointerout",p&&n!==Qc&&(y=n.relatedTarget||n.fromElement)&&(ri(y)||y[Ni]))break t;if((m||p)&&(p=s.window===s?s:(p=s.ownerDocument)?p.defaultView||p.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=c,y=y?ri(y):null,y!==null&&(E=Kr(y),v=y.tag,y!==E||v!==5&&v!==27&&v!==6)&&(y=null)):(m=null,y=c),m!==y)){if(v=ih,k="onMouseLeave",h="onMouseEnter",d="mouse",(t==="pointerout"||t==="pointerover")&&(v=ah,k="onPointerLeave",h="onPointerEnter",d="pointer"),E=m==null?p:br(m),g=y==null?p:br(y),p=new v(k,d+"leave",m,n,s),p.target=E,p.relatedTarget=g,k=null,ri(s)===c&&(v=new v(h,d+"enter",y,n,s),v.target=g,v.relatedTarget=E,k=v),E=k,m&&y)e:{for(v=m,h=y,d=0,g=v;g;g=$l(g))d++;for(g=0,k=h;k;k=$l(k))g++;for(;0<d-g;)v=$l(v),d--;for(;0<g-d;)h=$l(h),g--;for(;d--;){if(v===h||h!==null&&v===h.alternate)break e;v=$l(v),h=$l(h)}v=null}else v=null;m!==null&&$h(f,p,m,v,!1),y!==null&&E!==null&&$h(f,E,y,v,!0)}}t:{if(p=c?br(c):window,m=p.nodeName&&p.nodeName.toLowerCase(),m==="select"||m==="input"&&p.type==="file")var C=sh;else if(ch(p))if(Vd)C=Pv;else{C=Iv;var w=Kv}else m=p.nodeName,!m||m.toLowerCase()!=="input"||p.type!=="checkbox"&&p.type!=="radio"?c&&Ls(c.elementType)&&(C=sh):C=Jv;if(C&&(C=C(t,c))){Gd(f,C,n,s);break t}w&&w(t,p,c),t==="focusout"&&c&&p.type==="number"&&c.memoizedProps.value!=null&&Xc(p,"number",p.value)}switch(w=c?br(c):window,t){case"focusin":(ch(w)||w.contentEditable==="true")&&(oi=w,Fc=c,kr=null);break;case"focusout":kr=Fc=oi=null;break;case"mousedown":Kc=!0;break;case"contextmenu":case"mouseup":case"dragend":Kc=!1,hh(f,n,s);break;case"selectionchange":if($v)break;case"keydown":case"keyup":hh(f,n,s)}var A;if(qs)t:{switch(t){case"compositionstart":var T="onCompositionStart";break t;case"compositionend":T="onCompositionEnd";break t;case"compositionupdate":T="onCompositionUpdate";break t}T=void 0}else ui?jd(t,n)&&(T="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(qd&&n.locale!=="ko"&&(ui||T!=="onCompositionStart"?T==="onCompositionEnd"&&ui&&(A=Hd()):(Yn=s,Bs="value"in Yn?Yn.value:Yn.textContent,ui=!0)),w=_u(c,T),0<w.length&&(T=new rh(T,t,null,n,s),f.push({event:T,listeners:w}),A?T.data=A:(A=Yd(n),A!==null&&(T.data=A)))),(A=Vv?Xv(t,n):Qv(t,n))&&(T=_u(c,"onBeforeInput"),0<T.length&&(w=new rh("onBeforeInput","beforeinput",null,n,s),f.push({event:w,listeners:T}),w.data=A)),LS(f,t,c,n,s)}Ny(f,e)})}function Gr(t,e,n){return{instance:t,listener:e,currentTarget:n}}function _u(t,e){for(var n=e+"Capture",l=[];t!==null;){var i=t,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=Lr(t,n),i!=null&&l.unshift(Gr(t,i,r)),i=Lr(t,e),i!=null&&l.push(Gr(t,i,r))),t.tag===3)return l;t=t.return}return[]}function $l(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function $h(t,e,n,l,i){for(var r=e._reactName,a=[];n!==null&&n!==l;){var u=n,o=u.alternate,c=u.stateNode;if(u=u.tag,o!==null&&o===l)break;u!==5&&u!==26&&u!==27||c===null||(o=c,i?(c=Lr(n,r),c!=null&&a.unshift(Gr(n,c,o))):i||(c=Lr(n,r),c!=null&&a.push(Gr(n,c,o)))),n=n.return}a.length!==0&&t.push({event:e,listeners:a})}var BS=/\r\n?/g,HS=/\u0000|\uFFFD/g;function td(t){return(typeof t=="string"?t:""+t).replace(BS,`
`).replace(HS,"")}function Uy(t,e){return e=td(e),td(t)===e}function $u(){}function st(t,e,n,l,i,r){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||Ti(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&Ti(t,""+l);break;case"className":Ba(t,"class",l);break;case"tabIndex":Ba(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ba(t,n,l);break;case"style":Ud(t,l,r);break;case"data":if(e!=="object"){Ba(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Wa(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(e!=="input"&&st(t,e,"name",i.name,i,null),st(t,e,"formEncType",i.formEncType,i,null),st(t,e,"formMethod",i.formMethod,i,null),st(t,e,"formTarget",i.formTarget,i,null)):(st(t,e,"encType",i.encType,i,null),st(t,e,"method",i.method,i,null),st(t,e,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=Wa(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=$u);break;case"onScroll":l!=null&&$("scroll",t);break;case"onScrollEnd":l!=null&&$("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(D(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(D(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=Wa(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":$("beforetoggle",t),$("toggle",t),Pa(t,"popover",l);break;case"xlinkActuate":sn(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":sn(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":sn(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":sn(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":sn(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":sn(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":sn(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":sn(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":sn(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Pa(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=gv.get(n)||n,Pa(t,n,l))}}function xs(t,e,n,l,i,r){switch(n){case"style":Ud(t,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(D(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(D(60));t.innerHTML=n}}break;case"children":typeof l=="string"?Ti(t,l):(typeof l=="number"||typeof l=="bigint")&&Ti(t,""+l);break;case"onScroll":l!=null&&$("scroll",t);break;case"onScrollEnd":l!=null&&$("scrollend",t);break;case"onClick":l!=null&&(t.onclick=$u);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Dd.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),e=n.slice(2,i?n.length-7:void 0),r=t[me]||null,r=r!=null?r[n]:null,typeof r=="function"&&t.removeEventListener(e,r,i),typeof l=="function")){typeof r!="function"&&r!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,i);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Pa(t,n,l)}}}function It(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":$("error",t),$("load",t);var l=!1,i=!1,r;for(r in n)if(n.hasOwnProperty(r)){var a=n[r];if(a!=null)switch(r){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(D(137,e));default:st(t,e,r,a,n,null)}}i&&st(t,e,"srcSet",n.srcSet,n,null),l&&st(t,e,"src",n.src,n,null);return;case"input":$("invalid",t);var u=r=a=i=null,o=null,c=null;for(l in n)if(n.hasOwnProperty(l)){var s=n[l];if(s!=null)switch(l){case"name":i=s;break;case"type":a=s;break;case"checked":o=s;break;case"defaultChecked":c=s;break;case"value":r=s;break;case"defaultValue":u=s;break;case"children":case"dangerouslySetInnerHTML":if(s!=null)throw Error(D(137,e));break;default:st(t,e,l,s,n,null)}}_d(t,r,u,o,c,a,i,!1),hu(t);return;case"select":$("invalid",t),l=a=r=null;for(i in n)if(n.hasOwnProperty(i)&&(u=n[i],u!=null))switch(i){case"value":r=u;break;case"defaultValue":a=u;break;case"multiple":l=u;default:st(t,e,i,u,n,null)}e=r,n=a,t.multiple=!!l,e!=null?gi(t,!!l,e,!1):n!=null&&gi(t,!!l,n,!0);return;case"textarea":$("invalid",t),r=i=l=null;for(a in n)if(n.hasOwnProperty(a)&&(u=n[a],u!=null))switch(a){case"value":l=u;break;case"defaultValue":i=u;break;case"children":r=u;break;case"dangerouslySetInnerHTML":if(u!=null)throw Error(D(91));break;default:st(t,e,a,u,n,null)}Ld(t,l,i,r),hu(t);return;case"option":for(o in n)if(n.hasOwnProperty(o)&&(l=n[o],l!=null))switch(o){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:st(t,e,o,l,n,null)}return;case"dialog":$("beforetoggle",t),$("toggle",t),$("cancel",t),$("close",t);break;case"iframe":case"object":$("load",t);break;case"video":case"audio":for(l=0;l<Yr.length;l++)$(Yr[l],t);break;case"image":$("error",t),$("load",t);break;case"details":$("toggle",t);break;case"embed":case"source":case"link":$("error",t),$("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&(l=n[c],l!=null))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(D(137,e));default:st(t,e,c,l,n,null)}return;default:if(Ls(e)){for(s in n)n.hasOwnProperty(s)&&(l=n[s],l!==void 0&&xs(t,e,s,l,n,void 0));return}}for(u in n)n.hasOwnProperty(u)&&(l=n[u],l!=null&&st(t,e,u,l,n,null))}function qS(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,a=null,u=null,o=null,c=null,s=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&f!=null)switch(m){case"checked":break;case"value":break;case"defaultValue":o=f;default:l.hasOwnProperty(m)||st(t,e,m,null,l,f)}}for(var p in l){var m=l[p];if(f=n[p],l.hasOwnProperty(p)&&(m!=null||f!=null))switch(p){case"type":r=m;break;case"name":i=m;break;case"checked":c=m;break;case"defaultChecked":s=m;break;case"value":a=m;break;case"defaultValue":u=m;break;case"children":case"dangerouslySetInnerHTML":if(m!=null)throw Error(D(137,e));break;default:m!==f&&st(t,e,p,m,l,f)}}Vc(t,a,u,o,c,s,r,i);return;case"select":m=a=u=p=null;for(r in n)if(o=n[r],n.hasOwnProperty(r)&&o!=null)switch(r){case"value":break;case"multiple":m=o;default:l.hasOwnProperty(r)||st(t,e,r,null,l,o)}for(i in l)if(r=l[i],o=n[i],l.hasOwnProperty(i)&&(r!=null||o!=null))switch(i){case"value":p=r;break;case"defaultValue":u=r;break;case"multiple":a=r;default:r!==o&&st(t,e,i,r,l,o)}e=u,n=a,l=m,p!=null?gi(t,!!n,p,!1):!!l!=!!n&&(e!=null?gi(t,!!n,e,!0):gi(t,!!n,n?[]:"",!1));return;case"textarea":m=p=null;for(u in n)if(i=n[u],n.hasOwnProperty(u)&&i!=null&&!l.hasOwnProperty(u))switch(u){case"value":break;case"children":break;default:st(t,e,u,null,l,i)}for(a in l)if(i=l[a],r=n[a],l.hasOwnProperty(a)&&(i!=null||r!=null))switch(a){case"value":p=i;break;case"defaultValue":m=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(D(91));break;default:i!==r&&st(t,e,a,i,l,r)}Nd(t,p,m);return;case"option":for(var y in n)if(p=n[y],n.hasOwnProperty(y)&&p!=null&&!l.hasOwnProperty(y))switch(y){case"selected":t.selected=!1;break;default:st(t,e,y,null,l,p)}for(o in l)if(p=l[o],m=n[o],l.hasOwnProperty(o)&&p!==m&&(p!=null||m!=null))switch(o){case"selected":t.selected=p&&typeof p!="function"&&typeof p!="symbol";break;default:st(t,e,o,p,l,m)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var v in n)p=n[v],n.hasOwnProperty(v)&&p!=null&&!l.hasOwnProperty(v)&&st(t,e,v,null,l,p);for(c in l)if(p=l[c],m=n[c],l.hasOwnProperty(c)&&p!==m&&(p!=null||m!=null))switch(c){case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(D(137,e));break;default:st(t,e,c,p,l,m)}return;default:if(Ls(e)){for(var E in n)p=n[E],n.hasOwnProperty(E)&&p!==void 0&&!l.hasOwnProperty(E)&&xs(t,e,E,void 0,l,p);for(s in l)p=l[s],m=n[s],!l.hasOwnProperty(s)||p===m||p===void 0&&m===void 0||xs(t,e,s,p,l,m);return}}for(var h in n)p=n[h],n.hasOwnProperty(h)&&p!=null&&!l.hasOwnProperty(h)&&st(t,e,h,null,l,p);for(f in l)p=l[f],m=n[f],!l.hasOwnProperty(f)||p===m||p==null&&m==null||st(t,e,f,p,l,m)}var vs=null,Ss=null;function Nu(t){return t.nodeType===9?t:t.ownerDocument}function ed(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function By(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ks(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var _c=null;function jS(){var t=window.event;return t&&t.type==="popstate"?t===_c?!1:(_c=t,!0):(_c=null,!1)}var Hy=typeof setTimeout=="function"?setTimeout:void 0,YS=typeof clearTimeout=="function"?clearTimeout:void 0,nd=typeof Promise=="function"?Promise:void 0,GS=typeof queueMicrotask=="function"?queueMicrotask:typeof nd!="undefined"?function(t){return nd.resolve(null).then(t).catch(VS)}:Hy;function VS(t){setTimeout(function(){throw t})}function nl(t){return t==="head"}function ld(t,e){var n=e,l=0,i=0;do{var r=n.nextSibling;if(t.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var a=t.ownerDocument;if(n&1&&_r(a.documentElement),n&2&&_r(a.body),n&4)for(n=a.head,_r(n),a=n.firstChild;a;){var u=a.nextSibling,o=a.nodeName;a[Wr]||o==="SCRIPT"||o==="STYLE"||o==="LINK"&&a.rel.toLowerCase()==="stylesheet"||n.removeChild(a),a=u}}if(i===0){t.removeChild(r),Fr(e);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);Fr(e)}function ws(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ws(n),Ns(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function XS(t,e,n,l){for(;t.nodeType===1;){var i=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[Wr])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(r=t.getAttribute("rel"),r==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(r!==i.rel||t.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||t.getAttribute("title")!==(i.title==null?null:i.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(r=t.getAttribute("src"),(r!==(i.src==null?null:i.src)||t.getAttribute("type")!==(i.type==null?null:i.type)||t.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&t.getAttribute("name")===r)return t}else return t;if(t=Ve(t.nextSibling),t===null)break}return null}function QS(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=Ve(t.nextSibling),t===null))return null;return t}function Es(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function ZS(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function Ve(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Ts=null;function id(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function qy(t,e,n){switch(e=Nu(n),t){case"html":if(t=e.documentElement,!t)throw Error(D(452));return t;case"head":if(t=e.head,!t)throw Error(D(453));return t;case"body":if(t=e.body,!t)throw Error(D(454));return t;default:throw Error(D(451))}}function _r(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Ns(t)}var Ue=new Map,rd=new Set;function Lu(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Tn=rt.d;rt.d={f:FS,r:KS,D:IS,C:JS,L:PS,m:WS,X:tk,S:$S,M:ek};function FS(){var t=Tn.f(),e=Ju();return t||e}function KS(t){var e=Li(t);e!==null&&e.tag===5&&e.type==="form"?Rg(e):Tn.r(t)}var Hi=typeof document=="undefined"?null:document;function jy(t,e,n){var l=Hi;if(l&&typeof e=="string"&&e){var i=Re(e);i='link[rel="'+t+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),rd.has(i)||(rd.add(i),t={rel:t,crossOrigin:n,href:e},l.querySelector(i)===null&&(e=l.createElement("link"),It(e,"link",t),Gt(e),l.head.appendChild(e)))}}function IS(t){Tn.D(t),jy("dns-prefetch",t,null)}function JS(t,e){Tn.C(t,e),jy("preconnect",t,e)}function PS(t,e,n){Tn.L(t,e,n);var l=Hi;if(l&&t&&e){var i='link[rel="preload"][as="'+Re(e)+'"]';e==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Re(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Re(n.imageSizes)+'"]')):i+='[href="'+Re(t)+'"]';var r=i;switch(e){case"style":r=_i(t);break;case"script":r=qi(t)}Ue.has(r)||(t=gt({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Ue.set(r,t),l.querySelector(i)!==null||e==="style"&&l.querySelector(oa(r))||e==="script"&&l.querySelector(ca(r))||(e=l.createElement("link"),It(e,"link",t),Gt(e),l.head.appendChild(e)))}}function WS(t,e){Tn.m(t,e);var n=Hi;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",i='link[rel="modulepreload"][as="'+Re(l)+'"][href="'+Re(t)+'"]',r=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=qi(t)}if(!Ue.has(r)&&(t=gt({rel:"modulepreload",href:t},e),Ue.set(r,t),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(ca(r)))return}l=n.createElement("link"),It(l,"link",t),Gt(l),n.head.appendChild(l)}}}function $S(t,e,n){Tn.S(t,e,n);var l=Hi;if(l&&t){var i=di(l).hoistableStyles,r=_i(t);e=e||"default";var a=i.get(r);if(!a){var u={loading:0,preload:null};if(a=l.querySelector(oa(r)))u.loading=5;else{t=gt({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Ue.get(r))&&bf(t,n);var o=a=l.createElement("link");Gt(o),It(o,"link",t),o._p=new Promise(function(c,s){o.onload=c,o.onerror=s}),o.addEventListener("load",function(){u.loading|=1}),o.addEventListener("error",function(){u.loading|=2}),u.loading|=4,uu(a,e,l)}a={type:"stylesheet",instance:a,count:1,state:u},i.set(r,a)}}}function tk(t,e){Tn.X(t,e);var n=Hi;if(n&&t){var l=di(n).hoistableScripts,i=qi(t),r=l.get(i);r||(r=n.querySelector(ca(i)),r||(t=gt({src:t,async:!0},e),(e=Ue.get(i))&&xf(t,e),r=n.createElement("script"),Gt(r),It(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function ek(t,e){Tn.M(t,e);var n=Hi;if(n&&t){var l=di(n).hoistableScripts,i=qi(t),r=l.get(i);r||(r=n.querySelector(ca(i)),r||(t=gt({src:t,async:!0,type:"module"},e),(e=Ue.get(i))&&xf(t,e),r=n.createElement("script"),Gt(r),It(r,"link",t),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function ad(t,e,n,l){var i=(i=Xn.current)?Lu(i):null;if(!i)throw Error(D(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=_i(n.href),n=di(i).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=_i(n.href);var r=di(i).hoistableStyles,a=r.get(t);if(a||(i=i.ownerDocument||i,a={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(t,a),(r=i.querySelector(oa(t)))&&!r._p&&(a.instance=r,a.state.loading=5),Ue.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Ue.set(t,n),r||nk(i,t,n,a.state))),e&&l===null)throw Error(D(528,""));return a}if(e&&l!==null)throw Error(D(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=qi(n),n=di(i).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(D(444,t))}}function _i(t){return'href="'+Re(t)+'"'}function oa(t){return'link[rel="stylesheet"]['+t+"]"}function Yy(t){return gt({},t,{"data-precedence":t.precedence,precedence:null})}function nk(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),It(e,"link",n),Gt(e),t.head.appendChild(e))}function qi(t){return'[src="'+Re(t)+'"]'}function ca(t){return"script[async]"+t}function ud(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+Re(n.href)+'"]');if(l)return e.instance=l,Gt(l),l;var i=gt({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Gt(l),It(l,"style",i),uu(l,n.precedence,t),e.instance=l;case"stylesheet":i=_i(n.href);var r=t.querySelector(oa(i));if(r)return e.state.loading|=4,e.instance=r,Gt(r),r;l=Yy(n),(i=Ue.get(i))&&bf(l,i),r=(t.ownerDocument||t).createElement("link"),Gt(r);var a=r;return a._p=new Promise(function(u,o){a.onload=u,a.onerror=o}),It(r,"link",l),e.state.loading|=4,uu(r,n.precedence,t),e.instance=r;case"script":return r=qi(n.src),(i=t.querySelector(ca(r)))?(e.instance=i,Gt(i),i):(l=n,(i=Ue.get(r))&&(l=gt({},n),xf(l,i)),t=t.ownerDocument||t,i=t.createElement("script"),Gt(i),It(i,"link",l),t.head.appendChild(i),e.instance=i);case"void":return null;default:throw Error(D(443,e.type))}else e.type==="stylesheet"&&!(e.state.loading&4)&&(l=e.instance,e.state.loading|=4,uu(l,n.precedence,t));return e.instance}function uu(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,r=i,a=0;a<l.length;a++){var u=l[a];if(u.dataset.precedence===e)r=u;else if(r!==i)break}r?r.parentNode.insertBefore(t,r.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function bf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function xf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var ou=null;function od(t,e,n){if(ou===null){var l=new Map,i=ou=new Map;i.set(n,l)}else i=ou,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),i=0;i<n.length;i++){var r=n[i];if(!(r[Wr]||r[Wt]||t==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var a=r.getAttribute(e)||"";a=t+a;var u=l.get(a);u?u.push(r):l.set(a,[r])}}return l}function cd(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function lk(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Gy(t){return!(t.type==="stylesheet"&&!(t.state.loading&3))}var Vr=null;function ik(){}function rk(t,e,n){if(Vr===null)throw Error(D(475));var l=Vr;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&!(e.state.loading&4)){if(e.instance===null){var i=_i(n.href),r=t.querySelector(oa(i));if(r){t=r._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=Uu.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=r,Gt(r);return}r=t.ownerDocument||t,n=Yy(n),(i=Ue.get(i))&&bf(n,i),r=r.createElement("link"),Gt(r);var a=r;a._p=new Promise(function(u,o){a.onload=u,a.onerror=o}),It(r,"link",n),e.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&!(e.state.loading&3)&&(l.count++,e=Uu.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function ak(){if(Vr===null)throw Error(D(475));var t=Vr;return t.stylesheets&&t.count===0&&As(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&As(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Uu(){if(this.count--,this.count===0){if(this.stylesheets)As(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Bu=null;function As(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Bu=new Map,e.forEach(uk,t),Bu=null,Uu.call(t))}function uk(t,e){if(!(e.state.loading&4)){var n=Bu.get(t);if(n)var l=n.get(null);else{n=new Map,Bu.set(t,n);for(var i=t.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var a=i[r];(a.nodeName==="LINK"||a.getAttribute("media")!=="not all")&&(n.set(a.dataset.precedence,a),l=a)}l&&n.set(null,l)}i=e.instance,a=i.getAttribute("data-precedence"),r=n.get(a)||l,r===l&&n.set(null,i),n.set(a,i),this.count++,l=Uu.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),r?r.parentNode.insertBefore(i,r.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(i,t.firstChild)),e.state.loading|=4}}var Xr={$$typeof:hn,Provider:null,Consumer:null,_currentValue:gl,_currentValue2:gl,_threadCount:0};function ok(t,e,n,l,i,r,a,u){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ic(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ic(0),this.hiddenUpdates=ic(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=a,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=u,this.incompleteTransitions=new Map}function Vy(t,e,n,l,i,r,a,u,o,c,s,f){return t=new ok(t,e,n,a,u,o,c,f),e=1,r===!0&&(e|=24),r=ve(3,null,null,e),t.current=r,r.stateNode=t,e=Zs(),e.refCount++,t.pooledCache=e,e.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:e},Ks(r),t}function Xy(t){return t?(t=fi,t):fi}function Qy(t,e,n,l,i,r){i=Xy(i),l.context===null?l.context=i:l.pendingContext=i,l=Qn(e),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=Zn(t,l,e),n!==null&&(Ee(n,t,e),Tr(n,t,e))}function sd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function vf(t,e){sd(t,e),(t=t.alternate)&&sd(t,e)}function Zy(t){if(t.tag===13){var e=Ui(t,67108864);e!==null&&Ee(e,t,67108864),vf(t,67108864)}}var Hu=!0;function ck(t,e,n,l){var i=V.T;V.T=null;var r=rt.p;try{rt.p=2,Sf(t,e,n,l)}finally{rt.p=r,V.T=i}}function sk(t,e,n,l){var i=V.T;V.T=null;var r=rt.p;try{rt.p=8,Sf(t,e,n,l)}finally{rt.p=r,V.T=i}}function Sf(t,e,n,l){if(Hu){var i=zs(l);if(i===null)Rc(t,e,l,qu,n),fd(t,l);else if(mk(i,t,e,n,l))l.stopPropagation();else if(fd(t,l),e&4&&-1<fk.indexOf(t)){for(;i!==null;){var r=Li(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var a=pl(r.pendingLanes);if(a!==0){var u=r;for(u.pendingLanes|=2,u.entangledLanes|=2;a;){var o=1<<31-ke(a);u.entanglements[1]|=o,a&=~o}nn(r),!(ut&6)&&(Mu=$e()+500,ua(0,!1))}}break;case 13:u=Ui(r,2),u!==null&&Ee(u,r,2),Ju(),vf(r,2)}if(r=zs(l),r===null&&Rc(t,e,l,qu,n),r===i)break;i=r}i!==null&&l.stopPropagation()}else Rc(t,e,l,null,n)}}function zs(t){return t=Us(t),kf(t)}var qu=null;function kf(t){if(qu=null,t=ri(t),t!==null){var e=Kr(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=yd(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return qu=t,null}function Fy(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Wx()){case Sd:return 2;case kd:return 8;case pu:case $x:return 32;case wd:return 268435456;default:return 32}default:return 32}}var Cs=!1,In=null,Jn=null,Pn=null,Qr=new Map,Zr=new Map,qn=[],fk="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function fd(t,e){switch(t){case"focusin":case"focusout":In=null;break;case"dragenter":case"dragleave":Jn=null;break;case"mouseover":case"mouseout":Pn=null;break;case"pointerover":case"pointerout":Qr.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zr.delete(e.pointerId)}}function hr(t,e,n,l,i,r){return t===null||t.nativeEvent!==r?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[i]},e!==null&&(e=Li(e),e!==null&&Zy(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,i!==null&&e.indexOf(i)===-1&&e.push(i),t)}function mk(t,e,n,l,i){switch(e){case"focusin":return In=hr(In,t,e,n,l,i),!0;case"dragenter":return Jn=hr(Jn,t,e,n,l,i),!0;case"mouseover":return Pn=hr(Pn,t,e,n,l,i),!0;case"pointerover":var r=i.pointerId;return Qr.set(r,hr(Qr.get(r)||null,t,e,n,l,i)),!0;case"gotpointercapture":return r=i.pointerId,Zr.set(r,hr(Zr.get(r)||null,t,e,n,l,i)),!0}return!1}function Ky(t){var e=ri(t.target);if(e!==null){var n=Kr(e);if(n!==null){if(e=n.tag,e===13){if(e=yd(n),e!==null){t.blockedOn=e,uv(t.priority,function(){if(n.tag===13){var l=we();l=Rs(l);var i=Ui(n,l);i!==null&&Ee(i,n,l),vf(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function cu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=zs(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);Qc=l,n.target.dispatchEvent(l),Qc=null}else return e=Li(n),e!==null&&Zy(e),t.blockedOn=n,!1;e.shift()}return!0}function md(t,e,n){cu(t)&&n.delete(e)}function pk(){Cs=!1,In!==null&&cu(In)&&(In=null),Jn!==null&&cu(Jn)&&(Jn=null),Pn!==null&&cu(Pn)&&(Pn=null),Qr.forEach(md),Zr.forEach(md)}function Ia(t,e){t.blockedOn===e&&(t.blockedOn=null,Cs||(Cs=!0,Bt.unstable_scheduleCallback(Bt.unstable_NormalPriority,pk)))}var Ja=null;function pd(t){Ja!==t&&(Ja=t,Bt.unstable_scheduleCallback(Bt.unstable_NormalPriority,function(){Ja===t&&(Ja=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],i=t[e+2];if(typeof l!="function"){if(kf(l||n)===null)continue;break}var r=Li(n);r!==null&&(t.splice(e,3),e-=3,us(r,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Fr(t){function e(o){return Ia(o,t)}In!==null&&Ia(In,t),Jn!==null&&Ia(Jn,t),Pn!==null&&Ia(Pn,t),Qr.forEach(e),Zr.forEach(e);for(var n=0;n<qn.length;n++){var l=qn[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<qn.length&&(n=qn[0],n.blockedOn===null);)Ky(n),n.blockedOn===null&&qn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],r=n[l+1],a=i[me]||null;if(typeof r=="function")a||pd(n);else if(a){var u=null;if(r&&r.hasAttribute("formAction")){if(i=r,a=r[me]||null)u=a.formAction;else if(kf(i)!==null)continue}else u=a.action;typeof u=="function"?n[l+1]=u:(n.splice(l,3),l-=3),pd(n)}}}function wf(t){this._internalRoot=t}to.prototype.render=wf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(D(409));var n=e.current,l=we();Qy(n,l,t,e,null,null)};to.prototype.unmount=wf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Qy(t.current,2,null,t,null,null),Ju(),e[Ni]=null}};function to(t){this._internalRoot=t}to.prototype.unstable_scheduleHydration=function(t){if(t){var e=Cd();t={blockedOn:null,target:t,priority:e};for(var n=0;n<qn.length&&e!==0&&e<qn[n].priority;n++);qn.splice(n,0,t),n===0&&Ky(t)}};var hd=dd.version;if(hd!=="19.1.0")throw Error(D(527,hd,"19.1.0"));rt.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(D(188)):(t=Object.keys(t).join(","),Error(D(268,t)));return t=Qx(e),t=t!==null?bd(t):null,t=t===null?null:t.stateNode,t};var hk={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:V,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&(dr=__REACT_DEVTOOLS_GLOBAL_HOOK__,!dr.isDisabled&&dr.supportsFiber))try{Ir=dr.inject(hk),Se=dr}catch(t){}var dr;eo.createRoot=function(t,e){if(!gd(t))throw Error(D(299));var n=!1,l="",i=Vg,r=Xg,a=Qg,u=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(i=e.onUncaughtError),e.onCaughtError!==void 0&&(r=e.onCaughtError),e.onRecoverableError!==void 0&&(a=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(u=e.unstable_transitionCallbacks)),e=Vy(t,1,!1,null,null,n,l,i,r,a,u,null),t[Ni]=e.current,yf(t),new wf(e)};eo.hydrateRoot=function(t,e,n){if(!gd(t))throw Error(D(299));var l=!1,i="",r=Vg,a=Xg,u=Qg,o=null,c=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(a=n.onCaughtError),n.onRecoverableError!==void 0&&(u=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(o=n.unstable_transitionCallbacks),n.formState!==void 0&&(c=n.formState)),e=Vy(t,1,!0,e,n!=null?n:null,l,i,r,a,u,o,c),e.context=Xy(null),n=e.current,l=we(),l=Rs(l),i=Qn(l),i.callback=null,Zn(n,i,l),n=l,e.current.lanes=n,Pr(e,n),nn(e),t[Ni]=e.current,yf(t),new to(e)};eo.version="19.1.0"});var Wy=le((LA,Py)=>{"use strict";function Jy(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Jy)}catch(t){console.error(t)}}Jy(),Py.exports=Iy()});var m1=le((jz,f1)=>{var u1=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,Ek=/\n/g,Tk=/^\s*/,Ak=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,zk=/^:\s*/,Ck=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,Mk=/^[;\s]*/,Dk=/^\s+|\s+$/g,Ok=`
`,o1="/",c1="*",_l="",Rk="comment",_k="declaration";f1.exports=function(t,e){if(typeof t!="string")throw new TypeError("First argument must be a string");if(!t)return[];e=e||{};var n=1,l=1;function i(v){var E=v.match(Ek);E&&(n+=E.length);var h=v.lastIndexOf(Ok);l=~h?v.length-h:l+v.length}function r(){var v={line:n,column:l};return function(E){return E.position=new a(v),s(),E}}function a(v){this.start=v,this.end={line:n,column:l},this.source=e.source}a.prototype.content=t;var u=[];function o(v){var E=new Error(e.source+":"+n+":"+l+": "+v);if(E.reason=v,E.filename=e.source,E.line=n,E.column=l,E.source=t,e.silent)u.push(E);else throw E}function c(v){var E=v.exec(t);if(E){var h=E[0];return i(h),t=t.slice(h.length),E}}function s(){c(Tk)}function f(v){var E;for(v=v||[];E=p();)E!==!1&&v.push(E);return v}function p(){var v=r();if(!(o1!=t.charAt(0)||c1!=t.charAt(1))){for(var E=2;_l!=t.charAt(E)&&(c1!=t.charAt(E)||o1!=t.charAt(E+1));)++E;if(E+=2,_l===t.charAt(E-1))return o("End of comment missing");var h=t.slice(2,E-2);return l+=2,i(h),t=t.slice(E),l+=2,v({type:Rk,comment:h})}}function m(){var v=r(),E=c(Ak);if(E){if(p(),!c(zk))return o("property missing ':'");var h=c(Ck),d=v({type:_k,property:s1(E[0].replace(u1,_l)),value:h?s1(h[0].replace(u1,_l)):_l});return c(Mk),d}}function y(){var v=[];f(v);for(var E;E=m();)E!==!1&&(v.push(E),f(v));return v}return s(),y()};function s1(t){return t?t.replace(Dk,_l):_l}});var p1=le(ma=>{"use strict";var Nk=ma&&ma.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ma,"__esModule",{value:!0});ma.default=Uk;var Lk=Nk(m1());function Uk(t,e){var n=null;if(!t||typeof t!="string")return n;var l=(0,Lk.default)(t),i=typeof e=="function";return l.forEach(function(r){if(r.type==="declaration"){var a=r.property,u=r.value;i?e(a,u,r):u&&(n=n||{},n[a]=u)}}),n}});var d1=le(ao=>{"use strict";Object.defineProperty(ao,"__esModule",{value:!0});ao.camelCase=void 0;var Bk=/^--[a-zA-Z0-9_-]+$/,Hk=/-([a-z])/g,qk=/^[^-]+$/,jk=/^-(webkit|moz|ms|o|khtml)-/,Yk=/^-(ms)-/,Gk=function(t){return!t||qk.test(t)||Bk.test(t)},Vk=function(t,e){return e.toUpperCase()},h1=function(t,e){return"".concat(e,"-")},Xk=function(t,e){return e===void 0&&(e={}),Gk(t)?t:(t=t.toLowerCase(),e.reactCompat?t=t.replace(Yk,h1):t=t.replace(jk,h1),t.replace(Hk,Vk))};ao.camelCase=Xk});var y1=le((Lf,g1)=>{"use strict";var Qk=Lf&&Lf.__importDefault||function(t){return t&&t.__esModule?t:{default:t}},Zk=Qk(p1()),Fk=d1();function Nf(t,e){var n={};return!t||typeof t!="string"||(0,Zk.default)(t,function(l,i){l&&i&&(n[(0,Fk.camelCase)(l,e)]=i)}),n}Nf.default=Nf;g1.exports=Nf});var z1=le(oo=>{"use strict";var pw=Symbol.for("react.transitional.element"),hw=Symbol.for("react.fragment");function A1(t,e,n){var l=null;if(n!==void 0&&(l=""+n),e.key!==void 0&&(l=""+e.key),"key"in e){n={};for(var i in e)i!=="key"&&(n[i]=e[i])}else n=e;return e=n.ref,{$$typeof:pw,type:t,key:l,ref:e!==void 0?e:null,props:n}}oo.Fragment=hw;oo.jsx=A1;oo.jsxs=A1});var Yi=le((hC,C1)=>{"use strict";C1.exports=z1()});var Xb=le((MO,Vb)=>{"use strict";var Ro=Object.prototype.hasOwnProperty,Gb=Object.prototype.toString,Ub=Object.defineProperty,Bb=Object.getOwnPropertyDescriptor,Hb=function(e){return typeof Array.isArray=="function"?Array.isArray(e):Gb.call(e)==="[object Array]"},qb=function(e){if(!e||Gb.call(e)!=="[object Object]")return!1;var n=Ro.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&Ro.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!n&&!l)return!1;var i;for(i in e);return typeof i=="undefined"||Ro.call(e,i)},jb=function(e,n){Ub&&n.name==="__proto__"?Ub(e,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):e[n.name]=n.newValue},Yb=function(e,n){if(n==="__proto__")if(Ro.call(e,n)){if(Bb)return Bb(e,n).value}else return;return e[n]};Vb.exports=function t(){var e,n,l,i,r,a,u=arguments[0],o=1,c=arguments.length,s=!1;for(typeof u=="boolean"&&(s=u,u=arguments[1]||{},o=2),(u==null||typeof u!="object"&&typeof u!="function")&&(u={});o<c;++o)if(e=arguments[o],e!=null)for(n in e)l=Yb(u,n),i=Yb(e,n),u!==i&&(s&&i&&(qb(i)||(r=Hb(i)))?(r?(r=!1,a=l&&Hb(l)?l:[]):a=l&&qb(l)?l:{},jb(u,{name:n,newValue:t(s,a,i)})):typeof i!="undefined"&&jb(u,{name:n,newValue:i}));return u}});var de=Fe(lr()),cx=Fe(Wy());function $y(t,e){let n=e||{};return(t[t.length-1]===""?[...t,""]:t).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}var dk=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,gk=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,yk={};function no(t,e){return((e||yk).jsx?gk:dk).test(t)}var bk=/[ \t\n\f\r]/g;function Ef(t){return typeof t=="object"?t.type==="text"?t1(t.value):!1:t1(t)}function t1(t){return t.replace(bk,"")===""}var An=class{constructor(e,n,l){this.normal=n,this.property=e,l&&(this.space=l)}};An.prototype.normal={};An.prototype.property={};An.prototype.space=void 0;function Tf(t,e){let n={},l={};for(let i of t)Object.assign(n,i.property),Object.assign(l,i.normal);return new An(n,l,e)}function sa(t){return t.toLowerCase()}var Qt=class{constructor(e,n){this.attribute=n,this.property=e}};Qt.prototype.attribute="";Qt.prototype.booleanish=!1;Qt.prototype.boolean=!1;Qt.prototype.commaOrSpaceSeparated=!1;Qt.prototype.commaSeparated=!1;Qt.prototype.defined=!1;Qt.prototype.mustUseProperty=!1;Qt.prototype.number=!1;Qt.prototype.overloadedBoolean=!1;Qt.prototype.property="";Qt.prototype.spaceSeparated=!1;Qt.prototype.space=void 0;var fa={};yp(fa,{boolean:()=>X,booleanish:()=>At,commaOrSpaceSeparated:()=>he,commaSeparated:()=>ll,number:()=>O,overloadedBoolean:()=>Af,spaceSeparated:()=>ot});var xk=0,X=Ol(),At=Ol(),Af=Ol(),O=Ol(),ot=Ol(),ll=Ol(),he=Ol();function Ol(){return gp(2,++xk)}var zf=Object.keys(fa),Rl=class extends Qt{constructor(e,n,l,i){let r=-1;if(super(e,n),e1(this,"space",i),typeof l=="number")for(;++r<zf.length;){let a=zf[r];e1(this,zf[r],(l&fa[a])===fa[a])}}};Rl.prototype.defined=!0;function e1(t,e,n){n&&(t[e]=n)}function Be(t){let e={},n={};for(let[l,i]of Object.entries(t.properties)){let r=new Rl(l,t.transform(t.attributes||{},l),i,t.space);t.mustUseProperty&&t.mustUseProperty.includes(l)&&(r.mustUseProperty=!0),e[l]=r,n[sa(l)]=l,n[sa(r.attribute)]=l}return new An(e,n,t.space)}var Cf=Be({properties:{ariaActiveDescendant:null,ariaAtomic:At,ariaAutoComplete:null,ariaBusy:At,ariaChecked:At,ariaColCount:O,ariaColIndex:O,ariaColSpan:O,ariaControls:ot,ariaCurrent:null,ariaDescribedBy:ot,ariaDetails:null,ariaDisabled:At,ariaDropEffect:ot,ariaErrorMessage:null,ariaExpanded:At,ariaFlowTo:ot,ariaGrabbed:At,ariaHasPopup:null,ariaHidden:At,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:ot,ariaLevel:O,ariaLive:null,ariaModal:At,ariaMultiLine:At,ariaMultiSelectable:At,ariaOrientation:null,ariaOwns:ot,ariaPlaceholder:null,ariaPosInSet:O,ariaPressed:At,ariaReadOnly:At,ariaRelevant:null,ariaRequired:At,ariaRoleDescription:ot,ariaRowCount:O,ariaRowIndex:O,ariaRowSpan:O,ariaSelected:At,ariaSetSize:O,ariaSort:null,ariaValueMax:O,ariaValueMin:O,ariaValueNow:O,ariaValueText:null,role:null},transform(t,e){return e==="role"?e:"aria-"+e.slice(4).toLowerCase()}});function lo(t,e){return e in t?t[e]:e}function io(t,e){return lo(t,e.toLowerCase())}var n1=Be({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:ll,acceptCharset:ot,accessKey:ot,action:null,allow:null,allowFullScreen:X,allowPaymentRequest:X,allowUserMedia:X,alt:null,as:null,async:X,autoCapitalize:null,autoComplete:ot,autoFocus:X,autoPlay:X,blocking:ot,capture:null,charSet:null,checked:X,cite:null,className:ot,cols:O,colSpan:null,content:null,contentEditable:At,controls:X,controlsList:ot,coords:O|ll,crossOrigin:null,data:null,dateTime:null,decoding:null,default:X,defer:X,dir:null,dirName:null,disabled:X,download:Af,draggable:At,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:X,formTarget:null,headers:ot,height:O,hidden:X,high:O,href:null,hrefLang:null,htmlFor:ot,httpEquiv:ot,id:null,imageSizes:null,imageSrcSet:null,inert:X,inputMode:null,integrity:null,is:null,isMap:X,itemId:null,itemProp:ot,itemRef:ot,itemScope:X,itemType:ot,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:X,low:O,manifest:null,max:null,maxLength:O,media:null,method:null,min:null,minLength:O,multiple:X,muted:X,name:null,nonce:null,noModule:X,noValidate:X,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:X,optimum:O,pattern:null,ping:ot,placeholder:null,playsInline:X,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:X,referrerPolicy:null,rel:ot,required:X,reversed:X,rows:O,rowSpan:O,sandbox:ot,scope:null,scoped:X,seamless:X,selected:X,shadowRootClonable:X,shadowRootDelegatesFocus:X,shadowRootMode:null,shape:null,size:O,sizes:null,slot:null,span:O,spellCheck:At,src:null,srcDoc:null,srcLang:null,srcSet:null,start:O,step:null,style:null,tabIndex:O,target:null,title:null,translate:null,type:null,typeMustMatch:X,useMap:null,value:At,width:O,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:ot,axis:null,background:null,bgColor:null,border:O,borderColor:null,bottomMargin:O,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:X,declare:X,event:null,face:null,frame:null,frameBorder:null,hSpace:O,leftMargin:O,link:null,longDesc:null,lowSrc:null,marginHeight:O,marginWidth:O,noResize:X,noHref:X,noShade:X,noWrap:X,object:null,profile:null,prompt:null,rev:null,rightMargin:O,rules:null,scheme:null,scrolling:At,standby:null,summary:null,text:null,topMargin:O,valueType:null,version:null,vAlign:null,vLink:null,vSpace:O,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:X,disableRemotePlayback:X,prefix:null,property:null,results:O,security:null,unselectable:null},space:"html",transform:io});var l1=Be({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:he,accentHeight:O,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:O,amplitude:O,arabicForm:null,ascent:O,attributeName:null,attributeType:null,azimuth:O,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:O,by:null,calcMode:null,capHeight:O,className:ot,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:O,diffuseConstant:O,direction:null,display:null,dur:null,divisor:O,dominantBaseline:null,download:X,dx:null,dy:null,edgeMode:null,editable:null,elevation:O,enableBackground:null,end:null,event:null,exponent:O,externalResourcesRequired:null,fill:null,fillOpacity:O,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:ll,g2:ll,glyphName:ll,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:O,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:O,horizOriginX:O,horizOriginY:O,id:null,ideographic:O,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:O,k:O,k1:O,k2:O,k3:O,k4:O,kernelMatrix:he,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:O,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:O,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:O,overlineThickness:O,paintOrder:null,panose1:null,path:null,pathLength:O,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:ot,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:O,pointsAtY:O,pointsAtZ:O,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:he,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:he,rev:he,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:he,requiredFeatures:he,requiredFonts:he,requiredFormats:he,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:O,specularExponent:O,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:O,strikethroughThickness:O,string:null,stroke:null,strokeDashArray:he,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:O,strokeOpacity:O,strokeWidth:null,style:null,surfaceScale:O,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:he,tabIndex:O,tableValues:null,target:null,targetX:O,targetY:O,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:he,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:O,underlineThickness:O,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:O,values:null,vAlphabetic:O,vMathematical:O,vectorEffect:null,vHanging:O,vIdeographic:O,version:null,vertAdvY:O,vertOriginX:O,vertOriginY:O,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:O,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:lo});var Mf=Be({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(t,e){return"xlink:"+e.slice(5).toLowerCase()}});var Df=Be({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:io});var Of=Be({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(t,e){return"xml:"+e.slice(3).toLowerCase()}});var Rf={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var vk=/[A-Z]/g,i1=/-[a-z]/g,Sk=/^data[-\w.:]+$/i;function _f(t,e){let n=sa(e),l=e,i=Qt;if(n in t.normal)return t.property[t.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&Sk.test(e)){if(e.charAt(4)==="-"){let r=e.slice(5).replace(i1,wk);l="data"+r.charAt(0).toUpperCase()+r.slice(1)}else{let r=e.slice(4);if(!i1.test(r)){let a=r.replace(vk,kk);a.charAt(0)!=="-"&&(a="-"+a),e="data"+a}}i=Rl}return new i(l,e)}function kk(t){return"-"+t.toLowerCase()}function wk(t){return t.charAt(1).toUpperCase()}var r1=Tf([Cf,n1,Mf,Df,Of],"html"),ro=Tf([Cf,l1,Mf,Df,Of],"svg");function a1(t){return t.join(" ").trim()}var S1=Fe(y1(),1);var uo=b1("end"),ji=b1("start");function b1(t){return e;function e(n){let l=n&&n.position&&n.position[t]||{};if(typeof l.line=="number"&&l.line>0&&typeof l.column=="number"&&l.column>0)return{line:l.line,column:l.column,offset:typeof l.offset=="number"&&l.offset>-1?l.offset:void 0}}}function Uf(t){let e=ji(t),n=uo(t);if(e&&n)return{start:e,end:n}}function il(t){return!t||typeof t!="object"?"":"position"in t||"type"in t?x1(t.position):"start"in t||"end"in t?x1(t):"line"in t||"column"in t?Bf(t):""}function Bf(t){return v1(t&&t.line)+":"+v1(t&&t.column)}function x1(t){return Bf(t&&t.start)+"-"+Bf(t&&t.end)}function v1(t){return t&&typeof t=="number"?t:1}var Dt=class extends Error{constructor(e,n,l){super(),typeof n=="string"&&(l=n,n=void 0);let i="",r={},a=!1;if(n&&("line"in n&&"column"in n?r={place:n}:"start"in n&&"end"in n?r={place:n}:"type"in n?r={ancestors:[n],place:n.position}:r=M({},n)),typeof e=="string"?i=e:!r.cause&&e&&(a=!0,i=e.message,r.cause=e),!r.ruleId&&!r.source&&typeof l=="string"){let o=l.indexOf(":");o===-1?r.ruleId=l:(r.source=l.slice(0,o),r.ruleId=l.slice(o+1))}if(!r.place&&r.ancestors&&r.ancestors){let o=r.ancestors[r.ancestors.length-1];o&&(r.place=o.position)}let u=r.place&&"start"in r.place?r.place.start:r.place;this.ancestors=r.ancestors||void 0,this.cause=r.cause||void 0,this.column=u?u.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=u?u.line:void 0,this.name=il(r.place)||"1:1",this.place=r.place||void 0,this.reason=this.message,this.ruleId=r.ruleId||void 0,this.source=r.source||void 0,this.stack=a&&r.cause&&typeof r.cause.stack=="string"?r.cause.stack:"",this.actual,this.expected,this.note,this.url}};Dt.prototype.file="";Dt.prototype.name="";Dt.prototype.reason="";Dt.prototype.message="";Dt.prototype.stack="";Dt.prototype.column=void 0;Dt.prototype.line=void 0;Dt.prototype.ancestors=void 0;Dt.prototype.cause=void 0;Dt.prototype.fatal=void 0;Dt.prototype.place=void 0;Dt.prototype.ruleId=void 0;Dt.prototype.source=void 0;var Hf={}.hasOwnProperty,Kk=new Map,Ik=/[A-Z]/g,Jk=new Set(["table","tbody","thead","tfoot","tr"]),Pk=new Set(["td","th"]),k1="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function qf(t,e){if(!e||e.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");let n=e.filePath||void 0,l;if(e.development){if(typeof e.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");l=rw(n,e.jsxDEV)}else{if(typeof e.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof e.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");l=iw(n,e.jsx,e.jsxs)}let i={Fragment:e.Fragment,ancestors:[],components:e.components||{},create:l,elementAttributeNameCase:e.elementAttributeNameCase||"react",evaluater:e.createEvaluater?e.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:e.ignoreInvalidStyle||!1,passKeys:e.passKeys!==!1,passNode:e.passNode||!1,schema:e.space==="svg"?ro:r1,stylePropertyNameCase:e.stylePropertyNameCase||"dom",tableCellAlignToStyle:e.tableCellAlignToStyle!==!1},r=w1(i,t,void 0);return r&&typeof r!="string"?r:i.create(t,i.Fragment,{children:r||void 0},void 0)}function w1(t,e,n){if(e.type==="element")return Wk(t,e,n);if(e.type==="mdxFlowExpression"||e.type==="mdxTextExpression")return $k(t,e);if(e.type==="mdxJsxFlowElement"||e.type==="mdxJsxTextElement")return ew(t,e,n);if(e.type==="mdxjsEsm")return tw(t,e);if(e.type==="root")return nw(t,e,n);if(e.type==="text")return lw(t,e)}function Wk(t,e,n){let l=t.schema,i=l;e.tagName.toLowerCase()==="svg"&&l.space==="html"&&(i=ro,t.schema=i),t.ancestors.push(e);let r=T1(t,e.tagName,!1),a=aw(t,e),u=Yf(t,e);return Jk.has(e.tagName)&&(u=u.filter(function(o){return typeof o=="string"?!Ef(o):!0})),E1(t,a,r,e),jf(a,u),t.ancestors.pop(),t.schema=l,t.create(e,r,a,n)}function $k(t,e){if(e.data&&e.data.estree&&t.evaluater){let l=e.data.estree.body[0];return l.type,t.evaluater.evaluateExpression(l.expression)}pa(t,e.position)}function tw(t,e){if(e.data&&e.data.estree&&t.evaluater)return t.evaluater.evaluateProgram(e.data.estree);pa(t,e.position)}function ew(t,e,n){let l=t.schema,i=l;e.name==="svg"&&l.space==="html"&&(i=ro,t.schema=i),t.ancestors.push(e);let r=e.name===null?t.Fragment:T1(t,e.name,!0),a=uw(t,e),u=Yf(t,e);return E1(t,a,r,e),jf(a,u),t.ancestors.pop(),t.schema=l,t.create(e,r,a,n)}function nw(t,e,n){let l={};return jf(l,Yf(t,e)),t.create(e,t.Fragment,l,n)}function lw(t,e){return e.value}function E1(t,e,n,l){typeof n!="string"&&n!==t.Fragment&&t.passNode&&(e.node=l)}function jf(t,e){if(e.length>0){let n=e.length>1?e:e[0];n&&(t.children=n)}}function iw(t,e,n){return l;function l(i,r,a,u){let c=Array.isArray(a.children)?n:e;return u?c(r,a,u):c(r,a)}}function rw(t,e){return n;function n(l,i,r,a){let u=Array.isArray(r.children),o=ji(l);return e(i,r,a,u,{columnNumber:o?o.column-1:void 0,fileName:t,lineNumber:o?o.line:void 0},void 0)}}function aw(t,e){let n={},l,i;for(i in e.properties)if(i!=="children"&&Hf.call(e.properties,i)){let r=ow(t,i,e.properties[i]);if(r){let[a,u]=r;t.tableCellAlignToStyle&&a==="align"&&typeof u=="string"&&Pk.has(e.tagName)?l=u:n[a]=u}}if(l){let r=n.style||(n.style={});r[t.stylePropertyNameCase==="css"?"text-align":"textAlign"]=l}return n}function uw(t,e){let n={};for(let l of e.attributes)if(l.type==="mdxJsxExpressionAttribute")if(l.data&&l.data.estree&&t.evaluater){let r=l.data.estree.body[0];r.type;let a=r.expression;a.type;let u=a.properties[0];u.type,Object.assign(n,t.evaluater.evaluateExpression(u.argument))}else pa(t,e.position);else{let i=l.name,r;if(l.value&&typeof l.value=="object")if(l.value.data&&l.value.data.estree&&t.evaluater){let u=l.value.data.estree.body[0];u.type,r=t.evaluater.evaluateExpression(u.expression)}else pa(t,e.position);else r=l.value===null?!0:l.value;n[i]=r}return n}function Yf(t,e){let n=[],l=-1,i=t.passKeys?new Map:Kk;for(;++l<e.children.length;){let r=e.children[l],a;if(t.passKeys){let o=r.type==="element"?r.tagName:r.type==="mdxJsxFlowElement"||r.type==="mdxJsxTextElement"?r.name:void 0;if(o){let c=i.get(o)||0;a=o+"-"+c,i.set(o,c+1)}}let u=w1(t,r,a);u!==void 0&&n.push(u)}return n}function ow(t,e,n){let l=_f(t.schema,e);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=l.commaSeparated?$y(n):a1(n)),l.property==="style"){let i=typeof n=="object"?n:cw(t,String(n));return t.stylePropertyNameCase==="css"&&(i=sw(i)),["style",i]}return[t.elementAttributeNameCase==="react"&&l.space?Rf[l.property]||l.property:l.attribute,n]}}function cw(t,e){try{return(0,S1.default)(e,{reactCompat:!0})}catch(n){if(t.ignoreInvalidStyle)return{};let l=n,i=new Dt("Cannot parse `style` attribute",{ancestors:t.ancestors,cause:l,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw i.file=t.filePath||void 0,i.url=k1+"#cannot-parse-style-attribute",i}}function T1(t,e,n){let l;if(!n)l={type:"Literal",value:e};else if(e.includes(".")){let i=e.split("."),r=-1,a;for(;++r<i.length;){let u=no(i[r])?{type:"Identifier",name:i[r]}:{type:"Literal",value:i[r]};a=a?{type:"MemberExpression",object:a,property:u,computed:!!(r&&u.type==="Literal"),optional:!1}:u}l=a}else l=no(e)&&!/^[a-z]/.test(e)?{type:"Identifier",name:e}:{type:"Literal",value:e};if(l.type==="Literal"){let i=l.value;return Hf.call(t.components,i)?t.components[i]:i}if(t.evaluater)return t.evaluater.evaluateExpression(l);pa(t)}function pa(t,e){let n=new Dt("Cannot handle MDX estrees without `createEvaluater`",{ancestors:t.ancestors,place:e,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=t.filePath||void 0,n.url=k1+"#cannot-handle-mdx-estrees-without-createevaluater",n}function sw(t){let e={},n;for(n in t)Hf.call(t,n)&&(e[fw(n)]=t[n]);return e}function fw(t){let e=t.replace(Ik,mw);return e.slice(0,3)==="ms-"&&(e="-"+e),e}function mw(t){return"-"+t.toLowerCase()}var ha={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var Zi=Fe(Yi(),1),t0=Fe(lr(),1);var dw={};function Nl(t,e){let n=e||dw,l=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return D1(t,l,i)}function D1(t,e,n){if(gw(t)){if("value"in t)return t.type==="html"&&!n?"":t.value;if(e&&"alt"in t&&t.alt)return t.alt;if("children"in t)return M1(t.children,e,n)}return Array.isArray(t)?M1(t,e,n):""}function M1(t,e,n){let l=[],i=-1;for(;++i<t.length;)l[i]=D1(t[i],e,n);return l.join("")}function gw(t){return!!(t&&typeof t=="object")}var O1=document.createElement("i");function Gi(t){let e="&"+t+";";O1.innerHTML=e;let n=O1.textContent;return n.charCodeAt(n.length-1)===59&&t!=="semi"||n===e?!1:n}function Ot(t,e,n,l){let i=t.length,r=0,a;if(e<0?e=-e>i?0:i+e:e=e>i?i:e,n=n>0?n:0,l.length<1e4)a=Array.from(l),a.unshift(e,n),t.splice(...a);else for(n&&t.splice(e,n);r<l.length;)a=l.slice(r,r+1e4),a.unshift(e,0),t.splice(...a),r+=1e4,e+=1e4}function ue(t,e){return t.length>0?(Ot(t,t.length,0,e),t):e}var R1={}.hasOwnProperty;function co(t){let e={},n=-1;for(;++n<t.length;)yw(e,t[n]);return e}function yw(t,e){let n;for(n in e){let i=(R1.call(t,n)?t[n]:void 0)||(t[n]={}),r=e[n],a;if(r)for(a in r){R1.call(i,a)||(i[a]=[]);let u=r[a];bw(i[a],Array.isArray(u)?u:u?[u]:[])}}}function bw(t,e){let n=-1,l=[];for(;++n<e.length;)(e[n].add==="after"?t:l).push(e[n]);Ot(t,0,0,l)}function so(t,e){let n=Number.parseInt(t,e);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"\uFFFD":String.fromCodePoint(n)}function te(t){return t.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}var Ht=rl(/[A-Za-z]/),zt=rl(/[\dA-Za-z]/),_1=rl(/[#-'*+\--9=?A-Z^-~]/);function Ll(t){return t!==null&&(t<32||t===127)}var da=rl(/\d/),N1=rl(/[\dA-Fa-f]/),L1=rl(/[!-/:-@[-`{-~]/);function _(t){return t!==null&&t<-2}function W(t){return t!==null&&(t<0||t===32)}function Y(t){return t===-2||t===-1||t===32}var Ul=rl(new RegExp("\\p{P}|\\p{S}","u")),ln=rl(/\s/);function rl(t){return e;function e(n){return n!==null&&n>-1&&t.test(String.fromCharCode(n))}}function He(t){let e=[],n=-1,l=0,i=0;for(;++n<t.length;){let r=t.charCodeAt(n),a="";if(r===37&&zt(t.charCodeAt(n+1))&&zt(t.charCodeAt(n+2)))i=2;else if(r<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(r))||(a=String.fromCharCode(r));else if(r>55295&&r<57344){let u=t.charCodeAt(n+1);r<56320&&u>56319&&u<57344?(a=String.fromCharCode(r,u),i=1):a="\uFFFD"}else a=String.fromCharCode(r);a&&(e.push(t.slice(l,n),encodeURIComponent(a)),l=n+i+1,a=""),i&&(n+=i,i=0)}return e.join("")+t.slice(l)}function q(t,e,n,l){let i=l?l-1:Number.POSITIVE_INFINITY,r=0;return a;function a(o){return Y(o)?(t.enter(n),u(o)):e(o)}function u(o){return Y(o)&&r++<i?(t.consume(o),u):(t.exit(n),e(o))}}var U1={tokenize:xw};function xw(t){let e=t.attempt(this.parser.constructs.contentInitial,l,i),n;return e;function l(u){if(u===null){t.consume(u);return}return t.enter("lineEnding"),t.consume(u),t.exit("lineEnding"),q(t,e,"linePrefix")}function i(u){return t.enter("paragraph"),r(u)}function r(u){let o=t.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=o),n=o,a(u)}function a(u){if(u===null){t.exit("chunkText"),t.exit("paragraph"),t.consume(u);return}return _(u)?(t.consume(u),t.exit("chunkText"),r):(t.consume(u),a)}}var H1={tokenize:vw},B1={tokenize:Sw};function vw(t){let e=this,n=[],l=0,i,r,a;return u;function u(g){if(l<n.length){let k=n[l];return e.containerState=k[1],t.attempt(k[0].continuation,o,c)(g)}return c(g)}function o(g){if(l++,e.containerState._closeFlow){e.containerState._closeFlow=void 0,i&&d();let k=e.events.length,C=k,w;for(;C--;)if(e.events[C][0]==="exit"&&e.events[C][1].type==="chunkFlow"){w=e.events[C][1].end;break}h(l);let A=k;for(;A<e.events.length;)e.events[A][1].end=M({},w),A++;return Ot(e.events,C+1,0,e.events.slice(k)),e.events.length=A,c(g)}return u(g)}function c(g){if(l===n.length){if(!i)return p(g);if(i.currentConstruct&&i.currentConstruct.concrete)return y(g);e.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return e.containerState={},t.check(B1,s,f)(g)}function s(g){return i&&d(),h(l),p(g)}function f(g){return e.parser.lazy[e.now().line]=l!==n.length,a=e.now().offset,y(g)}function p(g){return e.containerState={},t.attempt(B1,m,y)(g)}function m(g){return l++,n.push([e.currentConstruct,e.containerState]),p(g)}function y(g){if(g===null){i&&d(),h(0),t.consume(g);return}return i=i||e.parser.flow(e.now()),t.enter("chunkFlow",{_tokenizer:i,contentType:"flow",previous:r}),v(g)}function v(g){if(g===null){E(t.exit("chunkFlow"),!0),h(0),t.consume(g);return}return _(g)?(t.consume(g),E(t.exit("chunkFlow")),l=0,e.interrupt=void 0,u):(t.consume(g),v)}function E(g,k){let C=e.sliceStream(g);if(k&&C.push(null),g.previous=r,r&&(r.next=g),r=g,i.defineSkip(g.start),i.write(C),e.parser.lazy[g.start.line]){let w=i.events.length;for(;w--;)if(i.events[w][1].start.offset<a&&(!i.events[w][1].end||i.events[w][1].end.offset>a))return;let A=e.events.length,T=A,N,S;for(;T--;)if(e.events[T][0]==="exit"&&e.events[T][1].type==="chunkFlow"){if(N){S=e.events[T][1].end;break}N=!0}for(h(l),w=A;w<e.events.length;)e.events[w][1].end=M({},S),w++;Ot(e.events,T+1,0,e.events.slice(A)),e.events.length=w}}function h(g){let k=n.length;for(;k-- >g;){let C=n[k];e.containerState=C[1],C[0].exit.call(e,t)}n.length=g}function d(){i.write([null]),r=void 0,i=void 0,e.containerState._closeFlow=void 0}}function Sw(t,e,n){return q(t,t.attempt(this.parser.constructs.document,e,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function zn(t){if(t===null||W(t)||ln(t))return 1;if(Ul(t))return 2}function al(t,e,n){let l=[],i=-1;for(;++i<t.length;){let r=t[i].resolveAll;r&&!l.includes(r)&&(e=r(e,n),l.push(r))}return e}var ga={name:"attention",resolveAll:kw,tokenize:ww};function kw(t,e){let n=-1,l,i,r,a,u,o,c,s;for(;++n<t.length;)if(t[n][0]==="enter"&&t[n][1].type==="attentionSequence"&&t[n][1]._close){for(l=n;l--;)if(t[l][0]==="exit"&&t[l][1].type==="attentionSequence"&&t[l][1]._open&&e.sliceSerialize(t[l][1]).charCodeAt(0)===e.sliceSerialize(t[n][1]).charCodeAt(0)){if((t[l][1]._close||t[n][1]._open)&&(t[n][1].end.offset-t[n][1].start.offset)%3&&!((t[l][1].end.offset-t[l][1].start.offset+t[n][1].end.offset-t[n][1].start.offset)%3))continue;o=t[l][1].end.offset-t[l][1].start.offset>1&&t[n][1].end.offset-t[n][1].start.offset>1?2:1;let f=M({},t[l][1].end),p=M({},t[n][1].start);q1(f,-o),q1(p,o),a={type:o>1?"strongSequence":"emphasisSequence",start:f,end:M({},t[l][1].end)},u={type:o>1?"strongSequence":"emphasisSequence",start:M({},t[n][1].start),end:p},r={type:o>1?"strongText":"emphasisText",start:M({},t[l][1].end),end:M({},t[n][1].start)},i={type:o>1?"strong":"emphasis",start:M({},a.start),end:M({},u.end)},t[l][1].end=M({},a.start),t[n][1].start=M({},u.end),c=[],t[l][1].end.offset-t[l][1].start.offset&&(c=ue(c,[["enter",t[l][1],e],["exit",t[l][1],e]])),c=ue(c,[["enter",i,e],["enter",a,e],["exit",a,e],["enter",r,e]]),c=ue(c,al(e.parser.constructs.insideSpan.null,t.slice(l+1,n),e)),c=ue(c,[["exit",r,e],["enter",u,e],["exit",u,e],["exit",i,e]]),t[n][1].end.offset-t[n][1].start.offset?(s=2,c=ue(c,[["enter",t[n][1],e],["exit",t[n][1],e]])):s=0,Ot(t,l-1,n-l+3,c),n=l+c.length-s-2;break}}for(n=-1;++n<t.length;)t[n][1].type==="attentionSequence"&&(t[n][1].type="data");return t}function ww(t,e){let n=this.parser.constructs.attentionMarkers.null,l=this.previous,i=zn(l),r;return a;function a(o){return r=o,t.enter("attentionSequence"),u(o)}function u(o){if(o===r)return t.consume(o),u;let c=t.exit("attentionSequence"),s=zn(o),f=!s||s===2&&i||n.includes(o),p=!i||i===2&&s||n.includes(l);return c._open=!!(r===42?f:f&&(i||!p)),c._close=!!(r===42?p:p&&(s||!f)),e(o)}}function q1(t,e){t.column+=e,t.offset+=e,t._bufferIndex+=e}var Gf={name:"autolink",tokenize:Ew};function Ew(t,e,n){let l=0;return i;function i(m){return t.enter("autolink"),t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.enter("autolinkProtocol"),r}function r(m){return Ht(m)?(t.consume(m),a):m===64?n(m):c(m)}function a(m){return m===43||m===45||m===46||zt(m)?(l=1,u(m)):c(m)}function u(m){return m===58?(t.consume(m),l=0,o):(m===43||m===45||m===46||zt(m))&&l++<32?(t.consume(m),u):(l=0,c(m))}function o(m){return m===62?(t.exit("autolinkProtocol"),t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.exit("autolink"),e):m===null||m===32||m===60||Ll(m)?n(m):(t.consume(m),o)}function c(m){return m===64?(t.consume(m),s):_1(m)?(t.consume(m),c):n(m)}function s(m){return zt(m)?f(m):n(m)}function f(m){return m===46?(t.consume(m),l=0,s):m===62?(t.exit("autolinkProtocol").type="autolinkEmail",t.enter("autolinkMarker"),t.consume(m),t.exit("autolinkMarker"),t.exit("autolink"),e):p(m)}function p(m){if((m===45||zt(m))&&l++<63){let y=m===45?p:f;return t.consume(m),y}return n(m)}}var rn={partial:!0,tokenize:Tw};function Tw(t,e,n){return l;function l(r){return Y(r)?q(t,i,"linePrefix")(r):i(r)}function i(r){return r===null||_(r)?e(r):n(r)}}var fo={continuation:{tokenize:zw},exit:Cw,name:"blockQuote",tokenize:Aw};function Aw(t,e,n){let l=this;return i;function i(a){if(a===62){let u=l.containerState;return u.open||(t.enter("blockQuote",{_container:!0}),u.open=!0),t.enter("blockQuotePrefix"),t.enter("blockQuoteMarker"),t.consume(a),t.exit("blockQuoteMarker"),r}return n(a)}function r(a){return Y(a)?(t.enter("blockQuotePrefixWhitespace"),t.consume(a),t.exit("blockQuotePrefixWhitespace"),t.exit("blockQuotePrefix"),e):(t.exit("blockQuotePrefix"),e(a))}}function zw(t,e,n){let l=this;return i;function i(a){return Y(a)?q(t,r,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a):r(a)}function r(a){return t.attempt(fo,e,n)(a)}}function Cw(t){t.exit("blockQuote")}var mo={name:"characterEscape",tokenize:Mw};function Mw(t,e,n){return l;function l(r){return t.enter("characterEscape"),t.enter("escapeMarker"),t.consume(r),t.exit("escapeMarker"),i}function i(r){return L1(r)?(t.enter("characterEscapeValue"),t.consume(r),t.exit("characterEscapeValue"),t.exit("characterEscape"),e):n(r)}}var po={name:"characterReference",tokenize:Dw};function Dw(t,e,n){let l=this,i=0,r,a;return u;function u(f){return t.enter("characterReference"),t.enter("characterReferenceMarker"),t.consume(f),t.exit("characterReferenceMarker"),o}function o(f){return f===35?(t.enter("characterReferenceMarkerNumeric"),t.consume(f),t.exit("characterReferenceMarkerNumeric"),c):(t.enter("characterReferenceValue"),r=31,a=zt,s(f))}function c(f){return f===88||f===120?(t.enter("characterReferenceMarkerHexadecimal"),t.consume(f),t.exit("characterReferenceMarkerHexadecimal"),t.enter("characterReferenceValue"),r=6,a=N1,s):(t.enter("characterReferenceValue"),r=7,a=da,s(f))}function s(f){if(f===59&&i){let p=t.exit("characterReferenceValue");return a===zt&&!Gi(l.sliceSerialize(p))?n(f):(t.enter("characterReferenceMarker"),t.consume(f),t.exit("characterReferenceMarker"),t.exit("characterReference"),e)}return a(f)&&i++<r?(t.consume(f),s):n(f)}}var j1={partial:!0,tokenize:Rw},ho={concrete:!0,name:"codeFenced",tokenize:Ow};function Ow(t,e,n){let l=this,i={partial:!0,tokenize:C},r=0,a=0,u;return o;function o(w){return c(w)}function c(w){let A=l.events[l.events.length-1];return r=A&&A[1].type==="linePrefix"?A[2].sliceSerialize(A[1],!0).length:0,u=w,t.enter("codeFenced"),t.enter("codeFencedFence"),t.enter("codeFencedFenceSequence"),s(w)}function s(w){return w===u?(a++,t.consume(w),s):a<3?n(w):(t.exit("codeFencedFenceSequence"),Y(w)?q(t,f,"whitespace")(w):f(w))}function f(w){return w===null||_(w)?(t.exit("codeFencedFence"),l.interrupt?e(w):t.check(j1,v,k)(w)):(t.enter("codeFencedFenceInfo"),t.enter("chunkString",{contentType:"string"}),p(w))}function p(w){return w===null||_(w)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),f(w)):Y(w)?(t.exit("chunkString"),t.exit("codeFencedFenceInfo"),q(t,m,"whitespace")(w)):w===96&&w===u?n(w):(t.consume(w),p)}function m(w){return w===null||_(w)?f(w):(t.enter("codeFencedFenceMeta"),t.enter("chunkString",{contentType:"string"}),y(w))}function y(w){return w===null||_(w)?(t.exit("chunkString"),t.exit("codeFencedFenceMeta"),f(w)):w===96&&w===u?n(w):(t.consume(w),y)}function v(w){return t.attempt(i,k,E)(w)}function E(w){return t.enter("lineEnding"),t.consume(w),t.exit("lineEnding"),h}function h(w){return r>0&&Y(w)?q(t,d,"linePrefix",r+1)(w):d(w)}function d(w){return w===null||_(w)?t.check(j1,v,k)(w):(t.enter("codeFlowValue"),g(w))}function g(w){return w===null||_(w)?(t.exit("codeFlowValue"),d(w)):(t.consume(w),g)}function k(w){return t.exit("codeFenced"),e(w)}function C(w,A,T){let N=0;return S;function S(j){return w.enter("lineEnding"),w.consume(j),w.exit("lineEnding"),F}function F(j){return w.enter("codeFencedFence"),Y(j)?q(w,Q,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(j):Q(j)}function Q(j){return j===u?(w.enter("codeFencedFenceSequence"),L(j)):T(j)}function L(j){return j===u?(N++,w.consume(j),L):N>=a?(w.exit("codeFencedFenceSequence"),Y(j)?q(w,G,"whitespace")(j):G(j)):T(j)}function G(j){return j===null||_(j)?(w.exit("codeFencedFence"),A(j)):T(j)}}}function Rw(t,e,n){let l=this;return i;function i(a){return a===null?n(a):(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),r)}function r(a){return l.parser.lazy[l.now().line]?n(a):e(a)}}var ya={name:"codeIndented",tokenize:Nw},_w={partial:!0,tokenize:Lw};function Nw(t,e,n){let l=this;return i;function i(c){return t.enter("codeIndented"),q(t,r,"linePrefix",5)(c)}function r(c){let s=l.events[l.events.length-1];return s&&s[1].type==="linePrefix"&&s[2].sliceSerialize(s[1],!0).length>=4?a(c):n(c)}function a(c){return c===null?o(c):_(c)?t.attempt(_w,a,o)(c):(t.enter("codeFlowValue"),u(c))}function u(c){return c===null||_(c)?(t.exit("codeFlowValue"),a(c)):(t.consume(c),u)}function o(c){return t.exit("codeIndented"),e(c)}}function Lw(t,e,n){let l=this;return i;function i(a){return l.parser.lazy[l.now().line]?n(a):_(a)?(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),i):q(t,r,"linePrefix",5)(a)}function r(a){let u=l.events[l.events.length-1];return u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?e(a):_(a)?i(a):n(a)}}var Vf={name:"codeText",previous:Bw,resolve:Uw,tokenize:Hw};function Uw(t){let e=t.length-4,n=3,l,i;if((t[n][1].type==="lineEnding"||t[n][1].type==="space")&&(t[e][1].type==="lineEnding"||t[e][1].type==="space")){for(l=n;++l<e;)if(t[l][1].type==="codeTextData"){t[n][1].type="codeTextPadding",t[e][1].type="codeTextPadding",n+=2,e-=2;break}}for(l=n-1,e++;++l<=e;)i===void 0?l!==e&&t[l][1].type!=="lineEnding"&&(i=l):(l===e||t[l][1].type==="lineEnding")&&(t[i][1].type="codeTextData",l!==i+2&&(t[i][1].end=t[l-1][1].end,t.splice(i+2,l-i-2),e-=l-i-2,l=i+2),i=void 0);return t}function Bw(t){return t!==96||this.events[this.events.length-1][1].type==="characterEscape"}function Hw(t,e,n){let l=this,i=0,r,a;return u;function u(p){return t.enter("codeText"),t.enter("codeTextSequence"),o(p)}function o(p){return p===96?(t.consume(p),i++,o):(t.exit("codeTextSequence"),c(p))}function c(p){return p===null?n(p):p===32?(t.enter("space"),t.consume(p),t.exit("space"),c):p===96?(a=t.enter("codeTextSequence"),r=0,f(p)):_(p)?(t.enter("lineEnding"),t.consume(p),t.exit("lineEnding"),c):(t.enter("codeTextData"),s(p))}function s(p){return p===null||p===32||p===96||_(p)?(t.exit("codeTextData"),c(p)):(t.consume(p),s)}function f(p){return p===96?(t.consume(p),r++,f):r===i?(t.exit("codeTextSequence"),t.exit("codeText"),e(p)):(a.type="codeTextData",s(p))}}var go=class{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw new RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,n){let l=n==null?Number.POSITIVE_INFINITY:n;return l<this.left.length?this.left.slice(e,l):e>this.left.length?this.right.slice(this.right.length-l+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-l+this.left.length).reverse())}splice(e,n,l){let i=n||0;this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-i,Number.POSITIVE_INFINITY);return l&&ba(this.left,l),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),ba(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),ba(this.right,e.reverse())}setCursor(e){if(!(e===this.left.length||e>this.left.length&&this.right.length===0||e<0&&this.left.length===0))if(e<this.left.length){let n=this.left.splice(e,Number.POSITIVE_INFINITY);ba(this.right,n.reverse())}else{let n=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);ba(this.left,n.reverse())}}};function ba(t,e){let n=0;if(e.length<1e4)t.push(...e);else for(;n<e.length;)t.push(...e.slice(n,n+1e4)),n+=1e4}function yo(t){let e={},n=-1,l,i,r,a,u,o,c,s=new go(t);for(;++n<s.length;){for(;n in e;)n=e[n];if(l=s.get(n),n&&l[1].type==="chunkFlow"&&s.get(n-1)[1].type==="listItemPrefix"&&(o=l[1]._tokenizer.events,r=0,r<o.length&&o[r][1].type==="lineEndingBlank"&&(r+=2),r<o.length&&o[r][1].type==="content"))for(;++r<o.length&&o[r][1].type!=="content";)o[r][1].type==="chunkText"&&(o[r][1]._isInFirstContentOfListItem=!0,r++);if(l[0]==="enter")l[1].contentType&&(Object.assign(e,qw(s,n)),n=e[n],c=!0);else if(l[1]._container){for(r=n,i=void 0;r--;)if(a=s.get(r),a[1].type==="lineEnding"||a[1].type==="lineEndingBlank")a[0]==="enter"&&(i&&(s.get(i)[1].type="lineEndingBlank"),a[1].type="lineEnding",i=r);else if(!(a[1].type==="linePrefix"||a[1].type==="listItemIndent"))break;i&&(l[1].end=M({},s.get(i)[1].start),u=s.slice(i,n),u.unshift(l),s.splice(i,n-i+1,u))}}return Ot(t,0,Number.POSITIVE_INFINITY,s.slice(0)),!c}function qw(t,e){let n=t.get(e)[1],l=t.get(e)[2],i=e-1,r=[],a=n._tokenizer;a||(a=l.parser[n.contentType](n.start),n._contentTypeTextTrailing&&(a._contentTypeTextTrailing=!0));let u=a.events,o=[],c={},s,f,p=-1,m=n,y=0,v=0,E=[v];for(;m;){for(;t.get(++i)[1]!==m;);r.push(i),m._tokenizer||(s=l.sliceStream(m),m.next||s.push(null),f&&a.defineSkip(m.start),m._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=!0),a.write(s),m._isInFirstContentOfListItem&&(a._gfmTasklistFirstContentOfListItem=void 0)),f=m,m=m.next}for(m=n;++p<u.length;)u[p][0]==="exit"&&u[p-1][0]==="enter"&&u[p][1].type===u[p-1][1].type&&u[p][1].start.line!==u[p][1].end.line&&(v=p+1,E.push(v),m._tokenizer=void 0,m.previous=void 0,m=m.next);for(a.events=[],m?(m._tokenizer=void 0,m.previous=void 0):E.pop(),p=E.length;p--;){let h=u.slice(E[p],E[p+1]),d=r.pop();o.push([d,d+h.length-1]),t.splice(d,2,h)}for(o.reverse(),p=-1;++p<o.length;)c[y+o[p][0]]=y+o[p][1],y+=o[p][1]-o[p][0]-1;return c}var Xf={resolve:Yw,tokenize:Gw},jw={partial:!0,tokenize:Vw};function Yw(t){return yo(t),t}function Gw(t,e){let n;return l;function l(u){return t.enter("content"),n=t.enter("chunkContent",{contentType:"content"}),i(u)}function i(u){return u===null?r(u):_(u)?t.check(jw,a,r)(u):(t.consume(u),i)}function r(u){return t.exit("chunkContent"),t.exit("content"),e(u)}function a(u){return t.consume(u),t.exit("chunkContent"),n.next=t.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function Vw(t,e,n){let l=this;return i;function i(a){return t.exit("chunkContent"),t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),q(t,r,"linePrefix")}function r(a){if(a===null||_(a))return n(a);let u=l.events[l.events.length-1];return!l.parser.constructs.disable.null.includes("codeIndented")&&u&&u[1].type==="linePrefix"&&u[2].sliceSerialize(u[1],!0).length>=4?e(a):t.interrupt(l.parser.constructs.flow,n,e)(a)}}function bo(t,e,n,l,i,r,a,u,o){let c=o||Number.POSITIVE_INFINITY,s=0;return f;function f(h){return h===60?(t.enter(l),t.enter(i),t.enter(r),t.consume(h),t.exit(r),p):h===null||h===32||h===41||Ll(h)?n(h):(t.enter(l),t.enter(a),t.enter(u),t.enter("chunkString",{contentType:"string"}),v(h))}function p(h){return h===62?(t.enter(r),t.consume(h),t.exit(r),t.exit(i),t.exit(l),e):(t.enter(u),t.enter("chunkString",{contentType:"string"}),m(h))}function m(h){return h===62?(t.exit("chunkString"),t.exit(u),p(h)):h===null||h===60||_(h)?n(h):(t.consume(h),h===92?y:m)}function y(h){return h===60||h===62||h===92?(t.consume(h),m):m(h)}function v(h){return!s&&(h===null||h===41||W(h))?(t.exit("chunkString"),t.exit(u),t.exit(a),t.exit(l),e(h)):s<c&&h===40?(t.consume(h),s++,v):h===41?(t.consume(h),s--,v):h===null||h===32||h===40||Ll(h)?n(h):(t.consume(h),h===92?E:v)}function E(h){return h===40||h===41||h===92?(t.consume(h),v):v(h)}}function xo(t,e,n,l,i,r){let a=this,u=0,o;return c;function c(m){return t.enter(l),t.enter(i),t.consume(m),t.exit(i),t.enter(r),s}function s(m){return u>999||m===null||m===91||m===93&&!o||m===94&&!u&&"_hiddenFootnoteSupport"in a.parser.constructs?n(m):m===93?(t.exit(r),t.enter(i),t.consume(m),t.exit(i),t.exit(l),e):_(m)?(t.enter("lineEnding"),t.consume(m),t.exit("lineEnding"),s):(t.enter("chunkString",{contentType:"string"}),f(m))}function f(m){return m===null||m===91||m===93||_(m)||u++>999?(t.exit("chunkString"),s(m)):(t.consume(m),o||(o=!Y(m)),m===92?p:f)}function p(m){return m===91||m===92||m===93?(t.consume(m),u++,f):f(m)}}function vo(t,e,n,l,i,r){let a;return u;function u(p){return p===34||p===39||p===40?(t.enter(l),t.enter(i),t.consume(p),t.exit(i),a=p===40?41:p,o):n(p)}function o(p){return p===a?(t.enter(i),t.consume(p),t.exit(i),t.exit(l),e):(t.enter(r),c(p))}function c(p){return p===a?(t.exit(r),o(a)):p===null?n(p):_(p)?(t.enter("lineEnding"),t.consume(p),t.exit("lineEnding"),q(t,c,"linePrefix")):(t.enter("chunkString",{contentType:"string"}),s(p))}function s(p){return p===a||p===null||_(p)?(t.exit("chunkString"),c(p)):(t.consume(p),p===92?f:s)}function f(p){return p===a||p===92?(t.consume(p),s):s(p)}}function Bl(t,e){let n;return l;function l(i){return _(i)?(t.enter("lineEnding"),t.consume(i),t.exit("lineEnding"),n=!0,l):Y(i)?q(t,l,n?"linePrefix":"lineSuffix")(i):e(i)}}var Qf={name:"definition",tokenize:Qw},Xw={partial:!0,tokenize:Zw};function Qw(t,e,n){let l=this,i;return r;function r(m){return t.enter("definition"),a(m)}function a(m){return xo.call(l,t,u,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(m)}function u(m){return i=te(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)),m===58?(t.enter("definitionMarker"),t.consume(m),t.exit("definitionMarker"),o):n(m)}function o(m){return W(m)?Bl(t,c)(m):c(m)}function c(m){return bo(t,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(m)}function s(m){return t.attempt(Xw,f,f)(m)}function f(m){return Y(m)?q(t,p,"whitespace")(m):p(m)}function p(m){return m===null||_(m)?(t.exit("definition"),l.parser.defined.push(i),e(m)):n(m)}}function Zw(t,e,n){return l;function l(u){return W(u)?Bl(t,i)(u):n(u)}function i(u){return vo(t,r,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(u)}function r(u){return Y(u)?q(t,a,"whitespace")(u):a(u)}function a(u){return u===null||_(u)?e(u):n(u)}}var Zf={name:"hardBreakEscape",tokenize:Fw};function Fw(t,e,n){return l;function l(r){return t.enter("hardBreakEscape"),t.consume(r),i}function i(r){return _(r)?(t.exit("hardBreakEscape"),e(r)):n(r)}}var Ff={name:"headingAtx",resolve:Kw,tokenize:Iw};function Kw(t,e){let n=t.length-2,l=3,i,r;return t[l][1].type==="whitespace"&&(l+=2),n-2>l&&t[n][1].type==="whitespace"&&(n-=2),t[n][1].type==="atxHeadingSequence"&&(l===n-1||n-4>l&&t[n-2][1].type==="whitespace")&&(n-=l+1===n?2:4),n>l&&(i={type:"atxHeadingText",start:t[l][1].start,end:t[n][1].end},r={type:"chunkText",start:t[l][1].start,end:t[n][1].end,contentType:"text"},Ot(t,l,n-l+1,[["enter",i,e],["enter",r,e],["exit",r,e],["exit",i,e]])),t}function Iw(t,e,n){let l=0;return i;function i(s){return t.enter("atxHeading"),r(s)}function r(s){return t.enter("atxHeadingSequence"),a(s)}function a(s){return s===35&&l++<6?(t.consume(s),a):s===null||W(s)?(t.exit("atxHeadingSequence"),u(s)):n(s)}function u(s){return s===35?(t.enter("atxHeadingSequence"),o(s)):s===null||_(s)?(t.exit("atxHeading"),e(s)):Y(s)?q(t,u,"whitespace")(s):(t.enter("atxHeadingText"),c(s))}function o(s){return s===35?(t.consume(s),o):(t.exit("atxHeadingSequence"),u(s))}function c(s){return s===null||s===35||W(s)?(t.exit("atxHeadingText"),u(s)):(t.consume(s),c)}}var Y1=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Kf=["pre","script","style","textarea"];var If={concrete:!0,name:"htmlFlow",resolveTo:Ww,tokenize:$w},Jw={partial:!0,tokenize:eE},Pw={partial:!0,tokenize:tE};function Ww(t){let e=t.length;for(;e--&&!(t[e][0]==="enter"&&t[e][1].type==="htmlFlow"););return e>1&&t[e-2][1].type==="linePrefix"&&(t[e][1].start=t[e-2][1].start,t[e+1][1].start=t[e-2][1].start,t.splice(e-2,2)),t}function $w(t,e,n){let l=this,i,r,a,u,o;return c;function c(b){return s(b)}function s(b){return t.enter("htmlFlow"),t.enter("htmlFlowData"),t.consume(b),f}function f(b){return b===33?(t.consume(b),p):b===47?(t.consume(b),r=!0,v):b===63?(t.consume(b),i=3,l.interrupt?e:x):Ht(b)?(t.consume(b),a=String.fromCharCode(b),E):n(b)}function p(b){return b===45?(t.consume(b),i=2,m):b===91?(t.consume(b),i=5,u=0,y):Ht(b)?(t.consume(b),i=4,l.interrupt?e:x):n(b)}function m(b){return b===45?(t.consume(b),l.interrupt?e:x):n(b)}function y(b){let mt="CDATA[";return b===mt.charCodeAt(u++)?(t.consume(b),u===mt.length?l.interrupt?e:Q:y):n(b)}function v(b){return Ht(b)?(t.consume(b),a=String.fromCharCode(b),E):n(b)}function E(b){if(b===null||b===47||b===62||W(b)){let mt=b===47,Ae=a.toLowerCase();return!mt&&!r&&Kf.includes(Ae)?(i=1,l.interrupt?e(b):Q(b)):Y1.includes(a.toLowerCase())?(i=6,mt?(t.consume(b),h):l.interrupt?e(b):Q(b)):(i=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(b):r?d(b):g(b))}return b===45||zt(b)?(t.consume(b),a+=String.fromCharCode(b),E):n(b)}function h(b){return b===62?(t.consume(b),l.interrupt?e:Q):n(b)}function d(b){return Y(b)?(t.consume(b),d):S(b)}function g(b){return b===47?(t.consume(b),S):b===58||b===95||Ht(b)?(t.consume(b),k):Y(b)?(t.consume(b),g):S(b)}function k(b){return b===45||b===46||b===58||b===95||zt(b)?(t.consume(b),k):C(b)}function C(b){return b===61?(t.consume(b),w):Y(b)?(t.consume(b),C):g(b)}function w(b){return b===null||b===60||b===61||b===62||b===96?n(b):b===34||b===39?(t.consume(b),o=b,A):Y(b)?(t.consume(b),w):T(b)}function A(b){return b===o?(t.consume(b),o=null,N):b===null||_(b)?n(b):(t.consume(b),A)}function T(b){return b===null||b===34||b===39||b===47||b===60||b===61||b===62||b===96||W(b)?C(b):(t.consume(b),T)}function N(b){return b===47||b===62||Y(b)?g(b):n(b)}function S(b){return b===62?(t.consume(b),F):n(b)}function F(b){return b===null||_(b)?Q(b):Y(b)?(t.consume(b),F):n(b)}function Q(b){return b===45&&i===2?(t.consume(b),P):b===60&&i===1?(t.consume(b),ct):b===62&&i===4?(t.consume(b),Zt):b===63&&i===3?(t.consume(b),x):b===93&&i===5?(t.consume(b),Jt):_(b)&&(i===6||i===7)?(t.exit("htmlFlowData"),t.check(Jw,ne,L)(b)):b===null||_(b)?(t.exit("htmlFlowData"),L(b)):(t.consume(b),Q)}function L(b){return t.check(Pw,G,ne)(b)}function G(b){return t.enter("lineEnding"),t.consume(b),t.exit("lineEnding"),j}function j(b){return b===null||_(b)?L(b):(t.enter("htmlFlowData"),Q(b))}function P(b){return b===45?(t.consume(b),x):Q(b)}function ct(b){return b===47?(t.consume(b),a="",H):Q(b)}function H(b){if(b===62){let mt=a.toLowerCase();return Kf.includes(mt)?(t.consume(b),Zt):Q(b)}return Ht(b)&&a.length<8?(t.consume(b),a+=String.fromCharCode(b),H):Q(b)}function Jt(b){return b===93?(t.consume(b),x):Q(b)}function x(b){return b===62?(t.consume(b),Zt):b===45&&i===2?(t.consume(b),x):Q(b)}function Zt(b){return b===null||_(b)?(t.exit("htmlFlowData"),ne(b)):(t.consume(b),Zt)}function ne(b){return t.exit("htmlFlow"),e(b)}}function tE(t,e,n){let l=this;return i;function i(a){return _(a)?(t.enter("lineEnding"),t.consume(a),t.exit("lineEnding"),r):n(a)}function r(a){return l.parser.lazy[l.now().line]?n(a):e(a)}}function eE(t,e,n){return l;function l(i){return t.enter("lineEnding"),t.consume(i),t.exit("lineEnding"),t.attempt(rn,e,n)}}var Jf={name:"htmlText",tokenize:nE};function nE(t,e,n){let l=this,i,r,a;return u;function u(x){return t.enter("htmlText"),t.enter("htmlTextData"),t.consume(x),o}function o(x){return x===33?(t.consume(x),c):x===47?(t.consume(x),C):x===63?(t.consume(x),g):Ht(x)?(t.consume(x),T):n(x)}function c(x){return x===45?(t.consume(x),s):x===91?(t.consume(x),r=0,y):Ht(x)?(t.consume(x),d):n(x)}function s(x){return x===45?(t.consume(x),m):n(x)}function f(x){return x===null?n(x):x===45?(t.consume(x),p):_(x)?(a=f,ct(x)):(t.consume(x),f)}function p(x){return x===45?(t.consume(x),m):f(x)}function m(x){return x===62?P(x):x===45?p(x):f(x)}function y(x){let Zt="CDATA[";return x===Zt.charCodeAt(r++)?(t.consume(x),r===Zt.length?v:y):n(x)}function v(x){return x===null?n(x):x===93?(t.consume(x),E):_(x)?(a=v,ct(x)):(t.consume(x),v)}function E(x){return x===93?(t.consume(x),h):v(x)}function h(x){return x===62?P(x):x===93?(t.consume(x),h):v(x)}function d(x){return x===null||x===62?P(x):_(x)?(a=d,ct(x)):(t.consume(x),d)}function g(x){return x===null?n(x):x===63?(t.consume(x),k):_(x)?(a=g,ct(x)):(t.consume(x),g)}function k(x){return x===62?P(x):g(x)}function C(x){return Ht(x)?(t.consume(x),w):n(x)}function w(x){return x===45||zt(x)?(t.consume(x),w):A(x)}function A(x){return _(x)?(a=A,ct(x)):Y(x)?(t.consume(x),A):P(x)}function T(x){return x===45||zt(x)?(t.consume(x),T):x===47||x===62||W(x)?N(x):n(x)}function N(x){return x===47?(t.consume(x),P):x===58||x===95||Ht(x)?(t.consume(x),S):_(x)?(a=N,ct(x)):Y(x)?(t.consume(x),N):P(x)}function S(x){return x===45||x===46||x===58||x===95||zt(x)?(t.consume(x),S):F(x)}function F(x){return x===61?(t.consume(x),Q):_(x)?(a=F,ct(x)):Y(x)?(t.consume(x),F):N(x)}function Q(x){return x===null||x===60||x===61||x===62||x===96?n(x):x===34||x===39?(t.consume(x),i=x,L):_(x)?(a=Q,ct(x)):Y(x)?(t.consume(x),Q):(t.consume(x),G)}function L(x){return x===i?(t.consume(x),i=void 0,j):x===null?n(x):_(x)?(a=L,ct(x)):(t.consume(x),L)}function G(x){return x===null||x===34||x===39||x===60||x===61||x===96?n(x):x===47||x===62||W(x)?N(x):(t.consume(x),G)}function j(x){return x===47||x===62||W(x)?N(x):n(x)}function P(x){return x===62?(t.consume(x),t.exit("htmlTextData"),t.exit("htmlText"),e):n(x)}function ct(x){return t.exit("htmlTextData"),t.enter("lineEnding"),t.consume(x),t.exit("lineEnding"),H}function H(x){return Y(x)?q(t,Jt,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(x):Jt(x)}function Jt(x){return t.enter("htmlTextData"),a(x)}}var Hl={name:"labelEnd",resolveAll:aE,resolveTo:uE,tokenize:oE},lE={tokenize:cE},iE={tokenize:sE},rE={tokenize:fE};function aE(t){let e=-1,n=[];for(;++e<t.length;){let l=t[e][1];if(n.push(t[e]),l.type==="labelImage"||l.type==="labelLink"||l.type==="labelEnd"){let i=l.type==="labelImage"?4:2;l.type="data",e+=i}}return t.length!==n.length&&Ot(t,0,t.length,n),t}function uE(t,e){let n=t.length,l=0,i,r,a,u;for(;n--;)if(i=t[n][1],r){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;t[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(a){if(t[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(r=n,i.type!=="labelLink")){l=2;break}}else i.type==="labelEnd"&&(a=n);let o={type:t[r][1].type==="labelLink"?"link":"image",start:M({},t[r][1].start),end:M({},t[t.length-1][1].end)},c={type:"label",start:M({},t[r][1].start),end:M({},t[a][1].end)},s={type:"labelText",start:M({},t[r+l+2][1].end),end:M({},t[a-2][1].start)};return u=[["enter",o,e],["enter",c,e]],u=ue(u,t.slice(r+1,r+l+3)),u=ue(u,[["enter",s,e]]),u=ue(u,al(e.parser.constructs.insideSpan.null,t.slice(r+l+4,a-3),e)),u=ue(u,[["exit",s,e],t[a-2],t[a-1],["exit",c,e]]),u=ue(u,t.slice(a+1)),u=ue(u,[["exit",o,e]]),Ot(t,r,t.length,u),t}function oE(t,e,n){let l=this,i=l.events.length,r,a;for(;i--;)if((l.events[i][1].type==="labelImage"||l.events[i][1].type==="labelLink")&&!l.events[i][1]._balanced){r=l.events[i][1];break}return u;function u(p){return r?r._inactive?f(p):(a=l.parser.defined.includes(te(l.sliceSerialize({start:r.end,end:l.now()}))),t.enter("labelEnd"),t.enter("labelMarker"),t.consume(p),t.exit("labelMarker"),t.exit("labelEnd"),o):n(p)}function o(p){return p===40?t.attempt(lE,s,a?s:f)(p):p===91?t.attempt(iE,s,a?c:f)(p):a?s(p):f(p)}function c(p){return t.attempt(rE,s,f)(p)}function s(p){return e(p)}function f(p){return r._balanced=!0,n(p)}}function cE(t,e,n){return l;function l(f){return t.enter("resource"),t.enter("resourceMarker"),t.consume(f),t.exit("resourceMarker"),i}function i(f){return W(f)?Bl(t,r)(f):r(f)}function r(f){return f===41?s(f):bo(t,a,u,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(f)}function a(f){return W(f)?Bl(t,o)(f):s(f)}function u(f){return n(f)}function o(f){return f===34||f===39||f===40?vo(t,c,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(f):s(f)}function c(f){return W(f)?Bl(t,s)(f):s(f)}function s(f){return f===41?(t.enter("resourceMarker"),t.consume(f),t.exit("resourceMarker"),t.exit("resource"),e):n(f)}}function sE(t,e,n){let l=this;return i;function i(u){return xo.call(l,t,r,a,"reference","referenceMarker","referenceString")(u)}function r(u){return l.parser.defined.includes(te(l.sliceSerialize(l.events[l.events.length-1][1]).slice(1,-1)))?e(u):n(u)}function a(u){return n(u)}}function fE(t,e,n){return l;function l(r){return t.enter("reference"),t.enter("referenceMarker"),t.consume(r),t.exit("referenceMarker"),i}function i(r){return r===93?(t.enter("referenceMarker"),t.consume(r),t.exit("referenceMarker"),t.exit("reference"),e):n(r)}}var Pf={name:"labelStartImage",resolveAll:Hl.resolveAll,tokenize:mE};function mE(t,e,n){let l=this;return i;function i(u){return t.enter("labelImage"),t.enter("labelImageMarker"),t.consume(u),t.exit("labelImageMarker"),r}function r(u){return u===91?(t.enter("labelMarker"),t.consume(u),t.exit("labelMarker"),t.exit("labelImage"),a):n(u)}function a(u){return u===94&&"_hiddenFootnoteSupport"in l.parser.constructs?n(u):e(u)}}var Wf={name:"labelStartLink",resolveAll:Hl.resolveAll,tokenize:pE};function pE(t,e,n){let l=this;return i;function i(a){return t.enter("labelLink"),t.enter("labelMarker"),t.consume(a),t.exit("labelMarker"),t.exit("labelLink"),r}function r(a){return a===94&&"_hiddenFootnoteSupport"in l.parser.constructs?n(a):e(a)}}var xa={name:"lineEnding",tokenize:hE};function hE(t,e){return n;function n(l){return t.enter("lineEnding"),t.consume(l),t.exit("lineEnding"),q(t,e,"linePrefix")}}var ql={name:"thematicBreak",tokenize:dE};function dE(t,e,n){let l=0,i;return r;function r(c){return t.enter("thematicBreak"),a(c)}function a(c){return i=c,u(c)}function u(c){return c===i?(t.enter("thematicBreakSequence"),o(c)):l>=3&&(c===null||_(c))?(t.exit("thematicBreak"),e(c)):n(c)}function o(c){return c===i?(t.consume(c),l++,o):(t.exit("thematicBreakSequence"),Y(c)?q(t,u,"whitespace")(c):u(c))}}var ee={continuation:{tokenize:xE},exit:SE,name:"list",tokenize:bE},gE={partial:!0,tokenize:kE},yE={partial:!0,tokenize:vE};function bE(t,e,n){let l=this,i=l.events[l.events.length-1],r=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,a=0;return u;function u(m){let y=l.containerState.type||(m===42||m===43||m===45?"listUnordered":"listOrdered");if(y==="listUnordered"?!l.containerState.marker||m===l.containerState.marker:da(m)){if(l.containerState.type||(l.containerState.type=y,t.enter(y,{_container:!0})),y==="listUnordered")return t.enter("listItemPrefix"),m===42||m===45?t.check(ql,n,c)(m):c(m);if(!l.interrupt||m===49)return t.enter("listItemPrefix"),t.enter("listItemValue"),o(m)}return n(m)}function o(m){return da(m)&&++a<10?(t.consume(m),o):(!l.interrupt||a<2)&&(l.containerState.marker?m===l.containerState.marker:m===41||m===46)?(t.exit("listItemValue"),c(m)):n(m)}function c(m){return t.enter("listItemMarker"),t.consume(m),t.exit("listItemMarker"),l.containerState.marker=l.containerState.marker||m,t.check(rn,l.interrupt?n:s,t.attempt(gE,p,f))}function s(m){return l.containerState.initialBlankLine=!0,r++,p(m)}function f(m){return Y(m)?(t.enter("listItemPrefixWhitespace"),t.consume(m),t.exit("listItemPrefixWhitespace"),p):n(m)}function p(m){return l.containerState.size=r+l.sliceSerialize(t.exit("listItemPrefix"),!0).length,e(m)}}function xE(t,e,n){let l=this;return l.containerState._closeFlow=void 0,t.check(rn,i,r);function i(u){return l.containerState.furtherBlankLines=l.containerState.furtherBlankLines||l.containerState.initialBlankLine,q(t,e,"listItemIndent",l.containerState.size+1)(u)}function r(u){return l.containerState.furtherBlankLines||!Y(u)?(l.containerState.furtherBlankLines=void 0,l.containerState.initialBlankLine=void 0,a(u)):(l.containerState.furtherBlankLines=void 0,l.containerState.initialBlankLine=void 0,t.attempt(yE,e,a)(u))}function a(u){return l.containerState._closeFlow=!0,l.interrupt=void 0,q(t,t.attempt(ee,e,n),"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(u)}}function vE(t,e,n){let l=this;return q(t,i,"listItemIndent",l.containerState.size+1);function i(r){let a=l.events[l.events.length-1];return a&&a[1].type==="listItemIndent"&&a[2].sliceSerialize(a[1],!0).length===l.containerState.size?e(r):n(r)}}function SE(t){t.exit(this.containerState.type)}function kE(t,e,n){let l=this;return q(t,i,"listItemPrefixWhitespace",l.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function i(r){let a=l.events[l.events.length-1];return!Y(r)&&a&&a[1].type==="listItemPrefixWhitespace"?e(r):n(r)}}var So={name:"setextUnderline",resolveTo:wE,tokenize:EE};function wE(t,e){let n=t.length,l,i,r;for(;n--;)if(t[n][0]==="enter"){if(t[n][1].type==="content"){l=n;break}t[n][1].type==="paragraph"&&(i=n)}else t[n][1].type==="content"&&t.splice(n,1),!r&&t[n][1].type==="definition"&&(r=n);let a={type:"setextHeading",start:M({},t[l][1].start),end:M({},t[t.length-1][1].end)};return t[i][1].type="setextHeadingText",r?(t.splice(i,0,["enter",a,e]),t.splice(r+1,0,["exit",t[l][1],e]),t[l][1].end=M({},t[r][1].end)):t[l][1]=a,t.push(["exit",a,e]),t}function EE(t,e,n){let l=this,i;return r;function r(c){let s=l.events.length,f;for(;s--;)if(l.events[s][1].type!=="lineEnding"&&l.events[s][1].type!=="linePrefix"&&l.events[s][1].type!=="content"){f=l.events[s][1].type==="paragraph";break}return!l.parser.lazy[l.now().line]&&(l.interrupt||f)?(t.enter("setextHeadingLine"),i=c,a(c)):n(c)}function a(c){return t.enter("setextHeadingLineSequence"),u(c)}function u(c){return c===i?(t.consume(c),u):(t.exit("setextHeadingLineSequence"),Y(c)?q(t,o,"lineSuffix")(c):o(c))}function o(c){return c===null||_(c)?(t.exit("setextHeadingLine"),e(c)):n(c)}}var G1={tokenize:TE};function TE(t){let e=this,n=t.attempt(rn,l,t.attempt(this.parser.constructs.flowInitial,i,q(t,t.attempt(this.parser.constructs.flow,i,t.attempt(Xf,i)),"linePrefix")));return n;function l(r){if(r===null){t.consume(r);return}return t.enter("lineEndingBlank"),t.consume(r),t.exit("lineEndingBlank"),e.currentConstruct=void 0,n}function i(r){if(r===null){t.consume(r);return}return t.enter("lineEnding"),t.consume(r),t.exit("lineEnding"),e.currentConstruct=void 0,n}}var V1={resolveAll:F1()},X1=Z1("string"),Q1=Z1("text");function Z1(t){return{resolveAll:F1(t==="text"?AE:void 0),tokenize:e};function e(n){let l=this,i=this.parser.constructs[t],r=n.attempt(i,a,u);return a;function a(s){return c(s)?r(s):u(s)}function u(s){if(s===null){n.consume(s);return}return n.enter("data"),n.consume(s),o}function o(s){return c(s)?(n.exit("data"),r(s)):(n.consume(s),o)}function c(s){if(s===null)return!0;let f=i[s],p=-1;if(f)for(;++p<f.length;){let m=f[p];if(!m.previous||m.previous.call(l,l.previous))return!0}return!1}}}function F1(t){return e;function e(n,l){let i=-1,r;for(;++i<=n.length;)r===void 0?n[i]&&n[i][1].type==="data"&&(r=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==r+2&&(n[r][1].end=n[i-1][1].end,n.splice(r+2,i-r-2),i=r+2),r=void 0);return t?t(n,l):n}}function AE(t,e){let n=0;for(;++n<=t.length;)if((n===t.length||t[n][1].type==="lineEnding")&&t[n-1][1].type==="data"){let l=t[n-1][1],i=e.sliceStream(l),r=i.length,a=-1,u=0,o;for(;r--;){let c=i[r];if(typeof c=="string"){for(a=c.length;c.charCodeAt(a-1)===32;)u++,a--;if(a)break;a=-1}else if(c===-2)o=!0,u++;else if(c!==-1){r++;break}}if(e._contentTypeTextTrailing&&n===t.length&&(u=0),u){let c={type:n===t.length||o||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:r?a:l.start._bufferIndex+a,_index:l.start._index+r,line:l.end.line,column:l.end.column-u,offset:l.end.offset-u},end:M({},l.end)};l.end=M({},c.start),l.start.offset===l.end.offset?Object.assign(l,c):(t.splice(n,0,["enter",c,e],["exit",c,e]),n+=2)}n++}return t}var $f={};yp($f,{attentionMarkers:()=>NE,contentInitial:()=>CE,disable:()=>LE,document:()=>zE,flow:()=>DE,flowInitial:()=>ME,insideSpan:()=>_E,string:()=>OE,text:()=>RE});var zE={42:ee,43:ee,45:ee,48:ee,49:ee,50:ee,51:ee,52:ee,53:ee,54:ee,55:ee,56:ee,57:ee,62:fo},CE={91:Qf},ME={[-2]:ya,[-1]:ya,32:ya},DE={35:Ff,42:ql,45:[So,ql],60:If,61:So,95:ql,96:ho,126:ho},OE={38:po,92:mo},RE={[-5]:xa,[-4]:xa,[-3]:xa,33:Pf,38:po,42:ga,60:[Gf,Jf],91:Wf,92:[Zf,mo],93:Hl,95:ga,96:Vf},_E={null:[ga,V1]},NE={null:[42,95]},LE={null:[]};function K1(t,e,n){let l={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},r=[],a=[],u=[],o=!0,c={attempt:N(A),check:N(T),consume:k,enter:C,exit:w,interrupt:N(T,{interrupt:!0})},s={code:null,containerState:{},defineSkip:h,events:[],now:E,parser:t,previous:null,sliceSerialize:y,sliceStream:v,write:m},f=e.tokenize.call(s,c),p;return e.resolveAll&&r.push(e),s;function m(L){return a=ue(a,L),d(),a[a.length-1]!==null?[]:(S(e,0),s.events=al(r,s.events,s),s.events)}function y(L,G){return BE(v(L),G)}function v(L){return UE(a,L)}function E(){let{_bufferIndex:L,_index:G,line:j,column:P,offset:ct}=l;return{_bufferIndex:L,_index:G,line:j,column:P,offset:ct}}function h(L){i[L.line]=L.column,Q()}function d(){let L;for(;l._index<a.length;){let G=a[l._index];if(typeof G=="string")for(L=l._index,l._bufferIndex<0&&(l._bufferIndex=0);l._index===L&&l._bufferIndex<G.length;)g(G.charCodeAt(l._bufferIndex));else g(G)}}function g(L){o=void 0,p=L,f=f(L)}function k(L){_(L)?(l.line++,l.column=1,l.offset+=L===-3?2:1,Q()):L!==-1&&(l.column++,l.offset++),l._bufferIndex<0?l._index++:(l._bufferIndex++,l._bufferIndex===a[l._index].length&&(l._bufferIndex=-1,l._index++)),s.previous=L,o=!0}function C(L,G){let j=G||{};return j.type=L,j.start=E(),s.events.push(["enter",j,s]),u.push(j),j}function w(L){let G=u.pop();return G.end=E(),s.events.push(["exit",G,s]),G}function A(L,G){S(L,G.from)}function T(L,G){G.restore()}function N(L,G){return j;function j(P,ct,H){let Jt,x,Zt,ne;return Array.isArray(P)?mt(P):"tokenize"in P?mt([P]):b(P);function b(Rt){return Ze;function Ze(oe){let qe=oe!==null&&Rt[oe],je=oe!==null&&Rt.null,Yo=[...Array.isArray(qe)?qe:qe?[qe]:[],...Array.isArray(je)?je:je?[je]:[]];return mt(Yo)(oe)}}function mt(Rt){return Jt=Rt,x=0,Rt.length===0?H:Ae(Rt[x])}function Ae(Rt){return Ze;function Ze(oe){return ne=F(),Zt=Rt,Rt.partial||(s.currentConstruct=Rt),Rt.name&&s.parser.constructs.disable.null.includes(Rt.name)?St(oe):Rt.tokenize.call(G?Object.assign(Object.create(s),G):s,c,Ql,St)(oe)}}function Ql(Rt){return o=!0,L(Zt,ne),ct}function St(Rt){return o=!0,ne.restore(),++x<Jt.length?Ae(Jt[x]):H}}}function S(L,G){L.resolveAll&&!r.includes(L)&&r.push(L),L.resolve&&Ot(s.events,G,s.events.length-G,L.resolve(s.events.slice(G),s)),L.resolveTo&&(s.events=L.resolveTo(s.events,s))}function F(){let L=E(),G=s.previous,j=s.currentConstruct,P=s.events.length,ct=Array.from(u);return{from:P,restore:H};function H(){l=L,s.previous=G,s.currentConstruct=j,s.events.length=P,u=ct,Q()}}function Q(){l.line in i&&l.column<2&&(l.column=i[l.line],l.offset+=i[l.line]-1)}}function UE(t,e){let n=e.start._index,l=e.start._bufferIndex,i=e.end._index,r=e.end._bufferIndex,a;if(n===i)a=[t[n].slice(l,r)];else{if(a=t.slice(n,i),l>-1){let u=a[0];typeof u=="string"?a[0]=u.slice(l):a.shift()}r>0&&a.push(t[i].slice(0,r))}return a}function BE(t,e){let n=-1,l=[],i;for(;++n<t.length;){let r=t[n],a;if(typeof r=="string")a=r;else switch(r){case-5:{a="\r";break}case-4:{a=`
`;break}case-3:{a=`\r
`;break}case-2:{a=e?" ":"	";break}case-1:{if(!e&&i)continue;a=" ";break}default:a=String.fromCharCode(r)}i=r===-2,l.push(a)}return l.join("")}function tm(t){let l={constructs:co([$f,...(t||{}).extensions||[]]),content:i(U1),defined:[],document:i(H1),flow:i(G1),lazy:{},string:i(X1),text:i(Q1)};return l;function i(r){return a;function a(u){return K1(l,r,u)}}}function em(t){for(;!yo(t););return t}var I1=/[\0\t\n\r]/g;function nm(){let t=1,e="",n=!0,l;return i;function i(r,a,u){let o=[],c,s,f,p,m;for(r=e+(typeof r=="string"?r.toString():new TextDecoder(a||void 0).decode(r)),f=0,e="",n&&(r.charCodeAt(0)===65279&&f++,n=void 0);f<r.length;){if(I1.lastIndex=f,c=I1.exec(r),p=c&&c.index!==void 0?c.index:r.length,m=r.charCodeAt(p),!c){e=r.slice(f);break}if(m===10&&f===p&&l)o.push(-3),l=void 0;else switch(l&&(o.push(-5),l=void 0),f<p&&(o.push(r.slice(f,p)),t+=p-f),m){case 0:{o.push(65533),t++;break}case 9:{for(s=Math.ceil(t/4)*4,o.push(-2);t++<s;)o.push(-1);break}case 10:{o.push(-4),t=1;break}default:l=!0,t=1}f=p+1}return u&&(l&&o.push(-5),e&&o.push(e),o.push(null)),o}}var HE=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function J1(t){return t.replace(HE,qE)}function qE(t,e,n){if(e)return e;if(n.charCodeAt(0)===35){let i=n.charCodeAt(1),r=i===120||i===88;return so(n.slice(r?2:1),r?16:10)}return Gi(n)||t}var W1={}.hasOwnProperty;function lm(t,e,n){return typeof e!="string"&&(n=e,e=void 0),jE(n)(em(tm(n).document().write(nm()(t,e,!0))))}function jE(t){let e={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(mp),autolinkProtocol:N,autolinkEmail:N,atxHeading:r(cp),blockQuote:r(oe),characterEscape:N,characterReference:N,codeFenced:r(qe),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:r(qe,a),codeText:r(je,a),codeTextData:N,data:N,codeFlowValue:N,definition:r(Yo),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:r(fx),hardBreakEscape:r(sp),hardBreakTrailing:r(sp),htmlFlow:r(fp,a),htmlFlowData:N,htmlText:r(fp,a),htmlTextData:N,image:r(mx),label:a,link:r(mp),listItem:r(px),listItemValue:p,listOrdered:r(pp,f),listUnordered:r(pp),paragraph:r(hx),reference:b,referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:r(cp),strong:r(dx),thematicBreak:r(yx)},exit:{atxHeading:o(),atxHeadingSequence:C,autolink:o(),autolinkEmail:Ze,autolinkProtocol:Rt,blockQuote:o(),characterEscapeValue:S,characterReferenceMarkerHexadecimal:Ae,characterReferenceMarkerNumeric:Ae,characterReferenceValue:Ql,characterReference:St,codeFenced:o(E),codeFencedFence:v,codeFencedFenceInfo:m,codeFencedFenceMeta:y,codeFlowValue:S,codeIndented:o(h),codeText:o(j),codeTextData:S,data:S,definition:o(),definitionDestinationString:k,definitionLabelString:d,definitionTitleString:g,emphasis:o(),hardBreakEscape:o(Q),hardBreakTrailing:o(Q),htmlFlow:o(L),htmlFlowData:S,htmlText:o(G),htmlTextData:S,image:o(ct),label:Jt,labelText:H,lineEnding:F,link:o(P),listItem:o(),listOrdered:o(),listUnordered:o(),paragraph:o(),referenceString:mt,resourceDestinationString:x,resourceTitleString:Zt,resource:ne,setextHeading:o(T),setextHeadingLineSequence:A,setextHeadingText:w,strong:o(),thematicBreak:o()}};$1(e,(t||{}).mdastExtensions||[]);let n={};return l;function l(z){let R={type:"root",children:[]},Z={stack:[R],tokenStack:[],config:e,enter:u,exit:c,buffer:a,resume:s,data:n},nt=[],pt=-1;for(;++pt<z.length;)if(z[pt][1].type==="listOrdered"||z[pt][1].type==="listUnordered")if(z[pt][0]==="enter")nt.push(pt);else{let Ye=nt.pop();pt=i(z,Ye,pt)}for(pt=-1;++pt<z.length;){let Ye=e[z[pt][0]];W1.call(Ye,z[pt][1].type)&&Ye[z[pt][1].type].call(Object.assign({sliceSerialize:z[pt][2].sliceSerialize},Z),z[pt][1])}if(Z.tokenStack.length>0){let Ye=Z.tokenStack[Z.tokenStack.length-1];(Ye[1]||P1).call(Z,void 0,Ye[0])}for(R.position={start:ul(z.length>0?z[0][1].start:{line:1,column:1,offset:0}),end:ul(z.length>0?z[z.length-2][1].end:{line:1,column:1,offset:0})},pt=-1;++pt<e.transforms.length;)R=e.transforms[pt](R)||R;return R}function i(z,R,Z){let nt=R-1,pt=-1,Ye=!1,fl,un,tr,er;for(;++nt<=Z;){let ge=z[nt];switch(ge[1].type){case"listUnordered":case"listOrdered":case"blockQuote":{ge[0]==="enter"?pt++:pt--,er=void 0;break}case"lineEndingBlank":{ge[0]==="enter"&&(fl&&!er&&!pt&&!tr&&(tr=nt),er=void 0);break}case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:er=void 0}if(!pt&&ge[0]==="enter"&&ge[1].type==="listItemPrefix"||pt===-1&&ge[0]==="exit"&&(ge[1].type==="listUnordered"||ge[1].type==="listOrdered")){if(fl){let Zl=nt;for(un=void 0;Zl--;){let on=z[Zl];if(on[1].type==="lineEnding"||on[1].type==="lineEndingBlank"){if(on[0]==="exit")continue;un&&(z[un][1].type="lineEndingBlank",Ye=!0),on[1].type="lineEnding",un=Zl}else if(!(on[1].type==="linePrefix"||on[1].type==="blockQuotePrefix"||on[1].type==="blockQuotePrefixWhitespace"||on[1].type==="blockQuoteMarker"||on[1].type==="listItemIndent"))break}tr&&(!un||tr<un)&&(fl._spread=!0),fl.end=Object.assign({},un?z[un][1].start:ge[1].end),z.splice(un||nt,0,["exit",fl,ge[2]]),nt++,Z++}if(ge[1].type==="listItemPrefix"){let Zl={type:"listItem",_spread:!1,start:Object.assign({},ge[1].start),end:void 0};fl=Zl,z.splice(nt,0,["enter",Zl,ge[2]]),nt++,Z++,tr=void 0,er=!0}}}return z[R][1]._spread=Ye,Z}function r(z,R){return Z;function Z(nt){u.call(this,z(nt),nt),R&&R.call(this,nt)}}function a(){this.stack.push({type:"fragment",children:[]})}function u(z,R,Z){this.stack[this.stack.length-1].children.push(z),this.stack.push(z),this.tokenStack.push([R,Z||void 0]),z.position={start:ul(R.start),end:void 0}}function o(z){return R;function R(Z){z&&z.call(this,Z),c.call(this,Z)}}function c(z,R){let Z=this.stack.pop(),nt=this.tokenStack.pop();if(nt)nt[0].type!==z.type&&(R?R.call(this,z,nt[0]):(nt[1]||P1).call(this,z,nt[0]));else throw new Error("Cannot close `"+z.type+"` ("+il({start:z.start,end:z.end})+"): it\u2019s not open");Z.position.end=ul(z.end)}function s(){return Nl(this.stack.pop())}function f(){this.data.expectingFirstListItemValue=!0}function p(z){if(this.data.expectingFirstListItemValue){let R=this.stack[this.stack.length-2];R.start=Number.parseInt(this.sliceSerialize(z),10),this.data.expectingFirstListItemValue=void 0}}function m(){let z=this.resume(),R=this.stack[this.stack.length-1];R.lang=z}function y(){let z=this.resume(),R=this.stack[this.stack.length-1];R.meta=z}function v(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function E(){let z=this.resume(),R=this.stack[this.stack.length-1];R.value=z.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function h(){let z=this.resume(),R=this.stack[this.stack.length-1];R.value=z.replace(/(\r?\n|\r)$/g,"")}function d(z){let R=this.resume(),Z=this.stack[this.stack.length-1];Z.label=R,Z.identifier=te(this.sliceSerialize(z)).toLowerCase()}function g(){let z=this.resume(),R=this.stack[this.stack.length-1];R.title=z}function k(){let z=this.resume(),R=this.stack[this.stack.length-1];R.url=z}function C(z){let R=this.stack[this.stack.length-1];if(!R.depth){let Z=this.sliceSerialize(z).length;R.depth=Z}}function w(){this.data.setextHeadingSlurpLineEnding=!0}function A(z){let R=this.stack[this.stack.length-1];R.depth=this.sliceSerialize(z).codePointAt(0)===61?1:2}function T(){this.data.setextHeadingSlurpLineEnding=void 0}function N(z){let Z=this.stack[this.stack.length-1].children,nt=Z[Z.length-1];(!nt||nt.type!=="text")&&(nt=gx(),nt.position={start:ul(z.start),end:void 0},Z.push(nt)),this.stack.push(nt)}function S(z){let R=this.stack.pop();R.value+=this.sliceSerialize(z),R.position.end=ul(z.end)}function F(z){let R=this.stack[this.stack.length-1];if(this.data.atHardBreak){let Z=R.children[R.children.length-1];Z.position.end=ul(z.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&e.canContainEols.includes(R.type)&&(N.call(this,z),S.call(this,z))}function Q(){this.data.atHardBreak=!0}function L(){let z=this.resume(),R=this.stack[this.stack.length-1];R.value=z}function G(){let z=this.resume(),R=this.stack[this.stack.length-1];R.value=z}function j(){let z=this.resume(),R=this.stack[this.stack.length-1];R.value=z}function P(){let z=this.stack[this.stack.length-1];if(this.data.inReference){let R=this.data.referenceType||"shortcut";z.type+="Reference",z.referenceType=R,delete z.url,delete z.title}else delete z.identifier,delete z.label;this.data.referenceType=void 0}function ct(){let z=this.stack[this.stack.length-1];if(this.data.inReference){let R=this.data.referenceType||"shortcut";z.type+="Reference",z.referenceType=R,delete z.url,delete z.title}else delete z.identifier,delete z.label;this.data.referenceType=void 0}function H(z){let R=this.sliceSerialize(z),Z=this.stack[this.stack.length-2];Z.label=J1(R),Z.identifier=te(R).toLowerCase()}function Jt(){let z=this.stack[this.stack.length-1],R=this.resume(),Z=this.stack[this.stack.length-1];if(this.data.inReference=!0,Z.type==="link"){let nt=z.children;Z.children=nt}else Z.alt=R}function x(){let z=this.resume(),R=this.stack[this.stack.length-1];R.url=z}function Zt(){let z=this.resume(),R=this.stack[this.stack.length-1];R.title=z}function ne(){this.data.inReference=void 0}function b(){this.data.referenceType="collapsed"}function mt(z){let R=this.resume(),Z=this.stack[this.stack.length-1];Z.label=R,Z.identifier=te(this.sliceSerialize(z)).toLowerCase(),this.data.referenceType="full"}function Ae(z){this.data.characterReferenceType=z.type}function Ql(z){let R=this.sliceSerialize(z),Z=this.data.characterReferenceType,nt;Z?(nt=so(R,Z==="characterReferenceMarkerNumeric"?10:16),this.data.characterReferenceType=void 0):nt=Gi(R);let pt=this.stack[this.stack.length-1];pt.value+=nt}function St(z){let R=this.stack.pop();R.position.end=ul(z.end)}function Rt(z){S.call(this,z);let R=this.stack[this.stack.length-1];R.url=this.sliceSerialize(z)}function Ze(z){S.call(this,z);let R=this.stack[this.stack.length-1];R.url="mailto:"+this.sliceSerialize(z)}function oe(){return{type:"blockquote",children:[]}}function qe(){return{type:"code",lang:null,meta:null,value:""}}function je(){return{type:"inlineCode",value:""}}function Yo(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function fx(){return{type:"emphasis",children:[]}}function cp(){return{type:"heading",depth:0,children:[]}}function sp(){return{type:"break"}}function fp(){return{type:"html",value:""}}function mx(){return{type:"image",title:null,url:"",alt:null}}function mp(){return{type:"link",title:null,url:"",children:[]}}function pp(z){return{type:"list",ordered:z.type==="listOrdered",start:null,spread:z._spread,children:[]}}function px(z){return{type:"listItem",spread:z._spread,checked:null,children:[]}}function hx(){return{type:"paragraph",children:[]}}function dx(){return{type:"strong",children:[]}}function gx(){return{type:"text",value:""}}function yx(){return{type:"thematicBreak"}}}function ul(t){return{line:t.line,column:t.column,offset:t.offset}}function $1(t,e){let n=-1;for(;++n<e.length;){let l=e[n];Array.isArray(l)?$1(t,l):YE(t,l)}}function YE(t,e){let n;for(n in e)if(W1.call(e,n))switch(n){case"canContainEols":{let l=e[n];l&&t[n].push(...l);break}case"transforms":{let l=e[n];l&&t[n].push(...l);break}case"enter":case"exit":{let l=e[n];l&&Object.assign(t[n],l);break}}}function P1(t,e){throw t?new Error("Cannot close `"+t.type+"` ("+il({start:t.start,end:t.end})+"): a different token (`"+e.type+"`, "+il({start:e.start,end:e.end})+") is open"):new Error("Cannot close document, a token (`"+e.type+"`, "+il({start:e.start,end:e.end})+") is still open")}function ko(t){let e=this;e.parser=n;function n(l){return lm(l,wt(M(M({},e.data("settings")),t),{extensions:e.data("micromarkExtensions")||[],mdastExtensions:e.data("fromMarkdownExtensions")||[]}))}}function tb(t,e){let n={type:"element",tagName:"blockquote",properties:{},children:t.wrap(t.all(e),!0)};return t.patch(e,n),t.applyData(e,n)}function eb(t,e){let n={type:"element",tagName:"br",properties:{},children:[]};return t.patch(e,n),[t.applyData(e,n),{type:"text",value:`
`}]}function nb(t,e){let n=e.value?e.value+`
`:"",l={};e.lang&&(l.className=["language-"+e.lang]);let i={type:"element",tagName:"code",properties:l,children:[{type:"text",value:n}]};return e.meta&&(i.data={meta:e.meta}),t.patch(e,i),i=t.applyData(e,i),i={type:"element",tagName:"pre",properties:{},children:[i]},t.patch(e,i),i}function lb(t,e){let n={type:"element",tagName:"del",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function ib(t,e){let n={type:"element",tagName:"em",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function rb(t,e){let n=typeof t.options.clobberPrefix=="string"?t.options.clobberPrefix:"user-content-",l=String(e.identifier).toUpperCase(),i=He(l.toLowerCase()),r=t.footnoteOrder.indexOf(l),a,u=t.footnoteCounts.get(l);u===void 0?(u=0,t.footnoteOrder.push(l),a=t.footnoteOrder.length):a=r+1,u+=1,t.footnoteCounts.set(l,u);let o={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+i,id:n+"fnref-"+i+(u>1?"-"+u:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(a)}]};t.patch(e,o);let c={type:"element",tagName:"sup",properties:{},children:[o]};return t.patch(e,c),t.applyData(e,c)}function ab(t,e){let n={type:"element",tagName:"h"+e.depth,properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function ub(t,e){if(t.options.allowDangerousHtml){let n={type:"raw",value:e.value};return t.patch(e,n),t.applyData(e,n)}}function wo(t,e){let n=e.referenceType,l="]";if(n==="collapsed"?l+="[]":n==="full"&&(l+="["+(e.label||e.identifier)+"]"),e.type==="imageReference")return[{type:"text",value:"!["+e.alt+l}];let i=t.all(e),r=i[0];r&&r.type==="text"?r.value="["+r.value:i.unshift({type:"text",value:"["});let a=i[i.length-1];return a&&a.type==="text"?a.value+=l:i.push({type:"text",value:l}),i}function ob(t,e){let n=String(e.identifier).toUpperCase(),l=t.definitionById.get(n);if(!l)return wo(t,e);let i={src:He(l.url||""),alt:e.alt};l.title!==null&&l.title!==void 0&&(i.title=l.title);let r={type:"element",tagName:"img",properties:i,children:[]};return t.patch(e,r),t.applyData(e,r)}function cb(t,e){let n={src:He(e.url)};e.alt!==null&&e.alt!==void 0&&(n.alt=e.alt),e.title!==null&&e.title!==void 0&&(n.title=e.title);let l={type:"element",tagName:"img",properties:n,children:[]};return t.patch(e,l),t.applyData(e,l)}function sb(t,e){let n={type:"text",value:e.value.replace(/\r?\n|\r/g," ")};t.patch(e,n);let l={type:"element",tagName:"code",properties:{},children:[n]};return t.patch(e,l),t.applyData(e,l)}function fb(t,e){let n=String(e.identifier).toUpperCase(),l=t.definitionById.get(n);if(!l)return wo(t,e);let i={href:He(l.url||"")};l.title!==null&&l.title!==void 0&&(i.title=l.title);let r={type:"element",tagName:"a",properties:i,children:t.all(e)};return t.patch(e,r),t.applyData(e,r)}function mb(t,e){let n={href:He(e.url)};e.title!==null&&e.title!==void 0&&(n.title=e.title);let l={type:"element",tagName:"a",properties:n,children:t.all(e)};return t.patch(e,l),t.applyData(e,l)}function pb(t,e,n){let l=t.all(e),i=n?GE(n):hb(e),r={},a=[];if(typeof e.checked=="boolean"){let s=l[0],f;s&&s.type==="element"&&s.tagName==="p"?f=s:(f={type:"element",tagName:"p",properties:{},children:[]},l.unshift(f)),f.children.length>0&&f.children.unshift({type:"text",value:" "}),f.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:e.checked,disabled:!0},children:[]}),r.className=["task-list-item"]}let u=-1;for(;++u<l.length;){let s=l[u];(i||u!==0||s.type!=="element"||s.tagName!=="p")&&a.push({type:"text",value:`
`}),s.type==="element"&&s.tagName==="p"&&!i?a.push(...s.children):a.push(s)}let o=l[l.length-1];o&&(i||o.type!=="element"||o.tagName!=="p")&&a.push({type:"text",value:`
`});let c={type:"element",tagName:"li",properties:r,children:a};return t.patch(e,c),t.applyData(e,c)}function GE(t){let e=!1;if(t.type==="list"){e=t.spread||!1;let n=t.children,l=-1;for(;!e&&++l<n.length;)e=hb(n[l])}return e}function hb(t){let e=t.spread;return e==null?t.children.length>1:e}function db(t,e){let n={},l=t.all(e),i=-1;for(typeof e.start=="number"&&e.start!==1&&(n.start=e.start);++i<l.length;){let a=l[i];if(a.type==="element"&&a.tagName==="li"&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let r={type:"element",tagName:e.ordered?"ol":"ul",properties:n,children:t.wrap(l,!0)};return t.patch(e,r),t.applyData(e,r)}function gb(t,e){let n={type:"element",tagName:"p",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function yb(t,e){let n={type:"root",children:t.wrap(t.all(e))};return t.patch(e,n),t.applyData(e,n)}function bb(t,e){let n={type:"element",tagName:"strong",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function xb(t,e){let n=t.all(e),l=n.shift(),i=[];if(l){let a={type:"element",tagName:"thead",properties:{},children:t.wrap([l],!0)};t.patch(e.children[0],a),i.push(a)}if(n.length>0){let a={type:"element",tagName:"tbody",properties:{},children:t.wrap(n,!0)},u=ji(e.children[1]),o=uo(e.children[e.children.length-1]);u&&o&&(a.position={start:u,end:o}),i.push(a)}let r={type:"element",tagName:"table",properties:{},children:t.wrap(i,!0)};return t.patch(e,r),t.applyData(e,r)}function vb(t,e,n){let l=n?n.children:void 0,r=(l?l.indexOf(e):1)===0?"th":"td",a=n&&n.type==="table"?n.align:void 0,u=a?a.length:e.children.length,o=-1,c=[];for(;++o<u;){let f=e.children[o],p={},m=a?a[o]:void 0;m&&(p.align=m);let y={type:"element",tagName:r,properties:p,children:[]};f&&(y.children=t.all(f),t.patch(f,y),y=t.applyData(f,y)),c.push(y)}let s={type:"element",tagName:"tr",properties:{},children:t.wrap(c,!0)};return t.patch(e,s),t.applyData(e,s)}function Sb(t,e){let n={type:"element",tagName:"td",properties:{},children:t.all(e)};return t.patch(e,n),t.applyData(e,n)}function wb(t){let e=String(t),n=/\r?\n|\r/g,l=n.exec(e),i=0,r=[];for(;l;)r.push(kb(e.slice(i,l.index),i>0,!0),l[0]),i=l.index+l[0].length,l=n.exec(e);return r.push(kb(e.slice(i),i>0,!1)),r.join("")}function kb(t,e,n){let l=0,i=t.length;if(e){let r=t.codePointAt(l);for(;r===9||r===32;)l++,r=t.codePointAt(l)}if(n){let r=t.codePointAt(i-1);for(;r===9||r===32;)i--,r=t.codePointAt(i-1)}return i>l?t.slice(l,i):""}function Eb(t,e){let n={type:"text",value:wb(String(e.value))};return t.patch(e,n),t.applyData(e,n)}function Tb(t,e){let n={type:"element",tagName:"hr",properties:{},children:[]};return t.patch(e,n),t.applyData(e,n)}var Ab={blockquote:tb,break:eb,code:nb,delete:lb,emphasis:ib,footnoteReference:rb,heading:ab,html:ub,imageReference:ob,image:cb,inlineCode:sb,linkReference:fb,link:mb,listItem:pb,list:db,paragraph:gb,root:yb,strong:bb,table:xb,tableCell:Sb,tableRow:vb,text:Eb,thematicBreak:Tb,toml:Eo,yaml:Eo,definition:Eo,footnoteDefinition:Eo};function Eo(){}var zb=typeof self=="object"?self:globalThis,ZE=(t,e)=>{let n=(i,r)=>(t.set(r,i),i),l=i=>{if(t.has(i))return t.get(i);let[r,a]=e[i];switch(r){case 0:case-1:return n(a,i);case 1:{let u=n([],i);for(let o of a)u.push(l(o));return u}case 2:{let u=n({},i);for(let[o,c]of a)u[l(o)]=l(c);return u}case 3:return n(new Date(a),i);case 4:{let{source:u,flags:o}=a;return n(new RegExp(u,o),i)}case 5:{let u=n(new Map,i);for(let[o,c]of a)u.set(l(o),l(c));return u}case 6:{let u=n(new Set,i);for(let o of a)u.add(l(o));return u}case 7:{let{name:u,message:o}=a;return n(new zb[u](o),i)}case 8:return n(BigInt(a),i);case"BigInt":return n(Object(BigInt(a)),i);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{let{buffer:u}=new Uint8Array(a);return n(new DataView(u),a)}}return n(new zb[r](a),i)};return l},am=t=>ZE(new Map,t)(0);var Vi="",{toString:FE}={},{keys:KE}=Object,va=t=>{let e=typeof t;if(e!=="object"||!t)return[0,e];let n=FE.call(t).slice(8,-1);switch(n){case"Array":return[1,Vi];case"Object":return[2,Vi];case"Date":return[3,Vi];case"RegExp":return[4,Vi];case"Map":return[5,Vi];case"Set":return[6,Vi];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},Ao=([t,e])=>t===0&&(e==="function"||e==="symbol"),IE=(t,e,n,l)=>{let i=(a,u)=>{let o=l.push(a)-1;return n.set(u,o),o},r=a=>{if(n.has(a))return n.get(a);let[u,o]=va(a);switch(u){case 0:{let s=a;switch(o){case"bigint":u=8,s=a.toString();break;case"function":case"symbol":if(t)throw new TypeError("unable to serialize "+o);s=null;break;case"undefined":return i([-1],a)}return i([u,s],a)}case 1:{if(o){let p=a;return o==="DataView"?p=new Uint8Array(a.buffer):o==="ArrayBuffer"&&(p=new Uint8Array(a)),i([o,[...p]],a)}let s=[],f=i([u,s],a);for(let p of a)s.push(r(p));return f}case 2:{if(o)switch(o){case"BigInt":return i([o,a.toString()],a);case"Boolean":case"Number":case"String":return i([o,a.valueOf()],a)}if(e&&"toJSON"in a)return r(a.toJSON());let s=[],f=i([u,s],a);for(let p of KE(a))(t||!Ao(va(a[p])))&&s.push([r(p),r(a[p])]);return f}case 3:return i([u,a.toISOString()],a);case 4:{let{source:s,flags:f}=a;return i([u,{source:s,flags:f}],a)}case 5:{let s=[],f=i([u,s],a);for(let[p,m]of a)(t||!(Ao(va(p))||Ao(va(m))))&&s.push([r(p),r(m)]);return f}case 6:{let s=[],f=i([u,s],a);for(let p of a)(t||!Ao(va(p)))&&s.push(r(p));return f}}let{message:c}=a;return i([u,{name:o,message:c}],a)};return r},um=(t,{json:e,lossy:n}={})=>{let l=[];return IE(!(e||n),!!e,new Map,l)(t),l};var Xi=typeof structuredClone=="function"?(t,e)=>e&&("json"in e||"lossy"in e)?am(um(t,e)):structuredClone(t):(t,e)=>am(um(t,e));function JE(t,e){let n=[{type:"text",value:"\u21A9"}];return e>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(e)}]}),n}function PE(t,e){return"Back to reference "+(t+1)+(e>1?"-"+e:"")}function Rb(t){let e=typeof t.options.clobberPrefix=="string"?t.options.clobberPrefix:"user-content-",n=t.options.footnoteBackContent||JE,l=t.options.footnoteBackLabel||PE,i=t.options.footnoteLabel||"Footnotes",r=t.options.footnoteLabelTagName||"h2",a=t.options.footnoteLabelProperties||{className:["sr-only"]},u=[],o=-1;for(;++o<t.footnoteOrder.length;){let c=t.footnoteById.get(t.footnoteOrder[o]);if(!c)continue;let s=t.all(c),f=String(c.identifier).toUpperCase(),p=He(f.toLowerCase()),m=0,y=[],v=t.footnoteCounts.get(f);for(;v!==void 0&&++m<=v;){y.length>0&&y.push({type:"text",value:" "});let d=typeof n=="string"?n:n(o,m);typeof d=="string"&&(d={type:"text",value:d}),y.push({type:"element",tagName:"a",properties:{href:"#"+e+"fnref-"+p+(m>1?"-"+m:""),dataFootnoteBackref:"",ariaLabel:typeof l=="string"?l:l(o,m),className:["data-footnote-backref"]},children:Array.isArray(d)?d:[d]})}let E=s[s.length-1];if(E&&E.type==="element"&&E.tagName==="p"){let d=E.children[E.children.length-1];d&&d.type==="text"?d.value+=" ":E.children.push({type:"text",value:" "}),E.children.push(...y)}else s.push(...y);let h={type:"element",tagName:"li",properties:{id:e+"fn-"+p},children:t.wrap(s,!0)};t.patch(c,h),u.push(h)}if(u.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:r,properties:wt(M({},Xi(a)),{id:"footnote-label"}),children:[{type:"text",value:i}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:t.wrap(u,!0)},{type:"text",value:`
`}]}}var ol=function(t){if(t==null)return e2;if(typeof t=="function")return zo(t);if(typeof t=="object")return Array.isArray(t)?WE(t):$E(t);if(typeof t=="string")return t2(t);throw new Error("Expected function, string, or object as test")};function WE(t){let e=[],n=-1;for(;++n<t.length;)e[n]=ol(t[n]);return zo(l);function l(...i){let r=-1;for(;++r<e.length;)if(e[r].apply(this,i))return!0;return!1}}function $E(t){let e=t;return zo(n);function n(l){let i=l,r;for(r in t)if(i[r]!==e[r])return!1;return!0}}function t2(t){return zo(e);function e(n){return n&&n.type===t}}function zo(t){return e;function e(n,l,i){return!!(n2(n)&&t.call(this,n,typeof l=="number"?l:void 0,i||void 0))}}function e2(){return!0}function n2(t){return t!==null&&typeof t=="object"&&"type"in t}var _b=[],Co=!0,jl=!1,Mo="skip";function Sa(t,e,n,l){let i;typeof e=="function"&&typeof n!="function"?(l=n,n=e):i=e;let r=ol(i),a=l?-1:1;u(t,void 0,[])();function u(o,c,s){let f=o&&typeof o=="object"?o:{};if(typeof f.type=="string"){let m=typeof f.tagName=="string"?f.tagName:typeof f.name=="string"?f.name:void 0;Object.defineProperty(p,"name",{value:"node ("+(o.type+(m?"<"+m+">":""))+")"})}return p;function p(){let m=_b,y,v,E;if((!e||r(o,c,s[s.length-1]||void 0))&&(m=l2(n(o,s)),m[0]===jl))return m;if("children"in o&&o.children){let h=o;if(h.children&&m[0]!==Mo)for(v=(l?h.children.length:-1)+a,E=s.concat(h);v>-1&&v<h.children.length;){let d=h.children[v];if(y=u(d,v,E)(),y[0]===jl)return y;v=typeof y[1]=="number"?y[1]:v+a}}return m}}}function l2(t){return Array.isArray(t)?t:typeof t=="number"?[Co,t]:t==null?_b:[t]}function Yl(t,e,n,l){let i,r,a;typeof e=="function"&&typeof n!="function"?(r=void 0,a=e,i=n):(r=e,a=n,i=l),Sa(t,r,u,i);function u(o,c){let s=c[c.length-1],f=s?s.children.indexOf(o):void 0;return a(o,f,s)}}var om={}.hasOwnProperty,i2={};function Lb(t,e){let n=e||i2,l=new Map,i=new Map,r=new Map,a=M(M({},Ab),n.handlers),u={all:c,applyData:a2,definitionById:l,footnoteById:i,footnoteCounts:r,footnoteOrder:[],handlers:a,one:o,options:n,patch:r2,wrap:o2};return Yl(t,function(s){if(s.type==="definition"||s.type==="footnoteDefinition"){let f=s.type==="definition"?l:i,p=String(s.identifier).toUpperCase();f.has(p)||f.set(p,s)}}),u;function o(s,f){let p=s.type,m=u.handlers[p];if(om.call(u.handlers,p)&&m)return m(u,s,f);if(u.options.passThrough&&u.options.passThrough.includes(p)){if("children"in s){let v=s,{children:E}=v,h=Ca(v,["children"]),d=Xi(h);return d.children=u.all(s),d}return Xi(s)}return(u.options.unknownHandler||u2)(u,s,f)}function c(s){let f=[];if("children"in s){let p=s.children,m=-1;for(;++m<p.length;){let y=u.one(p[m],s);if(y){if(m&&p[m-1].type==="break"&&(!Array.isArray(y)&&y.type==="text"&&(y.value=Nb(y.value)),!Array.isArray(y)&&y.type==="element")){let v=y.children[0];v&&v.type==="text"&&(v.value=Nb(v.value))}Array.isArray(y)?f.push(...y):f.push(y)}}}return f}}function r2(t,e){t.position&&(e.position=Uf(t))}function a2(t,e){let n=e;if(t&&t.data){let l=t.data.hName,i=t.data.hChildren,r=t.data.hProperties;if(typeof l=="string")if(n.type==="element")n.tagName=l;else{let a="children"in n?n.children:[n];n={type:"element",tagName:l,properties:{},children:a}}n.type==="element"&&r&&Object.assign(n.properties,Xi(r)),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function u2(t,e){let n=e.data||{},l="value"in e&&!(om.call(n,"hProperties")||om.call(n,"hChildren"))?{type:"text",value:e.value}:{type:"element",tagName:"div",properties:{},children:t.all(e)};return t.patch(e,l),t.applyData(e,l)}function o2(t,e){let n=[],l=-1;for(e&&n.push({type:"text",value:`
`});++l<t.length;)l&&n.push({type:"text",value:`
`}),n.push(t[l]);return e&&t.length>0&&n.push({type:"text",value:`
`}),n}function Nb(t){let e=0,n=t.charCodeAt(e);for(;n===9||n===32;)e++,n=t.charCodeAt(e);return t.slice(e)}function Do(t,e){let n=Lb(t,e),l=n.one(t,void 0),i=Rb(n),r=Array.isArray(l)?{type:"root",children:l}:l||{type:"root",children:[]};return i&&("children"in r,r.children.push({type:"text",value:`
`},i)),r}function Oo(t,e){return t&&"run"in t?function(n,l){return nr(this,null,function*(){let i=Do(n,M({file:l},e));yield t.run(i,l)})}:function(n,l){return Do(n,M({file:l},t||e))}}function cm(t){if(t)throw t}var No=Fe(Xb(),1);function ka(t){if(typeof t!="object"||t===null)return!1;let e=Object.getPrototypeOf(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)}function sm(){let t=[],e={run:n,use:l};return e;function n(...i){let r=-1,a=i.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);u(null,...i);function u(o,...c){let s=t[++r],f=-1;if(o){a(o);return}for(;++f<i.length;)(c[f]===null||c[f]===void 0)&&(c[f]=i[f]);i=c,s?Qb(s,u)(...c):a(null,...c)}}function l(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return t.push(i),e}}function Qb(t,e){let n;return l;function l(...a){let u=t.length>a.length,o;u&&a.push(i);try{o=t.apply(this,a)}catch(c){let s=c;if(u&&n)throw s;return i(s)}u||(o&&o.then&&typeof o.then=="function"?o.then(r,i):o instanceof Error?i(o):r(o))}function i(a,...u){n||(n=!0,e(a,...u))}function r(a){i(null,a)}}var Qe={basename:c2,dirname:s2,extname:f2,join:m2,sep:"/"};function c2(t,e){if(e!==void 0&&typeof e!="string")throw new TypeError('"ext" argument must be a string');wa(t);let n=0,l=-1,i=t.length,r;if(e===void 0||e.length===0||e.length>t.length){for(;i--;)if(t.codePointAt(i)===47){if(r){n=i+1;break}}else l<0&&(r=!0,l=i+1);return l<0?"":t.slice(n,l)}if(e===t)return"";let a=-1,u=e.length-1;for(;i--;)if(t.codePointAt(i)===47){if(r){n=i+1;break}}else a<0&&(r=!0,a=i+1),u>-1&&(t.codePointAt(i)===e.codePointAt(u--)?u<0&&(l=i):(u=-1,l=a));return n===l?l=a:l<0&&(l=t.length),t.slice(n,l)}function s2(t){if(wa(t),t.length===0)return".";let e=-1,n=t.length,l;for(;--n;)if(t.codePointAt(n)===47){if(l){e=n;break}}else l||(l=!0);return e<0?t.codePointAt(0)===47?"/":".":e===1&&t.codePointAt(0)===47?"//":t.slice(0,e)}function f2(t){wa(t);let e=t.length,n=-1,l=0,i=-1,r=0,a;for(;e--;){let u=t.codePointAt(e);if(u===47){if(a){l=e+1;break}continue}n<0&&(a=!0,n=e+1),u===46?i<0?i=e:r!==1&&(r=1):i>-1&&(r=-1)}return i<0||n<0||r===0||r===1&&i===n-1&&i===l+1?"":t.slice(i,n)}function m2(...t){let e=-1,n;for(;++e<t.length;)wa(t[e]),t[e]&&(n=n===void 0?t[e]:n+"/"+t[e]);return n===void 0?".":p2(n)}function p2(t){wa(t);let e=t.codePointAt(0)===47,n=h2(t,!e);return n.length===0&&!e&&(n="."),n.length>0&&t.codePointAt(t.length-1)===47&&(n+="/"),e?"/"+n:n}function h2(t,e){let n="",l=0,i=-1,r=0,a=-1,u,o;for(;++a<=t.length;){if(a<t.length)u=t.codePointAt(a);else{if(u===47)break;u=47}if(u===47){if(!(i===a-1||r===1))if(i!==a-1&&r===2){if(n.length<2||l!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(o=n.lastIndexOf("/"),o!==n.length-1){o<0?(n="",l=0):(n=n.slice(0,o),l=n.length-1-n.lastIndexOf("/")),i=a,r=0;continue}}else if(n.length>0){n="",l=0,i=a,r=0;continue}}e&&(n=n.length>0?n+"/..":"..",l=2)}else n.length>0?n+="/"+t.slice(i+1,a):n=t.slice(i+1,a),l=a-i-1;i=a,r=0}else u===46&&r>-1?r++:r=-1}return n}function wa(t){if(typeof t!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}var Zb={cwd:d2};function d2(){return"/"}function Qi(t){return!!(t!==null&&typeof t=="object"&&"href"in t&&t.href&&"protocol"in t&&t.protocol&&t.auth===void 0)}function Fb(t){if(typeof t=="string")t=new URL(t);else if(!Qi(t)){let e=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+t+"`");throw e.code="ERR_INVALID_ARG_TYPE",e}if(t.protocol!=="file:"){let e=new TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return g2(t)}function g2(t){if(t.hostname!==""){let l=new TypeError('File URL host must be "localhost" or empty on darwin');throw l.code="ERR_INVALID_FILE_URL_HOST",l}let e=t.pathname,n=-1;for(;++n<e.length;)if(e.codePointAt(n)===37&&e.codePointAt(n+1)===50){let l=e.codePointAt(n+2);if(l===70||l===102){let i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(e)}var fm=["history","path","basename","stem","extname","dirname"],Gl=class{constructor(e){let n;e?Qi(e)?n={path:e}:typeof e=="string"||y2(e)?n={value:e}:n=e:n={},this.cwd="cwd"in n?"":Zb.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let l=-1;for(;++l<fm.length;){let r=fm[l];r in n&&n[r]!==void 0&&n[r]!==null&&(this[r]=r==="history"?[...n[r]]:n[r])}let i;for(i in n)fm.includes(i)||(this[i]=n[i])}get basename(){return typeof this.path=="string"?Qe.basename(this.path):void 0}set basename(e){pm(e,"basename"),mm(e,"basename"),this.path=Qe.join(this.dirname||"",e)}get dirname(){return typeof this.path=="string"?Qe.dirname(this.path):void 0}set dirname(e){Kb(this.basename,"dirname"),this.path=Qe.join(e||"",this.basename)}get extname(){return typeof this.path=="string"?Qe.extname(this.path):void 0}set extname(e){if(mm(e,"extname"),Kb(this.dirname,"extname"),e){if(e.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(e.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Qe.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){Qi(e)&&(e=Fb(e)),pm(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return typeof this.path=="string"?Qe.basename(this.path,this.extname):void 0}set stem(e){pm(e,"stem"),mm(e,"stem"),this.path=Qe.join(this.dirname||"",e+(this.extname||""))}fail(e,n,l){let i=this.message(e,n,l);throw i.fatal=!0,i}info(e,n,l){let i=this.message(e,n,l);return i.fatal=void 0,i}message(e,n,l){let i=new Dt(e,n,l);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(e){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(e||void 0).decode(this.value)}};function mm(t,e){if(t&&t.includes(Qe.sep))throw new Error("`"+e+"` cannot be a path: did not expect `"+Qe.sep+"`")}function pm(t,e){if(!t)throw new Error("`"+e+"` cannot be empty")}function Kb(t,e){if(!t)throw new Error("Setting `"+e+"` requires `path` to be set too")}function y2(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var Ib=function(t){let l=this.constructor.prototype,i=l[t],r=function(){return i.apply(r,arguments)};return Object.setPrototypeOf(r,l),r};var b2={}.hasOwnProperty,ym=class t extends Ib{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=sm()}copy(){let e=new t,n=-1;for(;++n<this.attachers.length;){let l=this.attachers[n];e.use(...l)}return e.data((0,No.default)(!0,{},this.namespace)),e}data(e,n){return typeof e=="string"?arguments.length===2?(gm("data",this.frozen),this.namespace[e]=n,this):b2.call(this.namespace,e)&&this.namespace[e]||void 0:e?(gm("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;let e=this;for(;++this.freezeIndex<this.attachers.length;){let[n,...l]=this.attachers[this.freezeIndex];if(l[0]===!1)continue;l[0]===!0&&(l[0]=void 0);let i=n.call(e,...l);typeof i=="function"&&this.transformers.use(i)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let n=_o(e),l=this.parser||this.Parser;return hm("parse",l),l(String(n),n)}process(e,n){let l=this;return this.freeze(),hm("process",this.parser||this.Parser),dm("process",this.compiler||this.Compiler),n?i(void 0,n):new Promise(i);function i(r,a){let u=_o(e),o=l.parse(u);l.run(o,u,function(s,f,p){if(s||!f||!p)return c(s);let m=f,y=l.stringify(m,p);v2(y)?p.value=y:p.result=y,c(s,p)});function c(s,f){s||!f?a(s):r?r(f):n(void 0,f)}}}processSync(e){let n=!1,l;return this.freeze(),hm("processSync",this.parser||this.Parser),dm("processSync",this.compiler||this.Compiler),this.process(e,i),Pb("processSync","process",n),l;function i(r,a){n=!0,cm(r),l=a}}run(e,n,l){Jb(e),this.freeze();let i=this.transformers;return!l&&typeof n=="function"&&(l=n,n=void 0),l?r(void 0,l):new Promise(r);function r(a,u){let o=_o(n);i.run(e,o,c);function c(s,f,p){let m=f||e;s?u(s):a?a(m):l(void 0,m,p)}}}runSync(e,n){let l=!1,i;return this.run(e,n,r),Pb("runSync","run",l),i;function r(a,u){cm(a),i=u,l=!0}}stringify(e,n){this.freeze();let l=_o(n),i=this.compiler||this.Compiler;return dm("stringify",i),Jb(e),i(e,l)}use(e,...n){let l=this.attachers,i=this.namespace;if(gm("use",this.frozen),e!=null)if(typeof e=="function")o(e,n);else if(typeof e=="object")Array.isArray(e)?u(e):a(e);else throw new TypeError("Expected usable value, not `"+e+"`");return this;function r(c){if(typeof c=="function")o(c,[]);else if(typeof c=="object")if(Array.isArray(c)){let[s,...f]=c;o(s,f)}else a(c);else throw new TypeError("Expected usable value, not `"+c+"`")}function a(c){if(!("plugins"in c)&&!("settings"in c))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");u(c.plugins),c.settings&&(i.settings=(0,No.default)(!0,i.settings,c.settings))}function u(c){let s=-1;if(c!=null)if(Array.isArray(c))for(;++s<c.length;){let f=c[s];r(f)}else throw new TypeError("Expected a list of plugins, not `"+c+"`")}function o(c,s){let f=-1,p=-1;for(;++f<l.length;)if(l[f][0]===c){p=f;break}if(p===-1)l.push([c,...s]);else if(s.length>0){let[m,...y]=s,v=l[p][1];ka(v)&&ka(m)&&(m=(0,No.default)(!0,v,m)),l[p]=[c,m,...y]}}}},bm=new ym().freeze();function hm(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `parser`")}function dm(t,e){if(typeof e!="function")throw new TypeError("Cannot `"+t+"` without `compiler`")}function gm(t,e){if(e)throw new Error("Cannot call `"+t+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Jb(t){if(!ka(t)||typeof t.type!="string")throw new TypeError("Expected node, got `"+t+"`")}function Pb(t,e,n){if(!n)throw new Error("`"+t+"` finished async. Use `"+e+"` instead")}function _o(t){return x2(t)?t:new Gl(t)}function x2(t){return!!(t&&typeof t=="object"&&"message"in t&&"messages"in t)}function v2(t){return typeof t=="string"||S2(t)}function S2(t){return!!(t&&typeof t=="object"&&"byteLength"in t&&"byteOffset"in t)}var k2="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Wb=[],$b={allowDangerousHtml:!0},w2=/^(https?|ircs?|mailto|xmpp)$/i,E2=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function xm(t){let e=T2(t),n=A2(t);return z2(e.runSync(e.parse(n),n),t)}function T2(t){let e=t.rehypePlugins||Wb,n=t.remarkPlugins||Wb,l=t.remarkRehypeOptions?M(M({},t.remarkRehypeOptions),$b):$b;return bm().use(ko).use(n).use(Oo,l).use(e)}function A2(t){let e=t.children||"",n=new Gl;return typeof e=="string"?n.value=e:(""+e,void 0),n}function z2(t,e){let n=e.allowedElements,l=e.allowElement,i=e.components,r=e.disallowedElements,a=e.skipHtml,u=e.unwrapDisallowed,o=e.urlTransform||e0;for(let s of E2)Object.hasOwn(e,s.from)&&(""+s.from+(s.to?"use `"+s.to+"` instead":"remove it")+k2+s.id,void 0);return n&&r&&void 0,Yl(t,c),qf(t,{Fragment:Zi.Fragment,components:i,ignoreInvalidStyle:!0,jsx:Zi.jsx,jsxs:Zi.jsxs,passKeys:!0,passNode:!0});function c(s,f,p){if(s.type==="raw"&&p&&typeof f=="number")return a?p.children.splice(f,1):p.children[f]={type:"text",value:s.value},f;if(s.type==="element"){let m;for(m in ha)if(Object.hasOwn(ha,m)&&Object.hasOwn(s.properties,m)){let y=s.properties[m],v=ha[m];(v===null||v.includes(s.tagName))&&(s.properties[m]=o(String(y||""),m,s))}}if(s.type==="element"){let m=n?!n.includes(s.tagName):r?r.includes(s.tagName):!1;if(!m&&l&&typeof f=="number"&&(m=!l(s,f,p)),m&&p&&typeof f=="number")return u&&s.children?p.children.splice(f,1,...s.children):p.children.splice(f,1),f}}}function e0(t){let e=t.indexOf(":"),n=t.indexOf("?"),l=t.indexOf("#"),i=t.indexOf("/");return e===-1||i!==-1&&e>i||n!==-1&&e>n||l!==-1&&e>l||w2.test(t.slice(0,e))?t:""}function vm(t,e){let n=String(t);if(typeof e!="string")throw new TypeError("Expected character");let l=0,i=n.indexOf(e);for(;i!==-1;)l++,i=n.indexOf(e,i+e.length);return l}function Sm(t){if(typeof t!="string")throw new TypeError("Expected a string");return t.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}function km(t,e,n){let i=ol((n||{}).ignore||[]),r=C2(e),a=-1;for(;++a<r.length;)Sa(t,"text",u);function u(c,s){let f=-1,p;for(;++f<s.length;){let m=s[f],y=p?p.children:void 0;if(i(m,y?y.indexOf(m):void 0,p))return;p=m}if(p)return o(c,s)}function o(c,s){let f=s[s.length-1],p=r[a][0],m=r[a][1],y=0,E=f.children.indexOf(c),h=!1,d=[];p.lastIndex=0;let g=p.exec(c.value);for(;g;){let k=g.index,C={index:g.index,input:g.input,stack:[...s,c]},w=m(...g,C);if(typeof w=="string"&&(w=w.length>0?{type:"text",value:w}:void 0),w===!1?p.lastIndex=k+1:(y!==k&&d.push({type:"text",value:c.value.slice(y,k)}),Array.isArray(w)?d.push(...w):w&&d.push(w),y=k+g[0].length,h=!0),!p.global)break;g=p.exec(c.value)}return h?(y<c.value.length&&d.push({type:"text",value:c.value.slice(y)}),f.children.splice(E,1,...d)):d=[c],E+d.length}}function C2(t){let e=[];if(!Array.isArray(t))throw new TypeError("Expected find and replace tuple or list of tuples");let n=!t[0]||Array.isArray(t[0])?t:[t],l=-1;for(;++l<n.length;){let i=n[l];e.push([M2(i[0]),D2(i[1])])}return e}function M2(t){return typeof t=="string"?new RegExp(Sm(t),"g"):t}function D2(t){return typeof t=="function"?t:function(){return t}}var wm="phrasing",Em=["autolink","link","image","label"];function Am(){return{transforms:[U2],enter:{literalAutolink:O2,literalAutolinkEmail:Tm,literalAutolinkHttp:Tm,literalAutolinkWww:Tm},exit:{literalAutolink:L2,literalAutolinkEmail:N2,literalAutolinkHttp:R2,literalAutolinkWww:_2}}}function zm(){return{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:wm,notInConstruct:Em},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:wm,notInConstruct:Em},{character:":",before:"[ps]",after:"\\/",inConstruct:wm,notInConstruct:Em}]}}function O2(t){this.enter({type:"link",title:null,url:"",children:[]},t)}function Tm(t){this.config.enter.autolinkProtocol.call(this,t)}function R2(t){this.config.exit.autolinkProtocol.call(this,t)}function _2(t){this.config.exit.data.call(this,t);let e=this.stack[this.stack.length-1];e.type,e.url="http://"+this.sliceSerialize(t)}function N2(t){this.config.exit.autolinkEmail.call(this,t)}function L2(t){this.exit(t)}function U2(t){km(t,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,B2],[new RegExp("(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)","gu"),H2]],{ignore:["link","linkReference"]})}function B2(t,e,n,l,i){let r="";if(!n0(i)||(/^w/i.test(e)&&(n=e+n,e="",r="http://"),!q2(n)))return!1;let a=j2(n+l);if(!a[0])return!1;let u={type:"link",title:null,url:r+e+a[0],children:[{type:"text",value:e+a[0]}]};return a[1]?[u,{type:"text",value:a[1]}]:u}function H2(t,e,n,l){return!n0(l,!0)||/[-\d_]$/.test(n)?!1:{type:"link",title:null,url:"mailto:"+e+"@"+n,children:[{type:"text",value:e+"@"+n}]}}function q2(t){let e=t.split(".");return!(e.length<2||e[e.length-1]&&(/_/.test(e[e.length-1])||!/[a-zA-Z\d]/.test(e[e.length-1]))||e[e.length-2]&&(/_/.test(e[e.length-2])||!/[a-zA-Z\d]/.test(e[e.length-2])))}function j2(t){let e=/[!"&'),.:;<>?\]}]+$/.exec(t);if(!e)return[t,void 0];t=t.slice(0,e.index);let n=e[0],l=n.indexOf(")"),i=vm(t,"("),r=vm(t,")");for(;l!==-1&&i>r;)t+=n.slice(0,l+1),n=n.slice(l+1),l=n.indexOf(")"),r++;return[t,n]}function n0(t,e){let n=t.input.charCodeAt(t.index-1);return(t.index===0||ln(n)||Ul(n))&&(!e||n!==47)}l0.peek=I2;function Y2(){this.buffer()}function G2(t){this.enter({type:"footnoteReference",identifier:"",label:""},t)}function V2(){this.buffer()}function X2(t){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},t)}function Q2(t){let e=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=te(this.sliceSerialize(t)).toLowerCase(),n.label=e}function Z2(t){this.exit(t)}function F2(t){let e=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=te(this.sliceSerialize(t)).toLowerCase(),n.label=e}function K2(t){this.exit(t)}function I2(){return"["}function l0(t,e,n,l){let i=n.createTracker(l),r=i.move("[^"),a=n.enter("footnoteReference"),u=n.enter("reference");return r+=i.move(n.safe(n.associationId(t),{after:"]",before:r})),u(),a(),r+=i.move("]"),r}function Cm(){return{enter:{gfmFootnoteCallString:Y2,gfmFootnoteCall:G2,gfmFootnoteDefinitionLabelString:V2,gfmFootnoteDefinition:X2},exit:{gfmFootnoteCallString:Q2,gfmFootnoteCall:Z2,gfmFootnoteDefinitionLabelString:F2,gfmFootnoteDefinition:K2}}}function Mm(t){let e=!1;return t&&t.firstLineBlank&&(e=!0),{handlers:{footnoteDefinition:n,footnoteReference:l0},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]};function n(l,i,r,a){let u=r.createTracker(a),o=u.move("[^"),c=r.enter("footnoteDefinition"),s=r.enter("label");return o+=u.move(r.safe(r.associationId(l),{before:o,after:"]"})),s(),o+=u.move("]:"),l.children&&l.children.length>0&&(u.shift(4),o+=u.move((e?`
`:" ")+r.indentLines(r.containerFlow(l,u.current()),e?i0:J2))),c(),o}}function J2(t,e,n){return e===0?t:i0(t,e,n)}function i0(t,e,n){return(n?"":"    ")+t}var P2=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];r0.peek=tT;function Dm(){return{canContainEols:["delete"],enter:{strikethrough:W2},exit:{strikethrough:$2}}}function Om(){return{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:P2}],handlers:{delete:r0}}}function W2(t){this.enter({type:"delete",children:[]},t)}function $2(t){this.exit(t)}function r0(t,e,n,l){let i=n.createTracker(l),r=n.enter("strikethrough"),a=i.move("~~");return a+=n.containerPhrasing(t,wt(M({},i.current()),{before:a,after:"~"})),a+=i.move("~~"),r(),a}function tT(){return"~"}function eT(t){return t.length}function u0(t,e){let n=e||{},l=(n.align||[]).concat(),i=n.stringLength||eT,r=[],a=[],u=[],o=[],c=0,s=-1;for(;++s<t.length;){let v=[],E=[],h=-1;for(t[s].length>c&&(c=t[s].length);++h<t[s].length;){let d=nT(t[s][h]);if(n.alignDelimiters!==!1){let g=i(d);E[h]=g,(o[h]===void 0||g>o[h])&&(o[h]=g)}v.push(d)}a[s]=v,u[s]=E}let f=-1;if(typeof l=="object"&&"length"in l)for(;++f<c;)r[f]=a0(l[f]);else{let v=a0(l);for(;++f<c;)r[f]=v}f=-1;let p=[],m=[];for(;++f<c;){let v=r[f],E="",h="";v===99?(E=":",h=":"):v===108?E=":":v===114&&(h=":");let d=n.alignDelimiters===!1?1:Math.max(1,o[f]-E.length-h.length),g=E+"-".repeat(d)+h;n.alignDelimiters!==!1&&(d=E.length+d+h.length,d>o[f]&&(o[f]=d),m[f]=d),p[f]=g}a.splice(1,0,p),u.splice(1,0,m),s=-1;let y=[];for(;++s<a.length;){let v=a[s],E=u[s];f=-1;let h=[];for(;++f<c;){let d=v[f]||"",g="",k="";if(n.alignDelimiters!==!1){let C=o[f]-(E[f]||0),w=r[f];w===114?g=" ".repeat(C):w===99?C%2?(g=" ".repeat(C/2+.5),k=" ".repeat(C/2-.5)):(g=" ".repeat(C/2),k=g):k=" ".repeat(C)}n.delimiterStart!==!1&&!f&&h.push("|"),n.padding!==!1&&!(n.alignDelimiters===!1&&d==="")&&(n.delimiterStart!==!1||f)&&h.push(" "),n.alignDelimiters!==!1&&h.push(g),h.push(d),n.alignDelimiters!==!1&&h.push(k),n.padding!==!1&&h.push(" "),(n.delimiterEnd!==!1||f!==c-1)&&h.push("|")}y.push(n.delimiterEnd===!1?h.join("").replace(/ +$/,""):h.join(""))}return y.join(`
`)}function nT(t){return t==null?"":String(t)}function a0(t){let e=typeof t=="string"?t.codePointAt(0):0;return e===67||e===99?99:e===76||e===108?108:e===82||e===114?114:0}function o0(t,e,n,l){let i=n.enter("blockquote"),r=n.createTracker(l);r.move("> "),r.shift(2);let a=n.indentLines(n.containerFlow(t,r.current()),lT);return i(),a}function lT(t,e,n){return">"+(n?"":" ")+t}function s0(t,e){return c0(t,e.inConstruct,!0)&&!c0(t,e.notInConstruct,!1)}function c0(t,e,n){if(typeof e=="string"&&(e=[e]),!e||e.length===0)return n;let l=-1;for(;++l<e.length;)if(t.includes(e[l]))return!0;return!1}function Rm(t,e,n,l){let i=-1;for(;++i<n.unsafe.length;)if(n.unsafe[i].character===`
`&&s0(n.stack,n.unsafe[i]))return/[ \t]/.test(l.before)?"":" ";return`\\
`}function f0(t,e){let n=String(t),l=n.indexOf(e),i=l,r=0,a=0;if(typeof e!="string")throw new TypeError("Expected substring");for(;l!==-1;)l===i?++r>a&&(a=r):r=1,i=l+e.length,l=n.indexOf(e,i);return a}function m0(t,e){return!!(e.options.fences===!1&&t.value&&!t.lang&&/[^ \r\n]/.test(t.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(t.value))}function p0(t){let e=t.options.fence||"`";if(e!=="`"&&e!=="~")throw new Error("Cannot serialize code with `"+e+"` for `options.fence`, expected `` ` `` or `~`");return e}function h0(t,e,n,l){let i=p0(n),r=t.value||"",a=i==="`"?"GraveAccent":"Tilde";if(m0(t,n)){let f=n.enter("codeIndented"),p=n.indentLines(r,iT);return f(),p}let u=n.createTracker(l),o=i.repeat(Math.max(f0(r,i)+1,3)),c=n.enter("codeFenced"),s=u.move(o);if(t.lang){let f=n.enter(`codeFencedLang${a}`);s+=u.move(n.safe(t.lang,M({before:s,after:" ",encode:["`"]},u.current()))),f()}if(t.lang&&t.meta){let f=n.enter(`codeFencedMeta${a}`);s+=u.move(" "),s+=u.move(n.safe(t.meta,M({before:s,after:`
`,encode:["`"]},u.current()))),f()}return s+=u.move(`
`),r&&(s+=u.move(r+`
`)),s+=u.move(o),c(),s}function iT(t,e,n){return(n?"":"    ")+t}function Fi(t){let e=t.options.quote||'"';if(e!=='"'&&e!=="'")throw new Error("Cannot serialize title with `"+e+"` for `options.quote`, expected `\"`, or `'`");return e}function d0(t,e,n,l){let i=Fi(n),r=i==='"'?"Quote":"Apostrophe",a=n.enter("definition"),u=n.enter("label"),o=n.createTracker(l),c=o.move("[");return c+=o.move(n.safe(n.associationId(t),M({before:c,after:"]"},o.current()))),c+=o.move("]: "),u(),!t.url||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),c+=o.move("<"),c+=o.move(n.safe(t.url,M({before:c,after:">"},o.current()))),c+=o.move(">")):(u=n.enter("destinationRaw"),c+=o.move(n.safe(t.url,M({before:c,after:t.title?" ":`
`},o.current())))),u(),t.title&&(u=n.enter(`title${r}`),c+=o.move(" "+i),c+=o.move(n.safe(t.title,M({before:c,after:i},o.current()))),c+=o.move(i),u()),a(),c}function g0(t){let e=t.options.emphasis||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize emphasis with `"+e+"` for `options.emphasis`, expected `*`, or `_`");return e}function cl(t){return"&#x"+t.toString(16).toUpperCase()+";"}function Ki(t,e,n){let l=zn(t),i=zn(e);return l===void 0?i===void 0?n==="_"?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!0}:l===1?i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!0}:{inside:!1,outside:!1}:i===void 0?{inside:!1,outside:!1}:i===1?{inside:!0,outside:!1}:{inside:!1,outside:!1}}_m.peek=rT;function _m(t,e,n,l){let i=g0(n),r=n.enter("emphasis"),a=n.createTracker(l),u=a.move(i),o=a.move(n.containerPhrasing(t,M({after:i,before:u},a.current()))),c=o.charCodeAt(0),s=Ki(l.before.charCodeAt(l.before.length-1),c,i);s.inside&&(o=cl(c)+o.slice(1));let f=o.charCodeAt(o.length-1),p=Ki(l.after.charCodeAt(0),f,i);p.inside&&(o=o.slice(0,-1)+cl(f));let m=a.move(i);return r(),n.attentionEncodeSurroundingInfo={after:p.outside,before:s.outside},u+o+m}function rT(t,e,n){return n.options.emphasis||"*"}function y0(t,e){let n=!1;return Yl(t,function(l){if("value"in l&&/\r?\n|\r/.test(l.value)||l.type==="break")return n=!0,jl}),!!((!t.depth||t.depth<3)&&Nl(t)&&(e.options.setext||n))}function b0(t,e,n,l){let i=Math.max(Math.min(6,t.depth||1),1),r=n.createTracker(l);if(y0(t,n)){let s=n.enter("headingSetext"),f=n.enter("phrasing"),p=n.containerPhrasing(t,wt(M({},r.current()),{before:`
`,after:`
`}));return f(),s(),p+`
`+(i===1?"=":"-").repeat(p.length-(Math.max(p.lastIndexOf("\r"),p.lastIndexOf(`
`))+1))}let a="#".repeat(i),u=n.enter("headingAtx"),o=n.enter("phrasing");r.move(a+" ");let c=n.containerPhrasing(t,M({before:"# ",after:`
`},r.current()));return/^[\t ]/.test(c)&&(c=cl(c.charCodeAt(0))+c.slice(1)),c=c?a+" "+c:a,n.options.closeAtx&&(c+=" "+a),o(),u(),c}Nm.peek=aT;function Nm(t){return t.value||""}function aT(){return"<"}Lm.peek=uT;function Lm(t,e,n,l){let i=Fi(n),r=i==='"'?"Quote":"Apostrophe",a=n.enter("image"),u=n.enter("label"),o=n.createTracker(l),c=o.move("![");return c+=o.move(n.safe(t.alt,M({before:c,after:"]"},o.current()))),c+=o.move("]("),u(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(u=n.enter("destinationLiteral"),c+=o.move("<"),c+=o.move(n.safe(t.url,M({before:c,after:">"},o.current()))),c+=o.move(">")):(u=n.enter("destinationRaw"),c+=o.move(n.safe(t.url,M({before:c,after:t.title?" ":")"},o.current())))),u(),t.title&&(u=n.enter(`title${r}`),c+=o.move(" "+i),c+=o.move(n.safe(t.title,M({before:c,after:i},o.current()))),c+=o.move(i),u()),c+=o.move(")"),a(),c}function uT(){return"!"}Um.peek=oT;function Um(t,e,n,l){let i=t.referenceType,r=n.enter("imageReference"),a=n.enter("label"),u=n.createTracker(l),o=u.move("!["),c=n.safe(t.alt,M({before:o,after:"]"},u.current()));o+=u.move(c+"]["),a();let s=n.stack;n.stack=[],a=n.enter("reference");let f=n.safe(n.associationId(t),M({before:o,after:"]"},u.current()));return a(),n.stack=s,r(),i==="full"||!c||c!==f?o+=u.move(f+"]"):i==="shortcut"?o=o.slice(0,-1):o+=u.move("]"),o}function oT(){return"!"}Bm.peek=cT;function Bm(t,e,n){let l=t.value||"",i="`",r=-1;for(;new RegExp("(^|[^`])"+i+"([^`]|$)").test(l);)i+="`";for(/[^ \r\n]/.test(l)&&(/^[ \r\n]/.test(l)&&/[ \r\n]$/.test(l)||/^`|`$/.test(l))&&(l=" "+l+" ");++r<n.unsafe.length;){let a=n.unsafe[r],u=n.compilePattern(a),o;if(a.atBreak)for(;o=u.exec(l);){let c=o.index;l.charCodeAt(c)===10&&l.charCodeAt(c-1)===13&&c--,l=l.slice(0,c)+" "+l.slice(o.index+1)}}return i+l+i}function cT(){return"`"}function Hm(t,e){let n=Nl(t);return!!(!e.options.resourceLink&&t.url&&!t.title&&t.children&&t.children.length===1&&t.children[0].type==="text"&&(n===t.url||"mailto:"+n===t.url)&&/^[a-z][a-z+.-]+:/i.test(t.url)&&!/[\0- <>\u007F]/.test(t.url))}qm.peek=sT;function qm(t,e,n,l){let i=Fi(n),r=i==='"'?"Quote":"Apostrophe",a=n.createTracker(l),u,o;if(Hm(t,n)){let s=n.stack;n.stack=[],u=n.enter("autolink");let f=a.move("<");return f+=a.move(n.containerPhrasing(t,M({before:f,after:">"},a.current()))),f+=a.move(">"),u(),n.stack=s,f}u=n.enter("link"),o=n.enter("label");let c=a.move("[");return c+=a.move(n.containerPhrasing(t,M({before:c,after:"]("},a.current()))),c+=a.move("]("),o(),!t.url&&t.title||/[\0- \u007F]/.test(t.url)?(o=n.enter("destinationLiteral"),c+=a.move("<"),c+=a.move(n.safe(t.url,M({before:c,after:">"},a.current()))),c+=a.move(">")):(o=n.enter("destinationRaw"),c+=a.move(n.safe(t.url,M({before:c,after:t.title?" ":")"},a.current())))),o(),t.title&&(o=n.enter(`title${r}`),c+=a.move(" "+i),c+=a.move(n.safe(t.title,M({before:c,after:i},a.current()))),c+=a.move(i),o()),c+=a.move(")"),u(),c}function sT(t,e,n){return Hm(t,n)?"<":"["}jm.peek=fT;function jm(t,e,n,l){let i=t.referenceType,r=n.enter("linkReference"),a=n.enter("label"),u=n.createTracker(l),o=u.move("["),c=n.containerPhrasing(t,M({before:o,after:"]"},u.current()));o+=u.move(c+"]["),a();let s=n.stack;n.stack=[],a=n.enter("reference");let f=n.safe(n.associationId(t),M({before:o,after:"]"},u.current()));return a(),n.stack=s,r(),i==="full"||!c||c!==f?o+=u.move(f+"]"):i==="shortcut"?o=o.slice(0,-1):o+=u.move("]"),o}function fT(){return"["}function Ii(t){let e=t.options.bullet||"*";if(e!=="*"&&e!=="+"&&e!=="-")throw new Error("Cannot serialize items with `"+e+"` for `options.bullet`, expected `*`, `+`, or `-`");return e}function x0(t){let e=Ii(t),n=t.options.bulletOther;if(!n)return e==="*"?"-":"*";if(n!=="*"&&n!=="+"&&n!=="-")throw new Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===e)throw new Error("Expected `bullet` (`"+e+"`) and `bulletOther` (`"+n+"`) to be different");return n}function v0(t){let e=t.options.bulletOrdered||".";if(e!=="."&&e!==")")throw new Error("Cannot serialize items with `"+e+"` for `options.bulletOrdered`, expected `.` or `)`");return e}function Lo(t){let e=t.options.rule||"*";if(e!=="*"&&e!=="-"&&e!=="_")throw new Error("Cannot serialize rules with `"+e+"` for `options.rule`, expected `*`, `-`, or `_`");return e}function S0(t,e,n,l){let i=n.enter("list"),r=n.bulletCurrent,a=t.ordered?v0(n):Ii(n),u=t.ordered?a==="."?")":".":x0(n),o=e&&n.bulletLastUsed?a===n.bulletLastUsed:!1;if(!t.ordered){let s=t.children?t.children[0]:void 0;if((a==="*"||a==="-")&&s&&(!s.children||!s.children[0])&&n.stack[n.stack.length-1]==="list"&&n.stack[n.stack.length-2]==="listItem"&&n.stack[n.stack.length-3]==="list"&&n.stack[n.stack.length-4]==="listItem"&&n.indexStack[n.indexStack.length-1]===0&&n.indexStack[n.indexStack.length-2]===0&&n.indexStack[n.indexStack.length-3]===0&&(o=!0),Lo(n)===a&&s){let f=-1;for(;++f<t.children.length;){let p=t.children[f];if(p&&p.type==="listItem"&&p.children&&p.children[0]&&p.children[0].type==="thematicBreak"){o=!0;break}}}}o&&(a=u),n.bulletCurrent=a;let c=n.containerFlow(t,l);return n.bulletLastUsed=a,n.bulletCurrent=r,i(),c}function k0(t){let e=t.options.listItemIndent||"one";if(e!=="tab"&&e!=="one"&&e!=="mixed")throw new Error("Cannot serialize items with `"+e+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return e}function w0(t,e,n,l){let i=k0(n),r=n.bulletCurrent||Ii(n);e&&e.type==="list"&&e.ordered&&(r=(typeof e.start=="number"&&e.start>-1?e.start:1)+(n.options.incrementListMarker===!1?0:e.children.indexOf(t))+r);let a=r.length+1;(i==="tab"||i==="mixed"&&(e&&e.type==="list"&&e.spread||t.spread))&&(a=Math.ceil(a/4)*4);let u=n.createTracker(l);u.move(r+" ".repeat(a-r.length)),u.shift(a);let o=n.enter("listItem"),c=n.indentLines(n.containerFlow(t,u.current()),s);return o(),c;function s(f,p,m){return p?(m?"":" ".repeat(a))+f:(m?r:r+" ".repeat(a-r.length))+f}}function E0(t,e,n,l){let i=n.enter("paragraph"),r=n.enter("phrasing"),a=n.containerPhrasing(t,l);return r(),i(),a}var Ym=ol(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function T0(t,e,n,l){return(t.children.some(function(a){return Ym(a)})?n.containerPhrasing:n.containerFlow).call(n,t,l)}function A0(t){let e=t.options.strong||"*";if(e!=="*"&&e!=="_")throw new Error("Cannot serialize strong with `"+e+"` for `options.strong`, expected `*`, or `_`");return e}Gm.peek=mT;function Gm(t,e,n,l){let i=A0(n),r=n.enter("strong"),a=n.createTracker(l),u=a.move(i+i),o=a.move(n.containerPhrasing(t,M({after:i,before:u},a.current()))),c=o.charCodeAt(0),s=Ki(l.before.charCodeAt(l.before.length-1),c,i);s.inside&&(o=cl(c)+o.slice(1));let f=o.charCodeAt(o.length-1),p=Ki(l.after.charCodeAt(0),f,i);p.inside&&(o=o.slice(0,-1)+cl(f));let m=a.move(i+i);return r(),n.attentionEncodeSurroundingInfo={after:p.outside,before:s.outside},u+o+m}function mT(t,e,n){return n.options.strong||"*"}function z0(t,e,n,l){return n.safe(t.value,l)}function C0(t){let e=t.options.ruleRepetition||3;if(e<3)throw new Error("Cannot serialize rules with repetition `"+e+"` for `options.ruleRepetition`, expected `3` or more");return e}function M0(t,e,n){let l=(Lo(n)+(n.options.ruleSpaces?" ":"")).repeat(C0(n));return n.options.ruleSpaces?l.slice(0,-1):l}var Ea={blockquote:o0,break:Rm,code:h0,definition:d0,emphasis:_m,hardBreak:Rm,heading:b0,html:Nm,image:Lm,imageReference:Um,inlineCode:Bm,link:qm,linkReference:jm,list:S0,listItem:w0,paragraph:E0,root:T0,strong:Gm,text:z0,thematicBreak:M0};function Xm(){return{enter:{table:pT,tableData:D0,tableHeader:D0,tableRow:dT},exit:{codeText:gT,table:hT,tableData:Vm,tableHeader:Vm,tableRow:Vm}}}function pT(t){let e=t._align;this.enter({type:"table",align:e.map(function(n){return n==="none"?null:n}),children:[]},t),this.data.inTable=!0}function hT(t){this.exit(t),this.data.inTable=void 0}function dT(t){this.enter({type:"tableRow",children:[]},t)}function Vm(t){this.exit(t)}function D0(t){this.enter({type:"tableCell",children:[]},t)}function gT(t){let e=this.resume();this.data.inTable&&(e=e.replace(/\\([\\|])/g,yT));let n=this.stack[this.stack.length-1];n.type,n.value=e,this.exit(t)}function yT(t,e){return e==="|"?e:t}function Qm(t){let e=t||{},n=e.tableCellPadding,l=e.tablePipeAlign,i=e.stringLength,r=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:`
`,inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:p,table:a,tableCell:o,tableRow:u}};function a(m,y,v,E){return c(s(m,v,E),m.align)}function u(m,y,v,E){let h=f(m,v,E),d=c([h]);return d.slice(0,d.indexOf(`
`))}function o(m,y,v,E){let h=v.enter("tableCell"),d=v.enter("phrasing"),g=v.containerPhrasing(m,wt(M({},E),{before:r,after:r}));return d(),h(),g}function c(m,y){return u0(m,{align:y,alignDelimiters:l,padding:n,stringLength:i})}function s(m,y,v){let E=m.children,h=-1,d=[],g=y.enter("table");for(;++h<E.length;)d[h]=f(E[h],y,v);return g(),d}function f(m,y,v){let E=m.children,h=-1,d=[],g=y.enter("tableRow");for(;++h<E.length;)d[h]=o(E[h],m,y,v);return g(),d}function p(m,y,v){let E=Ea.inlineCode(m,y,v);return v.stack.includes("tableCell")&&(E=E.replace(/\|/g,"\\$&")),E}}function Zm(){return{exit:{taskListCheckValueChecked:O0,taskListCheckValueUnchecked:O0,paragraph:bT}}}function Fm(){return{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:xT}}}function O0(t){let e=this.stack[this.stack.length-2];e.type,e.checked=t.type==="taskListCheckValueChecked"}function bT(t){let e=this.stack[this.stack.length-2];if(e&&e.type==="listItem"&&typeof e.checked=="boolean"){let n=this.stack[this.stack.length-1];n.type;let l=n.children[0];if(l&&l.type==="text"){let i=e.children,r=-1,a;for(;++r<i.length;){let u=i[r];if(u.type==="paragraph"){a=u;break}}a===n&&(l.value=l.value.slice(1),l.value.length===0?n.children.shift():n.position&&l.position&&typeof l.position.start.offset=="number"&&(l.position.start.column++,l.position.start.offset++,n.position.start=Object.assign({},l.position.start)))}}this.exit(t)}function xT(t,e,n,l){let i=t.children[0],r=typeof t.checked=="boolean"&&i&&i.type==="paragraph",a="["+(t.checked?"x":" ")+"] ",u=n.createTracker(l);r&&u.move(a);let o=Ea.listItem(t,e,n,M(M({},l),u.current()));return r&&(o=o.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,c)),o;function c(s){return s+a}}function Km(){return[Am(),Cm(),Dm(),Xm(),Zm()]}function Im(t){return{extensions:[zm(),Mm(t),Om(),Qm(t),Fm()]}}var vT={tokenize:TT,partial:!0},R0={tokenize:AT,partial:!0},_0={tokenize:zT,partial:!0},N0={tokenize:CT,partial:!0},ST={tokenize:MT,partial:!0},L0={name:"wwwAutolink",tokenize:wT,previous:B0},U0={name:"protocolAutolink",tokenize:ET,previous:H0},Cn={name:"emailAutolink",tokenize:kT,previous:q0},an={};function Pm(){return{text:an}}var Vl=48;for(;Vl<123;)an[Vl]=Cn,Vl++,Vl===58?Vl=65:Vl===91&&(Vl=97);an[43]=Cn;an[45]=Cn;an[46]=Cn;an[95]=Cn;an[72]=[Cn,U0];an[104]=[Cn,U0];an[87]=[Cn,L0];an[119]=[Cn,L0];function kT(t,e,n){let l=this,i,r;return a;function a(f){return!Jm(f)||!q0.call(l,l.previous)||Wm(l.events)?n(f):(t.enter("literalAutolink"),t.enter("literalAutolinkEmail"),u(f))}function u(f){return Jm(f)?(t.consume(f),u):f===64?(t.consume(f),o):n(f)}function o(f){return f===46?t.check(ST,s,c)(f):f===45||f===95||zt(f)?(r=!0,t.consume(f),o):s(f)}function c(f){return t.consume(f),i=!0,o}function s(f){return r&&i&&Ht(l.previous)?(t.exit("literalAutolinkEmail"),t.exit("literalAutolink"),e(f)):n(f)}}function wT(t,e,n){let l=this;return i;function i(a){return a!==87&&a!==119||!B0.call(l,l.previous)||Wm(l.events)?n(a):(t.enter("literalAutolink"),t.enter("literalAutolinkWww"),t.check(vT,t.attempt(R0,t.attempt(_0,r),n),n)(a))}function r(a){return t.exit("literalAutolinkWww"),t.exit("literalAutolink"),e(a)}}function ET(t,e,n){let l=this,i="",r=!1;return a;function a(f){return(f===72||f===104)&&H0.call(l,l.previous)&&!Wm(l.events)?(t.enter("literalAutolink"),t.enter("literalAutolinkHttp"),i+=String.fromCodePoint(f),t.consume(f),u):n(f)}function u(f){if(Ht(f)&&i.length<5)return i+=String.fromCodePoint(f),t.consume(f),u;if(f===58){let p=i.toLowerCase();if(p==="http"||p==="https")return t.consume(f),o}return n(f)}function o(f){return f===47?(t.consume(f),r?c:(r=!0,o)):n(f)}function c(f){return f===null||Ll(f)||W(f)||ln(f)||Ul(f)?n(f):t.attempt(R0,t.attempt(_0,s),n)(f)}function s(f){return t.exit("literalAutolinkHttp"),t.exit("literalAutolink"),e(f)}}function TT(t,e,n){let l=0;return i;function i(a){return(a===87||a===119)&&l<3?(l++,t.consume(a),i):a===46&&l===3?(t.consume(a),r):n(a)}function r(a){return a===null?n(a):e(a)}}function AT(t,e,n){let l,i,r;return a;function a(c){return c===46||c===95?t.check(N0,o,u)(c):c===null||W(c)||ln(c)||c!==45&&Ul(c)?o(c):(r=!0,t.consume(c),a)}function u(c){return c===95?l=!0:(i=l,l=void 0),t.consume(c),a}function o(c){return i||l||!r?n(c):e(c)}}function zT(t,e){let n=0,l=0;return i;function i(a){return a===40?(n++,t.consume(a),i):a===41&&l<n?r(a):a===33||a===34||a===38||a===39||a===41||a===42||a===44||a===46||a===58||a===59||a===60||a===63||a===93||a===95||a===126?t.check(N0,e,r)(a):a===null||W(a)||ln(a)?e(a):(t.consume(a),i)}function r(a){return a===41&&l++,t.consume(a),i}}function CT(t,e,n){return l;function l(u){return u===33||u===34||u===39||u===41||u===42||u===44||u===46||u===58||u===59||u===63||u===95||u===126?(t.consume(u),l):u===38?(t.consume(u),r):u===93?(t.consume(u),i):u===60||u===null||W(u)||ln(u)?e(u):n(u)}function i(u){return u===null||u===40||u===91||W(u)||ln(u)?e(u):l(u)}function r(u){return Ht(u)?a(u):n(u)}function a(u){return u===59?(t.consume(u),l):Ht(u)?(t.consume(u),a):n(u)}}function MT(t,e,n){return l;function l(r){return t.consume(r),i}function i(r){return zt(r)?n(r):e(r)}}function B0(t){return t===null||t===40||t===42||t===95||t===91||t===93||t===126||W(t)}function H0(t){return!Ht(t)}function q0(t){return!(t===47||Jm(t))}function Jm(t){return t===43||t===45||t===46||t===95||zt(t)}function Wm(t){let e=t.length,n=!1;for(;e--;){let l=t[e][1];if((l.type==="labelLink"||l.type==="labelImage")&&!l._balanced){n=!0;break}if(l._gfmAutolinkLiteralWalkedInto){n=!1;break}}return t.length>0&&!n&&(t[t.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}var DT={tokenize:BT,partial:!0};function $m(){return{document:{91:{name:"gfmFootnoteDefinition",tokenize:NT,continuation:{tokenize:LT},exit:UT}},text:{91:{name:"gfmFootnoteCall",tokenize:_T},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:OT,resolveTo:RT}}}}function OT(t,e,n){let l=this,i=l.events.length,r=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),a;for(;i--;){let o=l.events[i][1];if(o.type==="labelImage"){a=o;break}if(o.type==="gfmFootnoteCall"||o.type==="labelLink"||o.type==="label"||o.type==="image"||o.type==="link")break}return u;function u(o){if(!a||!a._balanced)return n(o);let c=te(l.sliceSerialize({start:a.end,end:l.now()}));return c.codePointAt(0)!==94||!r.includes(c.slice(1))?n(o):(t.enter("gfmFootnoteCallLabelMarker"),t.consume(o),t.exit("gfmFootnoteCallLabelMarker"),e(o))}}function RT(t,e){let n=t.length,l;for(;n--;)if(t[n][1].type==="labelImage"&&t[n][0]==="enter"){l=t[n][1];break}t[n+1][1].type="data",t[n+3][1].type="gfmFootnoteCallLabelMarker";let i={type:"gfmFootnoteCall",start:Object.assign({},t[n+3][1].start),end:Object.assign({},t[t.length-1][1].end)},r={type:"gfmFootnoteCallMarker",start:Object.assign({},t[n+3][1].end),end:Object.assign({},t[n+3][1].end)};r.end.column++,r.end.offset++,r.end._bufferIndex++;let a={type:"gfmFootnoteCallString",start:Object.assign({},r.end),end:Object.assign({},t[t.length-1][1].start)},u={type:"chunkString",contentType:"string",start:Object.assign({},a.start),end:Object.assign({},a.end)},o=[t[n+1],t[n+2],["enter",i,e],t[n+3],t[n+4],["enter",r,e],["exit",r,e],["enter",a,e],["enter",u,e],["exit",u,e],["exit",a,e],t[t.length-2],t[t.length-1],["exit",i,e]];return t.splice(n,t.length-n+1,...o),t}function _T(t,e,n){let l=this,i=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),r=0,a;return u;function u(f){return t.enter("gfmFootnoteCall"),t.enter("gfmFootnoteCallLabelMarker"),t.consume(f),t.exit("gfmFootnoteCallLabelMarker"),o}function o(f){return f!==94?n(f):(t.enter("gfmFootnoteCallMarker"),t.consume(f),t.exit("gfmFootnoteCallMarker"),t.enter("gfmFootnoteCallString"),t.enter("chunkString").contentType="string",c)}function c(f){if(r>999||f===93&&!a||f===null||f===91||W(f))return n(f);if(f===93){t.exit("chunkString");let p=t.exit("gfmFootnoteCallString");return i.includes(te(l.sliceSerialize(p)))?(t.enter("gfmFootnoteCallLabelMarker"),t.consume(f),t.exit("gfmFootnoteCallLabelMarker"),t.exit("gfmFootnoteCall"),e):n(f)}return W(f)||(a=!0),r++,t.consume(f),f===92?s:c}function s(f){return f===91||f===92||f===93?(t.consume(f),r++,c):c(f)}}function NT(t,e,n){let l=this,i=l.parser.gfmFootnotes||(l.parser.gfmFootnotes=[]),r,a=0,u;return o;function o(y){return t.enter("gfmFootnoteDefinition")._container=!0,t.enter("gfmFootnoteDefinitionLabel"),t.enter("gfmFootnoteDefinitionLabelMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionLabelMarker"),c}function c(y){return y===94?(t.enter("gfmFootnoteDefinitionMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionMarker"),t.enter("gfmFootnoteDefinitionLabelString"),t.enter("chunkString").contentType="string",s):n(y)}function s(y){if(a>999||y===93&&!u||y===null||y===91||W(y))return n(y);if(y===93){t.exit("chunkString");let v=t.exit("gfmFootnoteDefinitionLabelString");return r=te(l.sliceSerialize(v)),t.enter("gfmFootnoteDefinitionLabelMarker"),t.consume(y),t.exit("gfmFootnoteDefinitionLabelMarker"),t.exit("gfmFootnoteDefinitionLabel"),p}return W(y)||(u=!0),a++,t.consume(y),y===92?f:s}function f(y){return y===91||y===92||y===93?(t.consume(y),a++,s):s(y)}function p(y){return y===58?(t.enter("definitionMarker"),t.consume(y),t.exit("definitionMarker"),i.includes(r)||i.push(r),q(t,m,"gfmFootnoteDefinitionWhitespace")):n(y)}function m(y){return e(y)}}function LT(t,e,n){return t.check(rn,e,t.attempt(DT,e,n))}function UT(t){t.exit("gfmFootnoteDefinition")}function BT(t,e,n){let l=this;return q(t,i,"gfmFootnoteDefinitionIndent",5);function i(r){let a=l.events[l.events.length-1];return a&&a[1].type==="gfmFootnoteDefinitionIndent"&&a[2].sliceSerialize(a[1],!0).length===4?e(r):n(r)}}function tp(t){let n=(t||{}).singleTilde,l={name:"strikethrough",tokenize:r,resolveAll:i};return n==null&&(n=!0),{text:{126:l},insideSpan:{null:[l]},attentionMarkers:{null:[126]}};function i(a,u){let o=-1;for(;++o<a.length;)if(a[o][0]==="enter"&&a[o][1].type==="strikethroughSequenceTemporary"&&a[o][1]._close){let c=o;for(;c--;)if(a[c][0]==="exit"&&a[c][1].type==="strikethroughSequenceTemporary"&&a[c][1]._open&&a[o][1].end.offset-a[o][1].start.offset===a[c][1].end.offset-a[c][1].start.offset){a[o][1].type="strikethroughSequence",a[c][1].type="strikethroughSequence";let s={type:"strikethrough",start:Object.assign({},a[c][1].start),end:Object.assign({},a[o][1].end)},f={type:"strikethroughText",start:Object.assign({},a[c][1].end),end:Object.assign({},a[o][1].start)},p=[["enter",s,u],["enter",a[c][1],u],["exit",a[c][1],u],["enter",f,u]],m=u.parser.constructs.insideSpan.null;m&&Ot(p,p.length,0,al(m,a.slice(c+1,o),u)),Ot(p,p.length,0,[["exit",f,u],["enter",a[o][1],u],["exit",a[o][1],u],["exit",s,u]]),Ot(a,c-1,o-c+3,p),o=c+p.length-2;break}}for(o=-1;++o<a.length;)a[o][1].type==="strikethroughSequenceTemporary"&&(a[o][1].type="data");return a}function r(a,u,o){let c=this.previous,s=this.events,f=0;return p;function p(y){return c===126&&s[s.length-1][1].type!=="characterEscape"?o(y):(a.enter("strikethroughSequenceTemporary"),m(y))}function m(y){let v=zn(c);if(y===126)return f>1?o(y):(a.consume(y),f++,m);if(f<2&&!n)return o(y);let E=a.exit("strikethroughSequenceTemporary"),h=zn(y);return E._open=!h||h===2&&!!v,E._close=!v||v===2&&!!h,u(y)}}}var Uo=class{constructor(){this.map=[]}add(e,n,l){HT(this,e,n,l)}consume(e){if(this.map.sort(function(r,a){return r[0]-a[0]}),this.map.length===0)return;let n=this.map.length,l=[];for(;n>0;)n-=1,l.push(e.slice(this.map[n][0]+this.map[n][1]),this.map[n][2]),e.length=this.map[n][0];l.push(e.slice()),e.length=0;let i=l.pop();for(;i;){for(let r of i)e.push(r);i=l.pop()}this.map.length=0}};function HT(t,e,n,l){let i=0;if(!(n===0&&l.length===0)){for(;i<t.map.length;){if(t.map[i][0]===e){t.map[i][1]+=n,t.map[i][2].push(...l);return}i+=1}t.map.push([e,n,l])}}function j0(t,e){let n=!1,l=[];for(;e<t.length;){let i=t[e];if(n){if(i[0]==="enter")i[1].type==="tableContent"&&l.push(t[e+1][1].type==="tableDelimiterMarker"?"left":"none");else if(i[1].type==="tableContent"){if(t[e-1][1].type==="tableDelimiterMarker"){let r=l.length-1;l[r]=l[r]==="left"?"center":"right"}}else if(i[1].type==="tableDelimiterRow")break}else i[0]==="enter"&&i[1].type==="tableDelimiterRow"&&(n=!0);e+=1}return l}function ep(){return{flow:{null:{name:"table",tokenize:qT,resolveAll:jT}}}}function qT(t,e,n){let l=this,i=0,r=0,a;return u;function u(S){let F=l.events.length-1;for(;F>-1;){let G=l.events[F][1].type;if(G==="lineEnding"||G==="linePrefix")F--;else break}let Q=F>-1?l.events[F][1].type:null,L=Q==="tableHead"||Q==="tableRow"?w:o;return L===w&&l.parser.lazy[l.now().line]?n(S):L(S)}function o(S){return t.enter("tableHead"),t.enter("tableRow"),c(S)}function c(S){return S===124||(a=!0,r+=1),s(S)}function s(S){return S===null?n(S):_(S)?r>1?(r=0,l.interrupt=!0,t.exit("tableRow"),t.enter("lineEnding"),t.consume(S),t.exit("lineEnding"),m):n(S):Y(S)?q(t,s,"whitespace")(S):(r+=1,a&&(a=!1,i+=1),S===124?(t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),a=!0,s):(t.enter("data"),f(S)))}function f(S){return S===null||S===124||W(S)?(t.exit("data"),s(S)):(t.consume(S),S===92?p:f)}function p(S){return S===92||S===124?(t.consume(S),f):f(S)}function m(S){return l.interrupt=!1,l.parser.lazy[l.now().line]?n(S):(t.enter("tableDelimiterRow"),a=!1,Y(S)?q(t,y,"linePrefix",l.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(S):y(S))}function y(S){return S===45||S===58?E(S):S===124?(a=!0,t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),v):C(S)}function v(S){return Y(S)?q(t,E,"whitespace")(S):E(S)}function E(S){return S===58?(r+=1,a=!0,t.enter("tableDelimiterMarker"),t.consume(S),t.exit("tableDelimiterMarker"),h):S===45?(r+=1,h(S)):S===null||_(S)?k(S):C(S)}function h(S){return S===45?(t.enter("tableDelimiterFiller"),d(S)):C(S)}function d(S){return S===45?(t.consume(S),d):S===58?(a=!0,t.exit("tableDelimiterFiller"),t.enter("tableDelimiterMarker"),t.consume(S),t.exit("tableDelimiterMarker"),g):(t.exit("tableDelimiterFiller"),g(S))}function g(S){return Y(S)?q(t,k,"whitespace")(S):k(S)}function k(S){return S===124?y(S):S===null||_(S)?!a||i!==r?C(S):(t.exit("tableDelimiterRow"),t.exit("tableHead"),e(S)):C(S)}function C(S){return n(S)}function w(S){return t.enter("tableRow"),A(S)}function A(S){return S===124?(t.enter("tableCellDivider"),t.consume(S),t.exit("tableCellDivider"),A):S===null||_(S)?(t.exit("tableRow"),e(S)):Y(S)?q(t,A,"whitespace")(S):(t.enter("data"),T(S))}function T(S){return S===null||S===124||W(S)?(t.exit("data"),A(S)):(t.consume(S),S===92?N:T)}function N(S){return S===92||S===124?(t.consume(S),T):T(S)}}function jT(t,e){let n=-1,l=!0,i=0,r=[0,0,0,0],a=[0,0,0,0],u=!1,o=0,c,s,f,p=new Uo;for(;++n<t.length;){let m=t[n],y=m[1];m[0]==="enter"?y.type==="tableHead"?(u=!1,o!==0&&(Y0(p,e,o,c,s),s=void 0,o=0),c={type:"table",start:Object.assign({},y.start),end:Object.assign({},y.end)},p.add(n,0,[["enter",c,e]])):y.type==="tableRow"||y.type==="tableDelimiterRow"?(l=!0,f=void 0,r=[0,0,0,0],a=[0,n+1,0,0],u&&(u=!1,s={type:"tableBody",start:Object.assign({},y.start),end:Object.assign({},y.end)},p.add(n,0,[["enter",s,e]])),i=y.type==="tableDelimiterRow"?2:s?3:1):i&&(y.type==="data"||y.type==="tableDelimiterMarker"||y.type==="tableDelimiterFiller")?(l=!1,a[2]===0&&(r[1]!==0&&(a[0]=a[1],f=Bo(p,e,r,i,void 0,f),r=[0,0,0,0]),a[2]=n)):y.type==="tableCellDivider"&&(l?l=!1:(r[1]!==0&&(a[0]=a[1],f=Bo(p,e,r,i,void 0,f)),r=a,a=[r[1],n,0,0])):y.type==="tableHead"?(u=!0,o=n):y.type==="tableRow"||y.type==="tableDelimiterRow"?(o=n,r[1]!==0?(a[0]=a[1],f=Bo(p,e,r,i,n,f)):a[1]!==0&&(f=Bo(p,e,a,i,n,f)),i=0):i&&(y.type==="data"||y.type==="tableDelimiterMarker"||y.type==="tableDelimiterFiller")&&(a[3]=n)}for(o!==0&&Y0(p,e,o,c,s),p.consume(e.events),n=-1;++n<e.events.length;){let m=e.events[n];m[0]==="enter"&&m[1].type==="table"&&(m[1]._align=j0(e.events,n))}return t}function Bo(t,e,n,l,i,r){let a=l===1?"tableHeader":l===2?"tableDelimiter":"tableData",u="tableContent";n[0]!==0&&(r.end=Object.assign({},Ji(e.events,n[0])),t.add(n[0],0,[["exit",r,e]]));let o=Ji(e.events,n[1]);if(r={type:a,start:Object.assign({},o),end:Object.assign({},o)},t.add(n[1],0,[["enter",r,e]]),n[2]!==0){let c=Ji(e.events,n[2]),s=Ji(e.events,n[3]),f={type:u,start:Object.assign({},c),end:Object.assign({},s)};if(t.add(n[2],0,[["enter",f,e]]),l!==2){let p=e.events[n[2]],m=e.events[n[3]];if(p[1].end=Object.assign({},m[1].end),p[1].type="chunkText",p[1].contentType="text",n[3]>n[2]+1){let y=n[2]+1,v=n[3]-n[2]-1;t.add(y,v,[])}}t.add(n[3]+1,0,[["exit",f,e]])}return i!==void 0&&(r.end=Object.assign({},Ji(e.events,i)),t.add(i,0,[["exit",r,e]]),r=void 0),r}function Y0(t,e,n,l,i){let r=[],a=Ji(e.events,n);i&&(i.end=Object.assign({},a),r.push(["exit",i,e])),l.end=Object.assign({},a),r.push(["exit",l,e]),t.add(n+1,0,r)}function Ji(t,e){let n=t[e],l=n[0]==="enter"?"start":"end";return n[1][l]}var YT={name:"tasklistCheck",tokenize:GT};function np(){return{text:{91:YT}}}function GT(t,e,n){let l=this;return i;function i(o){return l.previous!==null||!l._gfmTasklistFirstContentOfListItem?n(o):(t.enter("taskListCheck"),t.enter("taskListCheckMarker"),t.consume(o),t.exit("taskListCheckMarker"),r)}function r(o){return W(o)?(t.enter("taskListCheckValueUnchecked"),t.consume(o),t.exit("taskListCheckValueUnchecked"),a):o===88||o===120?(t.enter("taskListCheckValueChecked"),t.consume(o),t.exit("taskListCheckValueChecked"),a):n(o)}function a(o){return o===93?(t.enter("taskListCheckMarker"),t.consume(o),t.exit("taskListCheckMarker"),t.exit("taskListCheck"),u):n(o)}function u(o){return _(o)?e(o):Y(o)?t.check({tokenize:VT},e,n)(o):n(o)}}function VT(t,e,n){return q(t,l,"whitespace");function l(i){return i===null?n(i):e(i)}}function G0(t){return co([Pm(),$m(),tp(t),ep(),np()])}var XT={};function Ho(t){let e=this,n=t||XT,l=e.data(),i=l.micromarkExtensions||(l.micromarkExtensions=[]),r=l.fromMarkdownExtensions||(l.fromMarkdownExtensions=[]),a=l.toMarkdownExtensions||(l.toMarkdownExtensions=[]);i.push(G0(n)),r.push(Km()),a.push(Im(n))}function V0(t){var e,n,l="";if(typeof t=="string"||typeof t=="number")l+=t;else if(typeof t=="object")if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(n=V0(t[e]))&&(l&&(l+=" "),l+=n)}else for(n in t)t[n]&&(l&&(l+=" "),l+=n);return l}function X0(){for(var t,e,n=0,l="",i=arguments.length;n<i;n++)(t=arguments[n])&&(e=V0(t))&&(l&&(l+=" "),l+=e);return l}var op="-",QT=t=>{let e=FT(t),{conflictingClassGroups:n,conflictingClassGroupModifiers:l}=t;return{getClassGroupId:a=>{let u=a.split(op);return u[0]===""&&u.length!==1&&u.shift(),I0(u,e)||ZT(a)},getConflictingClassGroupIds:(a,u)=>{let o=n[a]||[];return u&&l[a]?[...o,...l[a]]:o}}},I0=(t,e)=>{var a;if(t.length===0)return e.classGroupId;let n=t[0],l=e.nextPart.get(n),i=l?I0(t.slice(1),l):void 0;if(i)return i;if(e.validators.length===0)return;let r=t.join(op);return(a=e.validators.find(({validator:u})=>u(r)))==null?void 0:a.classGroupId},Q0=/^\[(.+)\]$/,ZT=t=>{if(Q0.test(t)){let e=Q0.exec(t)[1],n=e==null?void 0:e.substring(0,e.indexOf(":"));if(n)return"arbitrary.."+n}},FT=t=>{let{theme:e,classGroups:n}=t,l={nextPart:new Map,validators:[]};for(let i in n)rp(n[i],l,i,e);return l},rp=(t,e,n,l)=>{t.forEach(i=>{if(typeof i=="string"){let r=i===""?e:Z0(e,i);r.classGroupId=n;return}if(typeof i=="function"){if(KT(i)){rp(i(l),e,n,l);return}e.validators.push({validator:i,classGroupId:n});return}Object.entries(i).forEach(([r,a])=>{rp(a,Z0(e,r),n,l)})})},Z0=(t,e)=>{let n=t;return e.split(op).forEach(l=>{n.nextPart.has(l)||n.nextPart.set(l,{nextPart:new Map,validators:[]}),n=n.nextPart.get(l)}),n},KT=t=>t.isThemeGetter,IT=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,n=new Map,l=new Map,i=(r,a)=>{n.set(r,a),e++,e>t&&(e=0,l=n,n=new Map)};return{get(r){let a=n.get(r);if(a!==void 0)return a;if((a=l.get(r))!==void 0)return i(r,a),a},set(r,a){n.has(r)?n.set(r,a):i(r,a)}}},ap="!",up=":",JT=up.length,PT=t=>{let{prefix:e,experimentalParseClassName:n}=t,l=i=>{let r=[],a=0,u=0,o=0,c;for(let y=0;y<i.length;y++){let v=i[y];if(a===0&&u===0){if(v===up){r.push(i.slice(o,y)),o=y+JT;continue}if(v==="/"){c=y;continue}}v==="["?a++:v==="]"?a--:v==="("?u++:v===")"&&u--}let s=r.length===0?i:i.substring(o),f=WT(s),p=f!==s,m=c&&c>o?c-o:void 0;return{modifiers:r,hasImportantModifier:p,baseClassName:f,maybePostfixModifierPosition:m}};if(e){let i=e+up,r=l;l=a=>a.startsWith(i)?r(a.substring(i.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(n){let i=l;l=r=>n({className:r,parseClassName:i})}return l},WT=t=>t.endsWith(ap)?t.substring(0,t.length-1):t.startsWith(ap)?t.substring(1):t,$T=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(l=>[l,!0]));return l=>{if(l.length<=1)return l;let i=[],r=[];return l.forEach(a=>{a[0]==="["||e[a]?(i.push(...r.sort(),a),r=[]):r.push(a)}),i.push(...r.sort()),i}},tA=t=>M({cache:IT(t.cacheSize),parseClassName:PT(t),sortModifiers:$T(t)},QT(t)),eA=/\s+/,nA=(t,e)=>{let{parseClassName:n,getClassGroupId:l,getConflictingClassGroupIds:i,sortModifiers:r}=e,a=[],u=t.trim().split(eA),o="";for(let c=u.length-1;c>=0;c-=1){let s=u[c],{isExternal:f,modifiers:p,hasImportantModifier:m,baseClassName:y,maybePostfixModifierPosition:v}=n(s);if(f){o=s+(o.length>0?" "+o:o);continue}let E=!!v,h=l(E?y.substring(0,v):y);if(!h){if(!E){o=s+(o.length>0?" "+o:o);continue}if(h=l(y),!h){o=s+(o.length>0?" "+o:o);continue}E=!1}let d=r(p).join(":"),g=m?d+ap:d,k=g+h;if(a.includes(k))continue;a.push(k);let C=i(h,E);for(let w=0;w<C.length;++w){let A=C[w];a.push(g+A)}o=s+(o.length>0?" "+o:o)}return o};function lA(){let t=0,e,n,l="";for(;t<arguments.length;)(e=arguments[t++])&&(n=J0(e))&&(l&&(l+=" "),l+=n);return l}var J0=t=>{if(typeof t=="string")return t;let e,n="";for(let l=0;l<t.length;l++)t[l]&&(e=J0(t[l]))&&(n&&(n+=" "),n+=e);return n};function iA(t,...e){let n,l,i,r=a;function a(o){let c=e.reduce((s,f)=>f(s),t());return n=tA(c),l=n.cache.get,i=n.cache.set,r=u,u(o)}function u(o){let c=l(o);if(c)return c;let s=nA(o,n);return i(o,s),s}return function(){return r(lA.apply(null,arguments))}}var qt=t=>{let e=n=>n[t]||[];return e.isThemeGetter=!0,e},P0=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,W0=/^\((?:(\w[\w-]*):)?(.+)\)$/i,rA=/^\d+\/\d+$/,aA=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,uA=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,oA=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,cA=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,sA=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Pi=t=>rA.test(t),J=t=>!!t&&!Number.isNaN(Number(t)),sl=t=>!!t&&Number.isInteger(Number(t)),lp=t=>t.endsWith("%")&&J(t.slice(0,-1)),Mn=t=>aA.test(t),fA=()=>!0,mA=t=>uA.test(t)&&!oA.test(t),$0=()=>!1,pA=t=>cA.test(t),hA=t=>sA.test(t),dA=t=>!U(t)&&!B(t),gA=t=>Wi(t,nx,$0),U=t=>P0.test(t),Xl=t=>Wi(t,lx,mA),ip=t=>Wi(t,SA,J),F0=t=>Wi(t,tx,$0),yA=t=>Wi(t,ex,hA),qo=t=>Wi(t,ix,pA),B=t=>W0.test(t),Ta=t=>$i(t,lx),bA=t=>$i(t,kA),K0=t=>$i(t,tx),xA=t=>$i(t,nx),vA=t=>$i(t,ex),jo=t=>$i(t,ix,!0),Wi=(t,e,n)=>{let l=P0.exec(t);return l?l[1]?e(l[1]):n(l[2]):!1},$i=(t,e,n=!1)=>{let l=W0.exec(t);return l?l[1]?e(l[1]):n:!1},tx=t=>t==="position"||t==="percentage",ex=t=>t==="image"||t==="url",nx=t=>t==="length"||t==="size"||t==="bg-size",lx=t=>t==="length",SA=t=>t==="number",kA=t=>t==="family-name",ix=t=>t==="shadow";var wA=()=>{let t=qt("color"),e=qt("font"),n=qt("text"),l=qt("font-weight"),i=qt("tracking"),r=qt("leading"),a=qt("breakpoint"),u=qt("container"),o=qt("spacing"),c=qt("radius"),s=qt("shadow"),f=qt("inset-shadow"),p=qt("text-shadow"),m=qt("drop-shadow"),y=qt("blur"),v=qt("perspective"),E=qt("aspect"),h=qt("ease"),d=qt("animate"),g=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...k(),B,U],w=()=>["auto","hidden","clip","visible","scroll"],A=()=>["auto","contain","none"],T=()=>[B,U,o],N=()=>[Pi,"full","auto",...T()],S=()=>[sl,"none","subgrid",B,U],F=()=>["auto",{span:["full",sl,B,U]},sl,B,U],Q=()=>[sl,"auto",B,U],L=()=>["auto","min","max","fr",B,U],G=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],j=()=>["start","end","center","stretch","center-safe","end-safe"],P=()=>["auto",...T()],ct=()=>[Pi,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],H=()=>[t,B,U],Jt=()=>[...k(),K0,F0,{position:[B,U]}],x=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Zt=()=>["auto","cover","contain",xA,gA,{size:[B,U]}],ne=()=>[lp,Ta,Xl],b=()=>["","none","full",c,B,U],mt=()=>["",J,Ta,Xl],Ae=()=>["solid","dashed","dotted","double"],Ql=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],St=()=>[J,lp,K0,F0],Rt=()=>["","none",y,B,U],Ze=()=>["none",J,B,U],oe=()=>["none",J,B,U],qe=()=>[J,B,U],je=()=>[Pi,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Mn],breakpoint:[Mn],color:[fA],container:[Mn],"drop-shadow":[Mn],ease:["in","out","in-out"],font:[dA],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Mn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Mn],shadow:[Mn],spacing:["px",J],text:[Mn],"text-shadow":[Mn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Pi,U,B,E]}],container:["container"],columns:[{columns:[J,U,B,u]}],"break-after":[{"break-after":g()}],"break-before":[{"break-before":g()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:A()}],"overscroll-x":[{"overscroll-x":A()}],"overscroll-y":[{"overscroll-y":A()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:N()}],"inset-x":[{"inset-x":N()}],"inset-y":[{"inset-y":N()}],start:[{start:N()}],end:[{end:N()}],top:[{top:N()}],right:[{right:N()}],bottom:[{bottom:N()}],left:[{left:N()}],visibility:["visible","invisible","collapse"],z:[{z:[sl,"auto",B,U]}],basis:[{basis:[Pi,"full","auto",u,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[J,Pi,"auto","initial","none",U]}],grow:[{grow:["",J,B,U]}],shrink:[{shrink:["",J,B,U]}],order:[{order:[sl,"first","last","none",B,U]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:F()}],"col-start":[{"col-start":Q()}],"col-end":[{"col-end":Q()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:F()}],"row-start":[{"row-start":Q()}],"row-end":[{"row-end":Q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":L()}],"auto-rows":[{"auto-rows":L()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...G(),"normal"]}],"justify-items":[{"justify-items":[...j(),"normal"]}],"justify-self":[{"justify-self":["auto",...j()]}],"align-content":[{content:["normal",...G()]}],"align-items":[{items:[...j(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...j(),{baseline:["","last"]}]}],"place-content":[{"place-content":G()}],"place-items":[{"place-items":[...j(),"baseline"]}],"place-self":[{"place-self":["auto",...j()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:P()}],mx:[{mx:P()}],my:[{my:P()}],ms:[{ms:P()}],me:[{me:P()}],mt:[{mt:P()}],mr:[{mr:P()}],mb:[{mb:P()}],ml:[{ml:P()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:ct()}],w:[{w:[u,"screen",...ct()]}],"min-w":[{"min-w":[u,"screen","none",...ct()]}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[a]},...ct()]}],h:[{h:["screen",...ct()]}],"min-h":[{"min-h":["screen","none",...ct()]}],"max-h":[{"max-h":["screen",...ct()]}],"font-size":[{text:["base",n,Ta,Xl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[l,B,ip]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",lp,U]}],"font-family":[{font:[bA,U,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,B,U]}],"line-clamp":[{"line-clamp":[J,"none",B,ip]}],leading:[{leading:[r,...T()]}],"list-image":[{"list-image":["none",B,U]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",B,U]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:H()}],"text-color":[{text:H()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Ae(),"wavy"]}],"text-decoration-thickness":[{decoration:[J,"from-font","auto",B,Xl]}],"text-decoration-color":[{decoration:H()}],"underline-offset":[{"underline-offset":[J,"auto",B,U]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",B,U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",B,U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Jt()}],"bg-repeat":[{bg:x()}],"bg-size":[{bg:Zt()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},sl,B,U],radial:["",B,U],conic:[sl,B,U]},vA,yA]}],"bg-color":[{bg:H()}],"gradient-from-pos":[{from:ne()}],"gradient-via-pos":[{via:ne()}],"gradient-to-pos":[{to:ne()}],"gradient-from":[{from:H()}],"gradient-via":[{via:H()}],"gradient-to":[{to:H()}],rounded:[{rounded:b()}],"rounded-s":[{"rounded-s":b()}],"rounded-e":[{"rounded-e":b()}],"rounded-t":[{"rounded-t":b()}],"rounded-r":[{"rounded-r":b()}],"rounded-b":[{"rounded-b":b()}],"rounded-l":[{"rounded-l":b()}],"rounded-ss":[{"rounded-ss":b()}],"rounded-se":[{"rounded-se":b()}],"rounded-ee":[{"rounded-ee":b()}],"rounded-es":[{"rounded-es":b()}],"rounded-tl":[{"rounded-tl":b()}],"rounded-tr":[{"rounded-tr":b()}],"rounded-br":[{"rounded-br":b()}],"rounded-bl":[{"rounded-bl":b()}],"border-w":[{border:mt()}],"border-w-x":[{"border-x":mt()}],"border-w-y":[{"border-y":mt()}],"border-w-s":[{"border-s":mt()}],"border-w-e":[{"border-e":mt()}],"border-w-t":[{"border-t":mt()}],"border-w-r":[{"border-r":mt()}],"border-w-b":[{"border-b":mt()}],"border-w-l":[{"border-l":mt()}],"divide-x":[{"divide-x":mt()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":mt()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Ae(),"hidden","none"]}],"divide-style":[{divide:[...Ae(),"hidden","none"]}],"border-color":[{border:H()}],"border-color-x":[{"border-x":H()}],"border-color-y":[{"border-y":H()}],"border-color-s":[{"border-s":H()}],"border-color-e":[{"border-e":H()}],"border-color-t":[{"border-t":H()}],"border-color-r":[{"border-r":H()}],"border-color-b":[{"border-b":H()}],"border-color-l":[{"border-l":H()}],"divide-color":[{divide:H()}],"outline-style":[{outline:[...Ae(),"none","hidden"]}],"outline-offset":[{"outline-offset":[J,B,U]}],"outline-w":[{outline:["",J,Ta,Xl]}],"outline-color":[{outline:H()}],shadow:[{shadow:["","none",s,jo,qo]}],"shadow-color":[{shadow:H()}],"inset-shadow":[{"inset-shadow":["none",f,jo,qo]}],"inset-shadow-color":[{"inset-shadow":H()}],"ring-w":[{ring:mt()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:H()}],"ring-offset-w":[{"ring-offset":[J,Xl]}],"ring-offset-color":[{"ring-offset":H()}],"inset-ring-w":[{"inset-ring":mt()}],"inset-ring-color":[{"inset-ring":H()}],"text-shadow":[{"text-shadow":["none",p,jo,qo]}],"text-shadow-color":[{"text-shadow":H()}],opacity:[{opacity:[J,B,U]}],"mix-blend":[{"mix-blend":[...Ql(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Ql()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[J]}],"mask-image-linear-from-pos":[{"mask-linear-from":St()}],"mask-image-linear-to-pos":[{"mask-linear-to":St()}],"mask-image-linear-from-color":[{"mask-linear-from":H()}],"mask-image-linear-to-color":[{"mask-linear-to":H()}],"mask-image-t-from-pos":[{"mask-t-from":St()}],"mask-image-t-to-pos":[{"mask-t-to":St()}],"mask-image-t-from-color":[{"mask-t-from":H()}],"mask-image-t-to-color":[{"mask-t-to":H()}],"mask-image-r-from-pos":[{"mask-r-from":St()}],"mask-image-r-to-pos":[{"mask-r-to":St()}],"mask-image-r-from-color":[{"mask-r-from":H()}],"mask-image-r-to-color":[{"mask-r-to":H()}],"mask-image-b-from-pos":[{"mask-b-from":St()}],"mask-image-b-to-pos":[{"mask-b-to":St()}],"mask-image-b-from-color":[{"mask-b-from":H()}],"mask-image-b-to-color":[{"mask-b-to":H()}],"mask-image-l-from-pos":[{"mask-l-from":St()}],"mask-image-l-to-pos":[{"mask-l-to":St()}],"mask-image-l-from-color":[{"mask-l-from":H()}],"mask-image-l-to-color":[{"mask-l-to":H()}],"mask-image-x-from-pos":[{"mask-x-from":St()}],"mask-image-x-to-pos":[{"mask-x-to":St()}],"mask-image-x-from-color":[{"mask-x-from":H()}],"mask-image-x-to-color":[{"mask-x-to":H()}],"mask-image-y-from-pos":[{"mask-y-from":St()}],"mask-image-y-to-pos":[{"mask-y-to":St()}],"mask-image-y-from-color":[{"mask-y-from":H()}],"mask-image-y-to-color":[{"mask-y-to":H()}],"mask-image-radial":[{"mask-radial":[B,U]}],"mask-image-radial-from-pos":[{"mask-radial-from":St()}],"mask-image-radial-to-pos":[{"mask-radial-to":St()}],"mask-image-radial-from-color":[{"mask-radial-from":H()}],"mask-image-radial-to-color":[{"mask-radial-to":H()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":k()}],"mask-image-conic-pos":[{"mask-conic":[J]}],"mask-image-conic-from-pos":[{"mask-conic-from":St()}],"mask-image-conic-to-pos":[{"mask-conic-to":St()}],"mask-image-conic-from-color":[{"mask-conic-from":H()}],"mask-image-conic-to-color":[{"mask-conic-to":H()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Jt()}],"mask-repeat":[{mask:x()}],"mask-size":[{mask:Zt()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",B,U]}],filter:[{filter:["","none",B,U]}],blur:[{blur:Rt()}],brightness:[{brightness:[J,B,U]}],contrast:[{contrast:[J,B,U]}],"drop-shadow":[{"drop-shadow":["","none",m,jo,qo]}],"drop-shadow-color":[{"drop-shadow":H()}],grayscale:[{grayscale:["",J,B,U]}],"hue-rotate":[{"hue-rotate":[J,B,U]}],invert:[{invert:["",J,B,U]}],saturate:[{saturate:[J,B,U]}],sepia:[{sepia:["",J,B,U]}],"backdrop-filter":[{"backdrop-filter":["","none",B,U]}],"backdrop-blur":[{"backdrop-blur":Rt()}],"backdrop-brightness":[{"backdrop-brightness":[J,B,U]}],"backdrop-contrast":[{"backdrop-contrast":[J,B,U]}],"backdrop-grayscale":[{"backdrop-grayscale":["",J,B,U]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[J,B,U]}],"backdrop-invert":[{"backdrop-invert":["",J,B,U]}],"backdrop-opacity":[{"backdrop-opacity":[J,B,U]}],"backdrop-saturate":[{"backdrop-saturate":[J,B,U]}],"backdrop-sepia":[{"backdrop-sepia":["",J,B,U]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",B,U]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[J,"initial",B,U]}],ease:[{ease:["linear","initial",h,B,U]}],delay:[{delay:[J,B,U]}],animate:[{animate:["none",d,B,U]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[v,B,U]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:Ze()}],"rotate-x":[{"rotate-x":Ze()}],"rotate-y":[{"rotate-y":Ze()}],"rotate-z":[{"rotate-z":Ze()}],scale:[{scale:oe()}],"scale-x":[{"scale-x":oe()}],"scale-y":[{"scale-y":oe()}],"scale-z":[{"scale-z":oe()}],"scale-3d":["scale-3d"],skew:[{skew:qe()}],"skew-x":[{"skew-x":qe()}],"skew-y":[{"skew-y":qe()}],transform:[{transform:[B,U,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:je()}],"translate-x":[{"translate-x":je()}],"translate-y":[{"translate-y":je()}],"translate-z":[{"translate-z":je()}],"translate-none":["translate-none"],accent:[{accent:H()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:H()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",B,U]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",B,U]}],fill:[{fill:["none",...H()]}],"stroke-w":[{stroke:[J,Ta,Xl,ip]}],stroke:[{stroke:["none",...H()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}};var rx=iA(wA);function ax(...t){return rx(X0(t))}var ox=Fe(Yi());function ux(n){var l=n,{className:t}=l,e=Ca(l,["className"]);return(0,ox.jsx)("textarea",M({"data-slot":"textarea",className:ax("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t)},e))}var et=Fe(Yi()),jt={container:{position:"fixed",bottom:"20px",right:"20px",zIndex:2147483647,fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontSize:"14px",lineHeight:"1.5",color:"#333",pointerEvents:"auto"},containerLeft:{left:"20px",right:"auto"},bubble:{width:"60px",height:"60px",borderRadius:"50%",border:"none",cursor:"pointer",display:"flex",alignItems:"center",justifyContent:"center",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",transition:"all 0.3s ease",outline:"none",position:"relative"},bubbleHover:{transform:"scale(1.05)",boxShadow:"0 6px 16px rgba(0, 0, 0, 0.2)"},chatWindow:{position:"absolute",bottom:"70px",right:"0",width:"360px",height:"600px",maxHeight:"calc(100vh - 100px)",backgroundColor:"#ffffff",borderRadius:"12px",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.12)",border:"1px solid #e5e7eb",display:"flex",flexDirection:"column",overflow:"hidden",transform:"translateY(10px)",opacity:0,transition:"all 0.3s ease",pointerEvents:"auto"},chatWindowLeft:{left:"0",right:"auto"},chatWindowOpen:{transform:"translateY(0)",opacity:1},header:{padding:"16px 20px",borderBottom:"1px solid #e5e7eb",display:"flex",alignItems:"center",justifyContent:"space-between",backgroundColor:"#ffffff"},headerTitle:{margin:0,fontSize:"16px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontWeight:"600",color:"#111827"},closeButton:{background:"none",border:"none",cursor:"pointer",padding:"4px",borderRadius:"4px",color:"#6b7280",fontSize:"18px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',lineHeight:1,outline:"none"},messagesContainer:{flex:1,padding:"16px",overflowY:"auto",display:"flex",flexDirection:"column",gap:"12px"},message:{maxWidth:"80%",padding:"8px 12px",borderRadius:"12px",fontSize:"14px",lineHeight:"1.4"},userMessage:{alignSelf:"flex-end",backgroundColor:"#3b82f6",color:"#ffffff"},assistantMessage:{alignSelf:"flex-start",backgroundColor:"#f3f4f6",color:"#111827"},inputContainer:{padding:"16px",borderTop:"1px solid #e5e7eb",display:"flex",gap:"8px",backgroundColor:"#ffffff",color:"#111827 !important"},input:{flex:1,padding:"8px 12px",border:"1px solid #d1d5db",borderRadius:"8px",fontSize:"14px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',outline:"none",resize:"none",minHeight:"36px",maxHeight:"100px",backgroundColor:"#ffffff"},sendButton:{padding:"8px 16px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontFamily:'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',fontWeight:"500",outline:"none",transition:"all 0.2s ease"},sendButtonDisabled:{opacity:.5,cursor:"not-allowed"},markdownContent:{lineHeight:"1.6"},markdownH1:{fontSize:"18px",fontWeight:"700",margin:"16px 0 8px 0",color:"inherit"},markdownH2:{fontSize:"16px",fontWeight:"600",margin:"14px 0 6px 0",color:"inherit"},markdownH3:{fontSize:"15px",fontWeight:"600",margin:"12px 0 4px 0",color:"inherit"},markdownP:{margin:"8px 0",color:"inherit"},markdownUl:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},markdownOl:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},markdownLi:{margin:"4px 0",color:"inherit"},markdownCode:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"2px 4px",borderRadius:"3px",fontSize:"13px",fontFamily:'Monaco, Consolas, "Courier New", monospace',color:"inherit"},markdownPre:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:'Monaco, Consolas, "Courier New", monospace',color:"inherit"},markdownBlockquote:{borderLeft:"3px solid #ddd",paddingLeft:"12px",margin:"8px 0",fontStyle:"italic",color:"inherit"},markdownA:{color:"#3b82f6",textDecoration:"underline"},markdownStrong:{fontWeight:"600",color:"inherit"},markdownEm:{fontStyle:"italic",color:"inherit"}};function EA({content:t,primaryColor:e}){return(0,et.jsx)("div",{style:jt.markdownContent,children:(0,et.jsx)(xm,{remarkPlugins:[Ho],components:{h1:({children:n})=>(0,et.jsx)("h1",{style:{fontSize:"18px",fontWeight:700,margin:"16px 0 8px 0",color:"inherit"},children:n}),h2:({children:n})=>(0,et.jsx)("h2",{style:{fontSize:"16px",fontWeight:600,margin:"14px 0 6px 0",color:"inherit"},children:n}),h3:({children:n})=>(0,et.jsx)("h3",{style:{fontSize:"15px",fontWeight:600,margin:"12px 0 4px 0",color:"inherit"},children:n}),p:({children:n})=>(0,et.jsx)("p",{style:{margin:"8px 0",color:"inherit"},children:n}),ul:({children:n})=>(0,et.jsx)("ul",{style:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},children:n}),ol:({children:n})=>(0,et.jsx)("ol",{style:{margin:"8px 0",paddingLeft:"20px",color:"inherit"},children:n}),li:({children:n})=>(0,et.jsx)("li",{style:{margin:"4px 0",color:"inherit"},children:n}),code:({children:n,className:l})=>l?(0,et.jsx)("code",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit",display:"block"},children:n}):(0,et.jsx)("code",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"2px 4px",borderRadius:"3px",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit"},children:n}),pre:({children:n})=>(0,et.jsx)("pre",{style:{backgroundColor:"rgba(0, 0, 0, 0.1)",padding:"12px",borderRadius:"6px",overflow:"auto",margin:"8px 0",fontSize:"13px",fontFamily:"Monaco, Consolas, 'Courier New', monospace",color:"inherit"},children:n}),blockquote:({children:n})=>(0,et.jsx)("blockquote",{style:{borderLeft:"3px solid #ddd",paddingLeft:"12px",margin:"8px 0",fontStyle:"italic",color:"inherit"},children:n}),a:({children:n,href:l})=>(0,et.jsx)("a",{href:l,style:{color:e,textDecoration:"underline"},target:"_blank",rel:"noopener noreferrer",children:n}),strong:({children:n})=>(0,et.jsx)("strong",{style:{fontWeight:600,color:"inherit"},children:n}),em:({children:n})=>(0,et.jsx)("em",{style:{fontStyle:"italic",color:"inherit"},children:n})},children:t})})}function TA(t){let e=(a,u)=>{let o=a.replace("#",""),c,s,f;return o.length===3?(c=Number.parseInt(o[0]+o[0],16),s=Number.parseInt(o[1]+o[1],16),f=Number.parseInt(o[2]+o[2],16)):(c=Number.parseInt(o.slice(0,2),16),s=Number.parseInt(o.slice(2,4),16),f=Number.parseInt(o.slice(4,6),16)),`rgba(${c}, ${s}, ${f}, ${u})`},n=e(t,.08),l=e(t,.25),i=e(t,.4),r=e(t,.6);return`
		/* WebKit browsers (Chrome, Safari, Edge) */
		.chat-messages-container::-webkit-scrollbar {
			width: 6px;
			background: transparent;
		}

		.chat-messages-container::-webkit-scrollbar-track {
			background: ${n};
			border-radius: 3px;
			margin: 2px 0;
		}

		.chat-messages-container::-webkit-scrollbar-thumb {
			background: ${l};
			border-radius: 3px;
			transition: all 0.2s ease;
			min-height: 20px;
		}

		.chat-messages-container::-webkit-scrollbar-thumb:hover {
			background: ${i};
		}

		.chat-messages-container::-webkit-scrollbar-thumb:active {
			background: ${r};
		}

		/* Hide scrollbar when not needed */
		.chat-messages-container::-webkit-scrollbar-corner {
			background: transparent;
		}

		/* Firefox */
		.chat-messages-container {
			scrollbar-width: thin;
			scrollbar-color: ${l} ${n};
		}

		/* Ensure smooth scrolling and proper behavior */
		.chat-messages-container {
			scroll-behavior: smooth;
			overflow-x: hidden;
			overflow-y: auto;
		}

		/* Hide scrollbar on mobile devices where it's not needed */
		@media (max-width: 640px) {
			.chat-messages-container::-webkit-scrollbar {
				width: 4px;
			}
		}

		/* Ensure scrollbar doesn't interfere with content */
		.chat-messages-container {
			scrollbar-gutter: stable;
		}

		/* Add subtle fade effect for better visual integration */
		.chat-messages-container::-webkit-scrollbar-thumb {
			box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
		}

		/* Improve accessibility - ensure scrollbar is visible for keyboard navigation */
		.chat-messages-container:focus-within::-webkit-scrollbar-thumb {
			background: ${i};
		}
	`}function AA({config:t,shadowRoot:e}){let[n,l]=(0,de.useState)(t.initiallyOpen||!1),[i,r]=(0,de.useState)([]),[a,u]=(0,de.useState)(""),[o,c]=(0,de.useState)(!1),[s,f]=(0,de.useState)(!1),p=(0,de.useRef)(null),m=t.primaryColor||"#3b82f6",y=t.secondaryColor||"#ffffff",v=t.position||"bottom-right",E=A=>{let T=A.replace("#",""),N=Number.parseInt(T.substring(0,2),16),S=Number.parseInt(T.substring(2,4),16),F=Number.parseInt(T.substring(4,6),16);return(N*299+S*587+F*114)/1e3>128?"#111827":"#ffffff"},h=E(y),d=E("#f3f4f6");(0,de.useEffect)(()=>{if(e){let A=e.querySelector("#chat-scrollbar-styles");A&&A.remove();let T=document.createElement("style");T.id="chat-scrollbar-styles",T.textContent=TA(m),e.appendChild(T)}},[e,m]),(0,de.useEffect)(()=>{p.current&&p.current.scrollIntoView({behavior:"smooth"})},[i]),(0,de.useEffect)(()=>{n&&i.length===0&&t.welcomeMessage&&r([{id:"welcome",content:t.welcomeMessage,role:"assistant",timestamp:new Date}])},[n,i.length,t.welcomeMessage]);let g=(0,de.useCallback)(()=>nr(this,null,function*(){var S;if(!a.trim()||o)return;let A={id:Date.now().toString(),content:a.trim(),role:"user",timestamp:new Date};r(F=>[...F,A]),u(""),c(!0);let T=(Date.now()+1).toString(),N={id:T,content:"",role:"assistant",timestamp:new Date};r(F=>[...F,N]);try{let F=yield fetch(`${t.baseUrl}/api/chat/${t.websiteId}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...i,A].map(j=>({role:j.role,content:j.content})),websiteId:t.websiteId,visitorId:t.visitorId})});if(!F.ok)throw new Error("Failed to send message");let Q=(S=F.body)==null?void 0:S.getReader(),L=new TextDecoder,G="";if(Q)for(;;){let{done:j,value:P}=yield Q.read();if(j)break;let H=L.decode(P,{stream:!0}).split(`
`);for(let Jt of H)if(Jt.startsWith("0:")){let x=Jt.slice(2);G+=x,o&&x.trim()&&c(!1);let Zt=G.replace(/\\n/g,`
`).replace(/\\r\\n/g,`
`).replace(/\\r/g,`
`).replace(/([a-zA-Z])""/g,"$1").replace(/""([a-zA-Z])/g,"$1").replace(/""([!?.,;:])/g,"$1").replace(/([!?.,;:])""/g,"$1").replace(/""'/g,"'").replace(/'""/g,"'").replace(/"""/g,'"').replace(/""/g,"").replace(/\\"/g,'"').replace(/\\""/g,'"').replace(/"{2,}/g,'"').replace(/"([^"]*)""/g,'"$1"').replace(/""([^"]*)""/g,'"$1"').replace(/\\\\"/g,'"').replace(/"\s*"/g,'"').replace(/([a-zA-Z])"([a-zA-Z])/g,"$1'$2").replace(/\s+"/g,' "').replace(/"\s+/g,'" ').trim().replace(/^["']+(.*)["']+$/,"$1").replace(/^["']\s*([\s\S]*?)\s*["']$/,"$1").replace(/^["']+\s*/,"").replace(/\s*["']+$/,"").trim();r(ne=>ne.map(b=>b.id===T?wt(M({},b),{content:Zt}):b))}}G||r(j=>j.map(P=>P.id===T?wt(M({},P),{content:"Sorry, I encountered an error."}):P))}catch(F){console.error("Error sending message:",F),r(Q=>Q.map(L=>L.id===T?wt(M({},L),{content:"Sorry, I encountered an error. Please try again."}):L))}finally{c(!1)}}),[a,o,i,t]),k=A=>{A.key==="Enter"&&!A.shiftKey&&(A.preventDefault(),g())},C=()=>{l(!n)},w=()=>{l(!1)};return(0,et.jsxs)("div",{style:M(M({},jt.container),v==="bottom-left"?jt.containerLeft:{}),children:[n&&(0,et.jsxs)("div",{style:M(M(M({},jt.chatWindow),v==="bottom-left"?jt.chatWindowLeft:{}),jt.chatWindowOpen),children:[(0,et.jsxs)("div",{style:wt(M({},jt.header),{backgroundColor:y}),children:[(0,et.jsx)("h3",{style:wt(M({},jt.headerTitle),{color:h}),children:t.headerText||"Chat Assistant"}),(0,et.jsx)("button",{type:"button",style:wt(M({},jt.closeButton),{color:h}),onClick:w,"aria-label":"Close chat",children:"\xD7"})]}),(0,et.jsxs)("div",{className:"chat-messages-container",style:jt.messagesContainer,children:[i.map(A=>(0,et.jsx)("div",{style:M(M(M({},jt.message),A.role==="user"?jt.userMessage:wt(M({},jt.assistantMessage),{color:d})),A.role==="user"?{backgroundColor:m}:{}),children:A.role==="assistant"?A.content.trim()===""&&o?(0,et.jsx)("div",{style:{color:d,fontStyle:"italic"},children:"Thinking..."}):(0,et.jsx)(EA,{content:A.content,primaryColor:m}):A.content},A.id)),(0,et.jsx)("div",{ref:p})]}),(0,et.jsxs)("div",{style:wt(M({},jt.inputContainer),{backgroundColor:y,color:h}),children:[(0,et.jsx)(ux,{style:wt(M({},jt.input),{backgroundColor:y,color:h}),value:a,onChange:A=>u(A.target.value),onKeyDown:k,placeholder:"Type your message...",rows:1}),(0,et.jsx)("button",{type:"button",style:M(wt(M({},jt.sendButton),{backgroundColor:m,color:"#ffffff"}),!a.trim()||o?jt.sendButtonDisabled:{}),onClick:g,disabled:!a.trim()||o,children:"Send"})]})]}),(0,et.jsx)("button",{type:"button",style:M(wt(M({},jt.bubble),{backgroundColor:m}),s?jt.bubbleHover:{}),onClick:C,onMouseEnter:()=>f(!0),onMouseLeave:()=>f(!1),"aria-label":n?"Close chat":"Open chat",children:(0,et.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",style:{color:"#ffffff"},children:[(0,et.jsx)("title",{children:n?"Close chat":"Open chat"}),n?(0,et.jsx)("path",{d:"M18 6L6 18M6 6l12 12"}):(0,et.jsx)("path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"})]})})]})}function sx({shadowRoot:t,config:e}){let n=document.createElement("div");t.appendChild(n);let l=(0,cx.createRoot)(n);return l.render((0,et.jsx)(AA,{config:e,shadowRoot:t})),{open(){},close(){},toggle(){},destroy(){l.unmount(),n.parentNode&&n.parentNode.removeChild(n)}}}window.BublWidgetV2={init:sx};})();
/*! Bundled license information:

react/cjs/react.production.js:
  (**
   * @license React
   * react.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

scheduler/cjs/scheduler.production.js:
  (**
   * @license React
   * scheduler.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom.production.js:
  (**
   * @license React
   * react-dom.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom-client.production.js:
  (**
   * @license React
   * react-dom-client.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react/cjs/react-jsx-runtime.production.js:
  (**
   * @license React
   * react-jsx-runtime.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
